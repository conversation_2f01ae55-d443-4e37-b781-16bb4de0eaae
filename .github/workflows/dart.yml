# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: Build Family Android app.

on:
  push:
    branches:
      - 'auto_build_*'

env:
  BASE_URL: ${{ secrets.BASE_URL }}
  API_KEY: ${{ secrets.API_KEY }}
  SOCKET_URL: ${{ secrets.SOCKET_URL }}
  HOLIDAY_API_KEY: ${{ secrets.HOLIDAY_API_KEY }}
  GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v4
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: 3.27.1
      - name: Setup Flutter Fire
        run: |
          npm install -g firebase-tools
          dart pub global activate flutterfire_cli

      - name: Create .env file
        run: |
          echo "API_KEY=${{ secrets.API_KEY }}" >> .env
          echo "BASE_URL=${{ secrets.BASE_URL }}" >> .env
          echo "BASE_URL=${{ secrets.SOCKET_URL }}" >> .env
          echo "HOLIDAY_API_KEY=${{ secrets.HOLIDAY_API_KEY }}" >> .env
          echo "GOOGLE_MAPS_API_KEY=${{ secrets.GOOGLE_MAPS_API_KEY }}" >> .env
      - run: flutter clean && flutter pub get
      - run: flutter pub run easy_localization:generate -O lib/config/lang -f keys -o locale_keys.g.dart --source-dir ./assets/lang
      - run: flutter pub run build_runner build
      - run: flutter build apk
      - name: Upload APK for later distribution
        uses: actions/upload-artifact@v4
        with:
          name: Android-Apk
          path: build/app/outputs/flutter-apk/app-release.apk
          retention-days: 1

      - run: flutter build appbundle
      - name: Upload App bundle for later distribution
        uses: actions/upload-artifact@v4
        with:
          name: Android-App-Bundle
          path: build/app/outputs/bundle/release/app-release.aab
          retention-days: 1
