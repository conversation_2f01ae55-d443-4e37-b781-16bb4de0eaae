# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/

# JetBrains IDE
.idea/

# VSCode
.vscode

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

temp
.dart_tool

build

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
mapbox_access_token.xml

# Flutter generated files
*.g.dart
*.gen.dart
*.config.dart
*.freezed.dart
*.mocks.dart
*.gr.dart

# Used by dotenv library to load environment variables.
.env
.env.*
.android.config
.ios.config
.envfile

.flutter-plugins-dependencies
.flutter-plugins

pubspec.lock

# FVM Version Cache
.fvm/
/.fvmrc
.sdkmanrc
