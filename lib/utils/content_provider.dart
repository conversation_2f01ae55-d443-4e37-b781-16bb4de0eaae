import 'dart:convert';

import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

abstract class ContentProvider {
  Future<String> fetchImageUrl(String placeName);
  Future<String> fetchLocationDetails(String placeName);
}

class GooglePlacesProvider implements ContentProvider {
  final String apiKey;
  final String proxyUrl;

  GooglePlacesProvider(this.apiKey, this.proxyUrl);

  @override
  Future<String> fetchImageUrl(String placeNameRaw) async {
    // logd("Search for : $placeNameRaw");

    String url;

    if (kIsWeb) {
      // for testing on web, use a proxy,
      String encodeWholeUrl = Uri.encodeComponent(
          'https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=$placeNameRaw&inputtype=textquery&fields=photos&key=$apiKey');

      url = '$proxyUrl?url=$encodeWholeUrl';
    } else {
      String placeName = Uri.encodeComponent(placeNameRaw);
      url =
          'https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=$placeName&inputtype=textquery&fields=photos&key=$apiKey';
      // logd("search url: $url");
    }

    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      try {
        //print('response.body: ${response.body}');
        final data = jsonDecode(response.body);

        if (data['candidates'].isNotEmpty && data['candidates'][0].isNotEmpty) {
          final photoReference = data['candidates'][0]['photos'][0]['photo_reference'];

          String img_url =
              'https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&photoreference=$photoReference&key=$apiKey';
          String finalUrl = img_url;
          if (kIsWeb) {
            img_url = Uri.encodeComponent(img_url);
            finalUrl = '$proxyUrl?url=$img_url';
          }

          return finalUrl;
        } else {
          print("No photo found for $placeNameRaw");
        }
      } catch (e) {
        print("error decoding json from url response: $e");
        print("Error Decodeing Json from url response : $url");
        logd("response.body: ${response.body}");
      }
    }
    return '';
  }

  @override
  Future<String> fetchLocationDetails(String placeName) async {
    final url =
        '$proxyUrl?url=https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=$placeName&inputtype=textquery&fields=name,formatted_address,geometry&key=$apiKey';
    final response = await http.get(Uri.parse(url));
    final responseBody = response.body;

    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      if (data['candidates'].isNotEmpty) {
        return jsonEncode(data['candidates'][0]);
      }
    }
    return '';
  }
}

class FoursquareProvider implements ContentProvider {
  final String clientId;
  final String clientSecret;

  FoursquareProvider(this.clientId, this.clientSecret);

  @override
  Future<String> fetchImageUrl(String placeName) async {
    final url =
        'https://api.foursquare.com/v2/venues/search?query=$placeName&client_id=$clientId&client_secret=$clientSecret&v=20211001';
    final response = await http.get(Uri.parse(url));
    final responseBody = response.body;

    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      if (data['response']['venues'].isNotEmpty) {
        final venueId = data['response']['venues'][0]['id'];
        final photoUrl = await _fetchVenuePhoto(venueId);
        return photoUrl;
      }
    }
    return '';
  }

  Future<String> _fetchVenuePhoto(String venueId) async {
    final url =
        'https://api.foursquare.com/v2/venues/$venueId/photos?client_id=$clientId&client_secret=$clientSecret&v=20211001';
    final response = await http.get(Uri.parse(url));
    final responseBody = response.body;

    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      if (data['response']['photos']['items'].isNotEmpty) {
        final photo = data['response']['photos']['items'][0];
        return '${photo['prefix']}original${photo['suffix']}';
      }
    }
    return '';
  }

  @override
  Future<String> fetchLocationDetails(String placeName) async {
    final url =
        'https://api.foursquare.com/v2/venues/search?query=$placeName&client_id=$clientId&client_secret=$clientSecret&v=20211001';
    final response = await http.get(Uri.parse(url));
    final responseBody = response.body;

    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      if (data['response']['venues'].isNotEmpty) {
        return jsonEncode(data['response']['venues'][0]);
      }
    }
    return '';
  }
}

class TripAdvisorProvider implements ContentProvider {
  final String apiKey;

  TripAdvisorProvider(this.apiKey);

  @override
  Future<String> fetchImageUrl(String placeName) async {
    final url = 'https://api.tripadvisor.com/api/partner/2.0/location/search?query=$placeName&key=$apiKey';
    final response = await http.get(Uri.parse(url));
    final responseBody = response.body;

    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      if (data['data'].isNotEmpty) {
        final photoUrl = data['data'][0]['photo']['images']['large']['url'];
        return photoUrl;
      }
    }
    return '';
  }

  @override
  Future<String> fetchLocationDetails(String placeName) async {
    final url = 'https://api.tripadvisor.com/api/partner/2.0/location/search?query=$placeName&key=$apiKey';
    final response = await http.get(Uri.parse(url));
    final responseBody = response.body;

    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      if (data['data'].isNotEmpty) {
        return jsonEncode(data['data'][0]);
      }
    }
    return '';
  }
}

class OpenStreetMapProvider implements ContentProvider {
  @override
  Future<String> fetchImageUrl(String placeName) async {
    // OpenStreetMap does not provide images directly, so we return an empty string
    return '';
  }

  @override
  Future<String> fetchLocationDetails(String placeName) async {
    final url = 'https://nominatim.openstreetmap.org/search?q=$placeName&format=json&addressdetails=1';
    final response = await http.get(Uri.parse(url));
    final responseBody = response.body;

    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      if (data.isNotEmpty) {
        return jsonEncode(data[0]);
      }
    }
    return '';
  }
}

ContentProvider provider = TripPlaceImageSearchSingleton()._defaultProvider;

class TripPlaceImageSearchSingleton {
  static final TripPlaceImageSearchSingleton _instance = TripPlaceImageSearchSingleton._internal();
  final ContentProvider _defaultProvider;

  factory TripPlaceImageSearchSingleton({ContentProvider? provider}) {
    return _instance;
  }

  static const apiKey = 'AIzaSyCNGovjxS_jPnR97Liv6LPFjRWxqLpPmto';
  static const proxyUrl = 'https://vrelay-vn1.5gencare.com/proxy/';

  TripPlaceImageSearchSingleton._internal() : _defaultProvider = GooglePlacesProvider(apiKey, proxyUrl);

  Future<String> fetchImageUrl(String placeName) {
    return _defaultProvider.fetchImageUrl(placeName);
  }

  Future<String> fetchLocationDetails(String placeName) {
    return _defaultProvider.fetchLocationDetails(placeName);
  }
}
