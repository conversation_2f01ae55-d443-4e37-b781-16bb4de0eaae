import 'dart:io';

import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';

//create a shorthand function call
logd(String message, {String? tag}) => AppLogger.d(tag != null ? "[$tag] $message" : message);
logi(String message) => AppLogger.i(message);
logw(String message) => AppLogger.w(message);
loge(String message, [Exception? error, StackTrace? stackTrace]) => AppLogger.e(message, error, stackTrace);

class AppLogger {
  static const bool _DEBUG = false;

  // static final _logger = Logger(
  //     printer: FullLogPrinter(
  //   SimplePrinter(),
  //   dateTimeFormat: DateTimeFormat.dateAndTime,
  // ));

  static final _logger = _DEBUG
      ? Logger(
          printer: FullLogPrinter(
          SimplePrinter(),
          dateTimeFormat: DateTimeFormat.dateAndTime,
        ))
      : Logger(
          printer: PrettyPrinter(methodCount: 0, printTime: true, printEmojis: false),
          // xxx: output: MultiOutput(multiOutput),
          filter: PermissiveFilter());

  // static final _devLogger = Logger(
  //   printer: PrettyPrinter(), // Development environment
  // );
  // static final _prodLogger = Logger(
  //   printer: SimplePrinter(), // Production environment
  // );

  static void d(String message) => _logger.d(message);
  static void i(String message) => _logger.i(message);
  static void w(String message) => _logger.w(message);
  static void e(String message, [Exception? error, StackTrace? stackTrace]) =>
      _logger.e(message, error: error, stackTrace: stackTrace);

// TODO: enable this later to log to file
  // //To be called in UI controller later.
  // Future<void> initFile() async {
  //   await getDirectoryForLogRecord();
  //   List<LogOutput> multiOutput = [OriFileOutput(file: mFile), ConsoleOutput()];

  //   //new logger log to multi output
  //   _logger = Logger(
  //       printer: PrettyPrinter(methodCount: 0, printTime: true, printEmojis: false),
  //       output: MultiOutput(multiOutput),
  //       filter: PermissiveFilter());

  //   print("Logger (multi output) is ready");
  // }

  //init to a dummy file-- will be changed later when called getDirectoryForLogRecord
  var mFile = File('/tmp/dummy.log');
  File get logFile => mFile;

  final String _logFileName = 'application.log';

  Future<void> getDirectoryForLogRecord() async {
    final Directory directory = await getApplicationDocumentsDirectory();
    mFile = File('${directory.path}/$_logFileName');
  }
}

class FullLogPrinter extends PrettyPrinter {
  final LogPrinter _realPrinter;

  FullLogPrinter(this._realPrinter, {dateTimeFormat = DateTimeFormat.none}) : super(dateTimeFormat: dateTimeFormat);

  @override
  List<String> log(LogEvent event) {
    const int chunkSize = 900; // Maximum chunk size (adjust as needed)
    final message = event.message.toString();

    var timestamp = '';
    if (printTime) {
      timestamp = getTime(DateTime.now());
    }

    // Split the message into chunks
    final chunks = <String>[];
    for (var i = 0; i < message.length; i += chunkSize) {
      chunks.add(message.substring(i, i + chunkSize > message.length ? message.length : i + chunkSize));
    }

    // Process each chunk through the real printer for formatting
    final formattedChunks = chunks
        .expand((chunk) => _realPrinter
            .log(LogEvent(event.level, '[$timestamp] $chunk ', error: event.error, stackTrace: event.stackTrace)))
        .toList();

    return formattedChunks;
  }
}

class PermissiveFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    // TODO: implement shouldLog
    return true;
  }
}

//create an ouputfile to store the logs
class OriFileOutput extends LogOutput {
  OriFileOutput({required File file}) : _file = file;

  File _file;

  var _counter = 1000;
  final int _maxFileSize = 3 * 1024 * 1024;

  @override
  void output(OutputEvent event) {
    //check the file size every 1000 lines
    if (_counter-- < 0) {
      _counter = 1000;
      _rotateLogIfNeeded();
    }

    for (var line in event.lines) {
      _file.writeAsStringSync("${line.toString()}\n", mode: FileMode.writeOnlyAppend);
    }
  }

  void _rotateLogIfNeeded() {
    try {
      if (_file.lengthSync() > _maxFileSize) {
        final directory = _file.parent;
        final originalFileName = _file.uri.pathSegments.last;
        final backupFileName = '${originalFileName}_old.log';
        final backupPath = '${directory.path}/$backupFileName';
        final backupFile = File(backupPath);

        if (backupFile.existsSync()) {
          backupFile.deleteSync();
        }

        _file.renameSync(backupPath);
        _file = File('${directory.path}/$originalFileName');
      }
    } catch (e) {
      print('Error rotating log file: $e');
    }
  }
}
