import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/usecase/sign_out_usecase.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/primary_outline_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class DialogUtils {
  static Future<void> showDialogView(BuildContext context, Widget child) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: appTheme.whiteText,
          insetPadding: padding(horizontal: 32),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: Padding(padding: padding(horizontal: 24, vertical: 20), child: child),
        );
      },
    );
  }

  static Future<bool?> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    required String confirmText,
    required String cancelText,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title, style: AppStyle.medium16()),
          content: Text(content, style: AppStyle.regular14()),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText, style: AppStyle.regular16()),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(confirmText, style: AppStyle.regular16(color: appTheme.primaryColor)),
            ),
          ],
        );
      },
    );
  }

  static Future<void> showCupertinoModal(
    BuildContext context,
    List<CupertinoActionModel> actions,
  ) {
    return showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return CupertinoActionSheet(
          actions: actions
              .map((action) => CupertinoActionSheetAction(
                  onPressed: () {
                    context.maybePop();
                    Future.delayed(const Duration(milliseconds: 50)).then((_) => action.onTap?.call());
                  },
                  child: Text(action.title, style: AppStyle.regular18(color: appTheme.primaryColor))))
              .toList(),
          cancelButton: CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(LocaleKeys.cancel.tr(), style: AppStyle.regular16()),
          ),
        );
      },
    );
  }

  static Future<void> showDeleteDialog(
    BuildContext context, {
    String title = '',
    String content = '',
    String confirmText = '',
    VoidCallback? onConfirm,
  }) {
    return showDialogView(
        context,
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(title, style: AppStyle.medium16()),
            const SizedBox(height: 12),
            Text(content, style: AppStyle.regular14()),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: PrimaryButton(
                    buttonPadding: padding(vertical: 8),
                    onTap: context.maybePop,
                    text: LocaleKeys.cancel.tr(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: PrimaryButton(
                    buttonPadding: padding(vertical: 8),
                    backgroundColor: appTheme.redColor,
                    onTap: onConfirm,
                    text: confirmText,
                  ),
                ),
              ],
            )
          ],
        ));
  }

  static Future<void> showSignOutDialog(BuildContext context) {
    return showDeleteDialog(
      context,
      title: LocaleKeys.sign_out_title.tr(),
      content: LocaleKeys.sign_out_content.tr(),
      confirmText: LocaleKeys.sign_out_title.tr(),
      onConfirm: () async {
        showLoading();
        try {
          await locator.get<SignOutUsecase>().call();
          context.router.replaceAll([const AuthRoute()]);
        } catch (e) {
          if (kDebugMode) {
            showSimpleToast(e.toString());
          }
        }
        dismissLoading();
      },
    );
  }

  static Future<void> showDeleteDialogV2(
      BuildContext context, {
        String content = '',
        String confirmText = '',
        VoidCallback? onConfirm,
      }) {
    return showDialogView(
        context,
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(content,
                textAlign: TextAlign.center,
                style: AppStyle.regular16V2()),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: PrimaryOutlineButton(
                    buttonPadding: paddingV2(vertical: 8, horizontal: 4),
                    textColor: appTheme.primaryColorV2,
                    borderColor: appTheme.primaryColorV2,
                    onTap: context.maybePop,
                    text: LocaleKeys.cancel.tr(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: PrimaryButtonV2(
                    // buttonPadding: padding(vertical: 8),
                    borderRadius: 1000,
                    padding: paddingV2(vertical: 8, horizontal: 4),
                    bg: appTheme.errorV2,
                    onTap: onConfirm,
                    text: confirmText,
                  ),
                ),
              ],
            )
          ],
        ));
  }
}

class CupertinoActionModel {
  final VoidCallback? onTap;
  final String title;

  CupertinoActionModel({this.onTap, required this.title});
}
