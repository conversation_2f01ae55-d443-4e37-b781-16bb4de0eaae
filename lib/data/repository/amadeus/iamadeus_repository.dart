import 'package:family_app/data/model/amadeus/authorization.dart';
import 'package:family_app/data/model/amadeus/city.dart';
import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/amadeus/hotel_offer_model.dart';
import 'package:family_app/data/model/amadeus/hotel_rating.dart';
import 'package:family_app/data/usecase/model/booking_hotel_param.dart';

import '../ibase_repository.dart';

const List<String> AmadeusSupportAmenities = [
  "SWIMMING_POOL",
  "SPA",
  "FITNESS_CENTER",
  "AIR_CONDITIONING",
  "RESTAURANT",
  "PARKING",
  "PETS_ALLOWED",
  "AI<PERSON>ORT_SHUTTLE",
  "BUSINESS_CENTER",
  "DISABLED_FACILITIES",
  "WIFI",
  "MEETING_ROOMS",
  "NO_KID_ALLOWED",
  "TENNIS",
  "GO<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON>IM<PERSON>_WATCHING",
  "BABY-SITTING",
  "BEA<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON>UN<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "MA<PERSON>AGE",
  "VALET_PARKING",
  "BAR or LOUNGE",
  "KIDS_WELCOME",
  "NO_PORN_FILMS",
  "MINIBAR",
  "TELEVISION",
  "WI-FI_IN_ROOM",
  "ROOM_SERVICE",
  "GUARDED_PARKG",
  "SERV_SPEC_MENU"
];

abstract class IAmadeusRepository extends IBaseRepository {
  Future<AmadeusAuth> oauth2();

  Future<List<AmadeusCity>> getCityByName(String keyword, String countryCode);

  Future<List<AmadeusHotelModel>> getHotelByCity(String cityCode,
      {List<int>? ratings, List<String>? amenities});

  Future<List<AmadeusHotelModel>> getHotelByIds(String hotelIds);

  Future<List<AMAHotelRatingModel>> getHotelRating(String hotelIds);

  Future<AmadeusHotelOfferResponse?> getHotelOffers(
    String hotelIds,
    int adults, {
    String? checkInDate,
    String? checkOutDate,
    int? roomQuantity,
    String? rateCode,
    String? currencyCode,
  });

  Future<dynamic> bookHotel(BookingHotelParam param);
}
