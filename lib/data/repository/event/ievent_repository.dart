import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/ibase_repository.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';

abstract class IEventRepository extends IBaseRepository {
  Future<EventModels> createEvent(EventParameter parameter);
  Future<EventModels> updateEvent(String id, EventParameter parameter);
  Future<List<EventModels>> getAllEventByActivity(String activityId);
  Future<List<EventModels>> getEventInFamily(String familyId, {DateTime? from, DateTime? to, String search = ''});
  Future<bool> deleteEvent(String id);
  Future<EventModels?> getEventById(String id);
}
