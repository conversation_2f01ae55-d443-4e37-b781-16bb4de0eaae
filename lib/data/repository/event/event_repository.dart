import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/remote/event_api.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: IEventRepository)
class EventRepository extends IEventRepository {
  final EventAPI eventAPI;

  EventRepository({required this.eventAPI});

  @override
  Future<EventModels> createEvent(EventParameter parameter) async {
    try {
      final result = await eventAPI.createEvent(parameter.toJson());
      return result.parse(EventModels.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<EventModels>> getAllEventByActivity(String activityId) async {
    try {
      final result = await eventAPI.getAllEventByActivity(activityId);
      return result.parseList(EventModels.fromJson);
    } catch (e) {
      return [];
    }
  }

  @override
  Future<EventModels> updateEvent(String id, EventParameter parameter) async {
    try {
      final result = await eventAPI.updateEvent(id, parameter.toJson());
      return result.parse(EventModels.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<EventModels>> getEventInFamily(
    String familyId, {
    DateTime? from,
    DateTime? to,
    String search = '',
  }) async {
    try {
      final queries = <String, dynamic>{};
      if (from != null) {
        queries.putIfAbsent('from', () => from.toUtc().toIso8601String());
      }
      if (to != null) {
        queries.putIfAbsent('to', () => to.toUtc().toIso8601String());
      }
      if (search.isNotEmpty) {
        queries.putIfAbsent('name', () => search);
      }

      if (from == null && to == null) {
        queries.putIfAbsent('order_by', () => 'from_date');
        queries.putIfAbsent('ordering', () => 'asc');
      }

      final result = await eventAPI.getAllEventInFamily(familyId, queries);
      return result.parseList(EventModels.fromJson);
    } catch (e) {
      return <EventModels>[];
    }
  }

  @override
  Future<bool> deleteEvent(String id) async {
    try {
      await eventAPI.deleteEvent(id);
      return true;
    } catch (e) {
      // handleError(e);
      return false;
    }
  }

  @override
  Future<EventModels?> getEventById(String id) async {
    try {
      final result = await eventAPI.getEventById(id, {});
      return result.parse(EventModels.fromJson);
    } catch (e) {
      handleError(e);
      return null;
      // return false;
    }
  }
}
