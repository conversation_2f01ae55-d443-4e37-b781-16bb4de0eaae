import 'package:dio/dio.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/remote/authentication_api.dart';
import 'package:family_app/data/repository/authen/authen_exception.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:family_app/data/repository/authen/model/sign_up_parameter.dart';
import 'package:family_app/data/usecase/apple_sign_in_usecase.dart';
import 'package:family_app/data/usecase/google_sign_in_usecase.dart';
import 'package:family_app/data/usecase/sign_in_usecase.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/foundation.dart';
// import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:injectable/injectable.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

@LazySingleton(as: IAuthenRepository)
class AuthenRepository extends IAuthenRepository {
  final AuthenticationAPI authenticationAPI;
  final GoogleSignIn googleSignIn = GoogleSignIn(
    scopes: AppConfig.SCOPES,
  );

  AuthenRepository({required this.authenticationAPI});

  @override
  Future<Account> login(SignInParameter parameter) async {
    try {
      final result = await authenticationAPI.login(parameter.toJson());
      return result.parse(Account.fromJson);
    } catch (e) {
      handleLoginError(e);
      rethrow;
    }
  }

  @override
  Future<Account> loginWithGoogle() async {
    try {
      final googleAccount = await googleSignIn.signIn();
      if (googleAccount == null) {
        throw Exception("Google Sign-In cancelled");
      }
      final email = googleAccount.email;
      if (email.isEmpty) {
        throw Exception("Google Sign-In did not return a valid email");
      }
      final googleAuth = await googleAccount.authentication;
      final idToken = googleAuth.idToken;
      if (idToken == null || idToken.isEmpty) {
        throw Exception("Failed to retrieve Google access token");
      }
      final parameter = GoogleOauthParameter(idToken: idToken);
      final result = await authenticationAPI.loginWithGoogle(parameter.toJson());
      return result.parse(Account.fromJson);
    } catch (e) {
      handleLoginError(e);
      signOutWithGoogleIfSignedIn();
      rethrow;
    }
  }

  @override
  Future<void> signOutWithGoogle() async {
    if (await googleSignIn.isSignedIn()) {
      await googleSignIn.disconnect(); // revokes access
    }
    await googleSignIn.signOut(); // clear local session
  }

  // Helper to safely sign out only if user is signed in
  Future<void> signOutWithGoogleIfSignedIn() async {
    if (await googleSignIn.isSignedIn()) {
      await signOutWithGoogle();
    }
  }

  @override
  Future<Account> loginWithApple() async {
    try {
      final AuthorizationCredentialAppleID credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: 'com.gencare.family',
          redirectUri: Uri.parse(
            'https://fl-smarthome.firebaseapp.com/__/auth/handler',
          ),
        ),
      );
      final idToken = credential.identityToken;
      if (idToken == null || idToken.isEmpty) {
        throw Exception("Failed to retrieve Apple id token");
      }
      final parameter = AppleOauthParameter(
        idToken: idToken,
        givenName: credential.givenName,
        familyName: credential.familyName,
        email: credential.email,
        authorizationCode: credential.authorizationCode,
        state: credential.state,
      );
      final result = await authenticationAPI.loginWithApple(parameter.toJson());
      return result.parse(Account.fromJson);
    } catch (e) {
      handleLoginError(e);
      rethrow;
    }
  }

  @override
  Future<Account> loginWithFacebook() async {
    try {
      // final LoginResult result = await FacebookAuth.instance.login();

      // AppLogger.d('credential: $result');

      return {} as Account;
    } catch (e) {
      handleLoginError(e);
      rethrow;
    }
  }

  void handleLoginError(Object e) {
    if (e is DioException) {
      if (e.response?.statusCode == 400) {
        final responseData = e.response!.data;
        if (responseData is Map) {
          final dataData = responseData['data'];
          if (dataData is Map && dataData['status'] == 'WAITING_ACTIVATE') {
            throw EmailNotActiveError(dataData['uuid']);
          }
          final dataMessage = responseData['message'];
          if (dataMessage == 'account_error' || dataMessage == 'wrong_password') {
            throw InvalidUserNameOrPassword();
          }
        }
      }
    }
    handleError(e);
  }

  @override
  Future<void> logout() async {
    try {
      final result = await authenticationAPI.logout();

      return result.data;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<Account?> signUp(SignUpParameter parameter) async {
    try {
      final result = await authenticationAPI.signUp(parameter.toMap());

      if (result.data is Map && result.data['status'] == 0) throw EmailNotActiveError(result.data['uuid']);

      return result.parse(Account.fromJson);
    } catch (e) {
      if (e is DioException) {
        if (e.response?.statusCode == 400) throw EmailNotAvailableError();
      }
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<void> verifyOTP(String email, String otp) async {
    try {
      final result = await authenticationAPI.sendOTP({'email': email, 'code': otp});

      return result.data;
    } catch (e) {
      if (e is DioException) {
        if (e.response?.statusCode == 400) {
          throw VerifyCodeError();
        }
      }
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<bool> verifyEmail(String uuid) async {
    try {
      final result = await authenticationAPI.verifyEmail({'uuid': uuid});

      return result.data == 'ACTIVATED';
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<void> forgotPass(String email) async {
    try {
      await authenticationAPI.forgotPass({'email': email});
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }
}
