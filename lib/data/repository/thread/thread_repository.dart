import 'dart:convert';

import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/data/remote/thread_api.dart';
import 'package:family_app/data/repository/thread/ithread_repository.dart';
import 'package:family_app/data/repository/thread/model/message_parameter.dart';
import 'package:family_app/data/repository/thread/model/thread_parameter.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: IThreadRepository)
class ThreadRepository extends IThreadRepository {

  final ThreadAPI threadAPI;

  ThreadRepository({required this.threadAPI});

  @override
  Future<List<ThreadFamily>> getAllThread(String familyId) async {
    try {
      final result = await threadAPI.getAllThread(familyId);
      return result.parseList(ThreadFamily.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<ThreadFamily> createThread(ThreadParameter parameter) async {
    try {
      final result = await threadAPI.createThread(parameter.toJson());
      return result.parse(ThreadFamily.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<ThreadFamily> getThreadDetail(String threadId) async {
    try {
      final result = await threadAPI.getThreadDetail(threadId);
      return result.parse(ThreadFamily.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<ThreadMessage>> getThreadMessage(String familyId) async {
    try {
      final result = await threadAPI.getThreadMessage(familyId);
      return result.parseList(ThreadMessage.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<ThreadMessage> createThreadMessage(MessageParameter parameter, String threadId) async {
    try {
      final result = await threadAPI.createThreadMessage(parameter.toJson(), threadId);
      return result.parse(ThreadMessage.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<ThreadFamily> updateMemberThread(List<String> members, String threadId) async {
    try {
      Map<String, dynamic> body = {
        "members": members
      };
      final result = await threadAPI.updateMemberThread(body, threadId);
      return result.parse(ThreadFamily.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }




}