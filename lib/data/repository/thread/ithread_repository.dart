import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/data/model/thread_message.dart';

import '../ibase_repository.dart';
import 'model/message_parameter.dart';
import 'model/thread_parameter.dart';

abstract class IThreadRepository extends IBaseRepository {

  Future<List<ThreadFamily>> getAllThread(String familyId);

  Future<ThreadFamily> createThread(ThreadParameter parameter);

  Future<ThreadFamily> getThreadDetail(String threadId);

  Future<List<ThreadMessage>> getThreadMessage(String familyId);

  Future<ThreadMessage> createThreadMessage(MessageParameter parameter, String threadId);

  Future<ThreadFamily> updateMemberThread( List<String> members,String threadId);



}