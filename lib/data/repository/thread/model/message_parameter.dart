import 'package:json_annotation/json_annotation.dart';

part 'message_parameter.g.dart';

@JsonSerializable()
class MessageParameter {
  @Json<PERSON>ey(name: 'message')
  String? message;

  @Json<PERSON>ey(name: 'message_type')
  String? messageType;

  @J<PERSON><PERSON>ey(name: 'message_type_id')
  String? messageTypeId;


  MessageParameter({
    this.message,
    this.messageType,
    this.messageTypeId
  });

  factory MessageParameter.fromJson(Map<String, dynamic> json) => _$MessageParameterFromJson(json);

  Map<String, dynamic> toJson() => _$MessageParameterToJson(this);


}