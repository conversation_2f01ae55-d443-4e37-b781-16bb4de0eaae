import 'package:dio/dio.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_log_item.dart';
import 'package:family_app/data/remote/activity_api.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/usecase/model/activity_item_parameter.dart';
import 'package:family_app/data/usecase/model/activity_parameter.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:injectable/injectable.dart';
import 'package:family_app/utils/log/app_logger.dart';
@LazySingleton(as: IActivityRepository)
class ActivityRepository extends IActivityRepository {
  final ActivityAPI activityAPI;

  ActivityRepository({required this.activityAPI});

  @override
  Future<List<ActivityModel>> getUserActivities() async {
    try {
      final queries = <String, dynamic>{};
      queries.putIfAbsent('order_by', () => 'created_at');
      queries.putIfAbsent('ordering', () => 'desc');
      final result = await activityAPI.getUserActivity(queries);
      return result.parseList(ActivityModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<ActivityModel>> getFamilyActivities(String id, {int limit = 10, int offset = 0}) async {
    try {
      final queries = <String, dynamic>{};

      queries.putIfAbsent('order_by', () => 'created_at');
      queries.putIfAbsent('ordering', () => 'desc');
      queries.putIfAbsent('limit', () => limit);

      final result = await activityAPI.getAllActivity(id, queries);

      logd("Family activities: id=$id, queries=$queries");
      //log the result
      logd("Family activities: $result");



      return result.parseList(ActivityModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<ActivityModel> createActivity(CreateActivityParameter parameter) async {
    try {
      final result = await activityAPI.createActivity(parameter.toJson());
      return result.parse(ActivityModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<ActivityModel> updateActivity(String id, CreateActivityParameter parameter) async {
    try {
      final result = await activityAPI.updateActivity(id, parameter.toJson());
      return result.parse(ActivityModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<ActivityModel> getActivityById(String id) async {
    try {
      final result = await activityAPI.getActivityById(id, {});
      return result.parse(ActivityModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<ActivityModel>> getAllActivities(String familyId, {String? name}) async {
    try {
      final queries = <String, dynamic>{};
      queries.putIfAbsent('order_by', () => 'created_at');
      queries.putIfAbsent('ordering', () => 'desc');
      if(name != null) {
        queries.putIfAbsent('name', () => name);
      }

      final result = await activityAPI.getAllActivity(familyId, queries);
      return result.parseList(ActivityModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<bool> deleteActivity(String id) async {
    try {
      final result = await activityAPI.deleteActivity(id);
      return result.code == 200;
    } catch (e) {
      handleError(e);
      // rethrow;
    }
    return false;
  }

  @override
  Future<Item?> createItemInActivity(UpsertItemParam param) async {
    try {
      final formData = FormData.fromMap(param.toUpsertActivityItemParam());
      final result = await activityAPI.createItemInActivity(formData);
      return result.parse(Item.fromJson);
    } catch (w) {
      handleError(w);
    }
    return null;
  }
  @override
  Future<List<Item>?> getAllItemInActivity(ActivityItemParameter param) async {
    try {
      final result = await activityAPI.getAllItemInActivity(param.toJson());
      return result.parseList(Item.fromJson);
    } catch (w) {
      handleError(w);
    }
    return null;
  }

  @override
  Future<bool> deleteItemInActivity(String id) async {
    try {
      if (id.isEmpty) return false;
      await activityAPI.deleteItemInActivity(id);
      return true;
    } catch (w) {
      handleError(w);
    }
    return false;
  }

  @override
  Future<Item?> updateItemInActivity(String id, UpsertItemParam param) async {
    try {
      final result = await activityAPI.updateItemInActivity(id, param.toJson());
      return result.parse(Item.fromJson);
    } catch (w) {
      handleError(w);
      return null;
    }
  }

  // @override
  // Future<List<ListItem>> getActivityByFamilyId(String familyId) async {
  //   try {
  //     final result = await activityAPI.getActivityByFamily(familyId);
  //     return result.parseList(ListItem.fromJson);
  //   } catch (e) {
  //     handleError(e);
  //     return [];
  //   }
  // }

  @override
  Future<List<ListLog>> getAllItemLogInActivity(String activityId) async {
    try {
      final result = await activityAPI.getAllItemLogInActivity(activityId);
      return result.parseList(ListLog.fromJson);
    } catch (e) {
      handleError(e);
      return [];
    }
  }

  @override
  Future<ActivityModel> updateActivityItinerary(String id, CreateActivityParameter parameter) async {
    try {
      final result = await activityAPI.updateActivityItinerary(id, parameter.toJson());
      return result.parse(ActivityModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }
}
