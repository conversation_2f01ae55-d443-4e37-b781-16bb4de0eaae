// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:family_app/screen/main/calendar/calendar_cubit.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class EventUsecase extends BaseUseCase<EventModels, EventParameter> {
  final IEventRepository eventRepository;

  EventUsecase({required this.eventRepository});

  @override
  Future<EventModels> call(EventParameter param) async {
    try {
      late EventModels? result;

      if (param.id.isNotEmpty) {
        result = await eventRepository.updateEvent(param.id, param);
      } else {
        result = await eventRepository.createEvent(param);
      }
      if (locator.isRegistered<CalendarCubit>()) {
        locator.get<CalendarCubit>().onUpsertEvent(result, param.id.isNotEmpty);
      }
      return result;
    } catch (e) {
      rethrow;
    }
  }
}
