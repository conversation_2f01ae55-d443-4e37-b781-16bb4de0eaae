import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/socket/base_socket_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class SignOutUsecase extends BaseNormalUseCase {
  final IAuthenRepository authenRepository;
  final LocalStorage localStorage;
  final BaseSocketService socketService;
  final AccountService accountService;

  SignOutUsecase({
    required this.authenRepository,
    required this.localStorage,
    required this.socketService,
    required this.accountService,
  });

  @override
  Future<void> call() async {
    try {
      await authenRepository.signOutWithGoogle();
      await authenRepository.logout();
      accountService.logout();
      socketService.onDisconnect();
      localStorage.clear();
    } catch (e) {
      rethrow;
    }
  }
}
