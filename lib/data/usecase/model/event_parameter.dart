import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/repeat_config_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'event_parameter.g.dart';

@JsonSerializable()
class EventParameter {
  @Json<PERSON>ey(includeFromJson: false, includeToJson: false)
  final String id;
  final String name;
  @Json<PERSON>ey(name: 'from_date')
  final String fromDate;
  @Json<PERSON>ey(name: 'to_date')
  final String toDate;
  final String color;
  final String caption;
  final String description;
  @Json<PERSON><PERSON>(name: 'family_id')
  final String familyId;
  @Json<PERSON>ey(name: 'activity_id')
  final String activityId;
  @Json<PERSON><PERSON>(name: 'notification_status')
  final String notificationStatus;
  @J<PERSON><PERSON><PERSON>(name: 'notification_time')
  final String notificationTime;

  @Json<PERSON>ey(name: 'members')
  final List<String> members;

  @Json<PERSON>ey(name: 'recurrennce')
  String? repeat;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'all_day', defaultValue: 0)
  int allDay;

  EventParameter({
    this.id = '',
    required this.name,
    required this.fromDate,
    required this.toDate,
    required this.color,
    required this.caption,
    required this.description,
    required this.familyId,
    required this.activityId,
    required this.notificationStatus,
    required this.notificationTime,
    this.members = const [],
    this.allDay = 0,
    this.repeat
  });

  factory EventParameter.fromJson(Map<String, dynamic> json) => _$EventParameterFromJson(json);

  Map<String, dynamic> toJson() => _$EventParameterToJson(this);

  EventParameter copyWith({
    String? id,
    String? name,
    String? fromDate,
    String? toDate,
    String? color,
    String? caption,
    String? description,
    String? familyId,
    String? activityId,
    String? notificationStatus,
    String? notificationTime,
    List<String>? members,
  }) {
    return EventParameter(
      id: id ?? this.id,
      name: name ?? this.name,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      color: color ?? this.color,
      caption: caption ?? this.caption,
      description: description ?? this.description,
      familyId: familyId ?? this.familyId,
      activityId: activityId ?? this.activityId,
      notificationStatus: notificationStatus ?? this.notificationStatus,
      notificationTime: notificationTime ?? this.notificationTime,
      members: members ?? this.members,
    );
  }

  static RepeatConfig? _repeatTypeFromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return null;
    }
    return RepeatConfig.fromJson(json);
  }

  static Map<String, dynamic>? _repeatTypeToJson(RepeatConfig? repeat) {
    if (repeat == null) {
      return null;
    }
    return repeat.toJson();
  }


}
