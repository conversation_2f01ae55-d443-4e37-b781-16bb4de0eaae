import 'package:family_app/utils/content_provider.dart';
import 'package:json_annotation/json_annotation.dart';

abstract class HotelModel {
  String? name;
  String? imageURL;

  String? city, country;

  String? get id => null;

  set id(String? value) {
    // This setter is intentionally left empty as HotelModel does not have an id.
  }

  double? overallRating;


  HotelModel({this.name, this.imageURL});

  Future<String> fetchImage() async {
    String searchString = "hotel $name, $city, $country";
    imageURL = await provider.fetchImageUrl(searchString);
    return imageURL ?? "";
  }

  Future<double> fetchRating() async {
    return 0.0;
  }
}

abstract class HotelOfferModel {
  String? id;
  String? checkInDate;
  String? checkOutDate;
  String? imageURL;

  @JsonKey(includeFromJson: false, includeToJson: false)
  int quantity = 0;

  double get price => 0; // Placeholder for price, should be overridden in subclasses
  String? get currency => "USD"; // Placeholder for currency, should be overridden in subclasses
  String? get description => null; // Placeholder for description, should be overridden in subclasses

  int get adults => 1;
  int get children => 0;
  String? get beds => null;


  HotelOfferModel({this.id, this.checkInDate, this.checkOutDate});

  Future<String> fetchRoomImage(String roomSearch) async {
    imageURL = await provider.fetchImageUrl(roomSearch);
    return imageURL ?? "";
  }
}
