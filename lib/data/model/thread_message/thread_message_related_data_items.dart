import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'thread_message_related_data_items.g.dart';

@JsonSerializable()
class ThreadMessageRelatedDataItem {
  final String name;
  final String uuid;

  ThreadMessageRelatedDataItem({
    required this.name,
    required this.uuid,
  });

  factory ThreadMessageRelatedDataItem.fromJson(Map<String, dynamic> json) =>
      _$ThreadMessageRelatedDataItemFromJson(json);

  Map<String, dynamic> toJson() => _$ThreadMessageRelatedDataItemToJson(this);
}

@JsonSerializable()
class ThreadMessageRelatedDataItems {
  final List<ThreadMessageRelatedDataItem> items;

  ThreadMessageRelatedDataItems({required this.items});

  factory ThreadMessageRelatedDataItems.fromJson(String itemsJson) {
    if (itemsJson.isEmpty) {
      return ThreadMessageRelatedDataItems(items: []);
    }

    try {
      // Decode the JSON string into a list of maps
      final List<dynamic> decoded = jsonDecode(itemsJson);

      // Convert the list of maps into a list of ThreadMessageRelatedDataItem objects
      final List<ThreadMessageRelatedDataItem> items = decoded
          .map((item) => ThreadMessageRelatedDataItem.fromJson(item))
          .toList();

      return ThreadMessageRelatedDataItems(items: items);
    } catch (e) {
      // Return an empty list if decoding fails
      return ThreadMessageRelatedDataItems(items: []);
    }
  }

  Map<String, dynamic> toJson() {
    final List<Map<String, dynamic>> result =
        items.map((item) => item.toJson()).toList();
    return {'items': result};
  }
}
