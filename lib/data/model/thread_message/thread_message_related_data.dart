import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

import '../account.dart';
import 'thread_message_related_data_items.dart';
import 'thread_message_related_data_summary.dart';
import 'thread_message_related_data_poll_details.dart';

part 'thread_message_related_data.g.dart';

@JsonSerializable()
class ThreadMessageRelatedData {
  @Json<PERSON><PERSON>(name: '_')
  int? underscore;
  String? uuid;
  int? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  String? userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'thread_id')
  String threadId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_id')
  String messageId;
  String name;
  @Json<PERSON><PERSON>(name: 'from_date')
  String? fromDate;
  @J<PERSON><PERSON><PERSON>(name: 'to_date')
  String? toDate;
  dynamic items;
  dynamic summary;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'poll_details')
  dynamic pollDetails;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'extra_data')
  dynamic extraData;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  String? updatedAt;
  @JsonK<PERSON>(name: 'deleted_at')
  String? deletedAt;

  ThreadMessageRelatedData({
    this.underscore,
    this.uuid,
    this.status,
    this.createdAt,
    this.deletedAt,
    this.extraData,
    this.fromDate,
    required this.items,
    required this.messageId,
    required this.name,
    required this.threadId,
    this.summary,
    this.pollDetails,
    this.toDate,
    this.updatedAt,
    this.userId,
  });

  factory ThreadMessageRelatedData.fromJson(Map<String, dynamic> json) => _$ThreadMessageRelatedDataFromJson(json);

  Map<String, dynamic> toJson() => _$ThreadMessageRelatedDataToJson(this);

  /// Helper method to decode the `items` field
  ThreadMessageRelatedDataItems getDecodedItems() {
    return ThreadMessageRelatedDataItems.fromJson(items);
  }

  /// Helper method to decode the `summary` field
  ThreadMessageRelatedDataSummary getDecodedSummary() {
    return ThreadMessageRelatedDataSummary.fromJson(summary);
  }

  /// Method to find the item with the best vote
  MapEntry<String, int>? getItemWithBestTotalCountVote() {
    final decodedSummary = getDecodedSummary();

    // Return a default MapEntry if the votes map is empty
    if (decodedSummary.votes.isEmpty) {
      return const MapEntry<String, int>('', 0); // Default object with value = 0
    }

    // Find the item with the maximum vote count
    return decodedSummary.votes.entries.reduce((a, b) => a.value > b.value ? a : b);
  }

  /// Helper method to get the item info with the best total count vote
  ThreadMessageRelatedDataItem? getItemWithBestTotalCountVoteInfo() {
    final bestVoteEntry = getItemWithBestTotalCountVote();

    // If no best vote entry is found or no votes exist, return null
    if (bestVoteEntry == null || bestVoteEntry.key.isEmpty || bestVoteEntry.value == 0) {
      return null;
    }

    // Decode the items
    final decodedItems = getDecodedItems();

    // Find the first item with the matching UUID
    for (final item in decodedItems.items) {
      if (item.uuid == bestVoteEntry.key) {
        return item;
      }
    }

    // Return null if no matching item is found
    return null;
  }

  /// Helper method to decode the `pollDetails` field
  ThreadMessageRelatedDataPollDetails getDecodedPollDetails() {
    return ThreadMessageRelatedDataPollDetails.fromJson(pollDetails);
  }

  /// Helper method to check if a user has voted in the poll
  bool hasUserVoted(String? userUuid) {
    if (userUuid == null || userUuid.isEmpty) {
      return false; // User UUID is not provided
    }

    try {
      final pollDetails = getDecodedPollDetails();
      return pollDetails.voters.containsKey(userUuid);
    } catch (e) {
      // Handle decoding errors
      return false;
    }
  }

  /// Helper method to get the vote count for a specific item UUID
  int getVoteCountByItemUuid(String itemUuid) {
    final summary = getDecodedSummary();
    return summary.getVoteCountByItemUuid(itemUuid);
  }

  /// Helper method to get voters' details (including photo URLs) for each item
  Map<String, List<Account>> getVotersWithPhotoUrls(List<Account> accounts) {
    final voters = getDecodedPollDetails();
    return voters.getVotersWithPhotoUrls(accounts);
  }

  /// Helper method to get options with their voters
  List<ItemWithVoters> getItemsWithVoters(List<Account> accounts) {
    final decodedItems = getDecodedItems();
    final votersWithPhotoUrls = getVotersWithPhotoUrls(accounts);

    // Combine each item with its voters
    return decodedItems.items.map((item) {
      final itemUuid = item.uuid;
      final voters = votersWithPhotoUrls[itemUuid] ?? [];

      return ItemWithVoters(
        uuid: itemUuid,
        name: item.name,
        voters: voters,
      );
    }).toList();
  }

  /// Helper method to parse `toDate` into a DateTime object
  DateTime getEndDate() {
    if (toDate == null || toDate!.isEmpty) {
      return DateTime.now();
    }
    final formatter = DateFormat("yyyy-MM-dd HH:mm:ss");
    return formatter.parse(toDate!);
  }

  /// Method to check if the poll has ended
  bool isEnded() {
    if (fromDate == null || toDate == null) return false;

    final endDate = getEndDate();
    final now = DateTime.now();
    return now.isAfter(endDate);
  }
}

class ItemWithVoters {
  final String? uuid;
  final String name;
  final List<Account>? voters;

  ItemWithVoters({
    required this.name,
    this.uuid,
    this.voters,
  });

  @override
  String toString() {
    return 'ItemWithVoters(uuid: $uuid, name: $name, voters: ${voters?.map((v) => v.fullName).toList()})';
  }
}
