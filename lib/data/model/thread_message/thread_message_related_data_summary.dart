import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'thread_message_related_data_summary.g.dart';

@JsonSerializable()
class ThreadMessageRelatedDataSummary {
  final Map<String, int> votes;
  final int total;

  ThreadMessageRelatedDataSummary({
    required this.votes,
    required this.total,
  });

  factory ThreadMessageRelatedDataSummary.fromJson(String? summaryJson) {
    if (summaryJson == null || summaryJson.isEmpty) {
      return ThreadMessageRelatedDataSummary(votes: {}, total: 0);
    }

    try {
      // Decode the JSON string into a map
      final Map<String, dynamic> decoded = jsonDecode(summaryJson);

      // Extract the 'total' key
      final int total = decoded['total'];

      // Remove the 'total' key from the map
      decoded.remove('total');

      // Convert the remaining map to Map<String, int>
      final Map<String, int> votes = Map<String, int>.from(decoded);

      return ThreadMessageRelatedDataSummary(votes: votes, total: total);
    } catch (e) {
      // Return default values if decoding fails
      return ThreadMessageRelatedDataSummary(votes: {}, total: 0);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> result = Map.from(votes);
    result['total'] = total;
    return result;
  }

  /// Helper method to get the vote count for a specific item UUID
  int getVoteCountByItemUuid(String itemUuid) {
    return votes[itemUuid] ?? 0; // Return 0 if the itemUuid is not found
  }
}
