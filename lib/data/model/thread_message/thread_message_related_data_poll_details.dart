import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

import '../account.dart';

part 'thread_message_related_data_poll_details.g.dart';

@JsonSerializable()
class ThreadMessageRelatedDataPollDetails {
  /// A map of voter UUIDs to lists of item UUIDs they voted for
  final Map<String, List<String>> voters;

  ThreadMessageRelatedDataPollDetails({
    required this.voters,
  });

  factory ThreadMessageRelatedDataPollDetails.fromJson(String? votersJson) {
    if (votersJson == null || votersJson.isEmpty) {
      return ThreadMessageRelatedDataPollDetails(voters: {});
    }

    try {
      // Decode the JSON string into a map
      final Map<String, dynamic> decoded = jsonDecode(votersJson);

      // Convert the map values to lists of strings
      final Map<String, List<String>> voters = {};
      decoded.forEach((key, value) {
        voters[key] = List<String>.from(value);
      });

      return ThreadMessageRelatedDataPollDetails(voters: voters);
    } catch (e) {
      // Return an empty map if decoding fails
      return ThreadMessageRelatedDataPollDetails(voters: {});
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> result = {};
    voters.forEach((key, value) {
      result[key] = value;
    });
    return result;
  }

  /// Helper method to get voters' details (including photo URLs) for each item
  Map<String, List<Account>> getVotersWithPhotoUrls(List<Account> accounts) {
    final Map<String, List<Account>> result = {};

    // Iterate through the voters map
    voters.forEach((voterUuid, itemUuids) {
      // Find the account corresponding to the voter UUID
      Account? resultAccount;
      for (final account in accounts) {
        if (account.familyMemberUuid == voterUuid) {
          resultAccount = account;
          break;
        }
      }

      if (resultAccount != null) {
        // Add the account to the result for each item they voted for
        for (final itemUuid in itemUuids) {
          result.putIfAbsent(itemUuid, () => []).add(resultAccount);
        }
      }
    });

    return result;
  }
}
