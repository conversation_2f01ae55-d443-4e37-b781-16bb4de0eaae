// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/repeat_config_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'event.g.dart';

enum EventRepeatType {
  none,
  daily,
  weekly,
  monthly,
  yearly,
  custom
}

extension EventRepeatTypeExt on EventRepeatType {
  String get name {
    switch (this) {
      case EventRepeatType.none:
        return 'none';
      case EventRepeatType.daily:
        return 'daily';
      case EventRepeatType.weekly:
        return 'weekly';
      case EventRepeatType.monthly:
        return 'monthly';
      case EventRepeatType.yearly:
        return 'yearly';
      case EventRepeatType.custom:
        return 'custom';
    }
  }

  String get displayName {
    switch (this) {
      case EventRepeatType.none:
        return LocaleKeys.none.tr();
      case EventRepeatType.daily:
        return LocaleKeys.every_day.tr();
      case EventRepeatType.weekly:
        return LocaleKeys.every_week.tr();
      case EventRepeatType.monthly:
        return LocaleKeys.every_month.tr();
      case EventRepeatType.yearly:
        return LocaleKeys.every_year.tr();
      case EventRepeatType.custom:
        return LocaleKeys.custom.tr();
    }
  }

  static EventRepeatType fromString(String? value) {
    if (value == null) {
      return EventRepeatType.none;
    }
    return EventRepeatType.values.firstWhere(
      (e) => e.name == value,
      orElse: () => EventRepeatType.none,
    );
  }
}



@JsonSerializable()
class EventModels {
  String? uuid;
  int? status;

  @JsonKey(name: 'created_at')
  String? createdAt;

  @JsonKey(name: 'created_by')
  String? createdBy;

  @JsonKey(name: 'activity_id')
  String? activityId;

  @JsonKey(name: 'list_id')
  String? listId;

  @JsonKey(name: 'family_id')
  String? familyId;

  String? name;
  String? description;

  @JsonKey(name: 'from_date_utc')
  String? fromDate;

  @JsonKey(name: 'to_date_utc')
  String? toDate;

  String? color;

  @JsonKey(name: 'notification_status')
  String? notificationStatus;

  @JsonKey(name: 'notification_time_utc')
  String? notificationTime;

  @JsonKey(includeFromJson: false, includeToJson: false)
  ActivityModel? activity;

  @JsonKey(includeFromJson: false)
  List<String>? memberIds;

  @JsonKey(includeToJson: false, fromJson: parseIncludeMember)
  List<Account>? members;

  @JsonKey(name: 'recurrennce', fromJson: _repeatTypeFromJson, toJson: _repeatTypeToJson)
  RepeatConfig? repeat;

  @JsonKey(name: 'all_day', defaultValue: 0)
  int allDay = 0;


  EventModels({
    this.uuid,
    this.status,
    this.createdAt,
    this.createdBy,
    this.activityId,
    this.listId,
    this.familyId,
    this.name,
    this.description,
    this.fromDate,
    this.toDate,
    this.color,
    this.notificationStatus,
    this.notificationTime,
    this.activity,
    this.memberIds,
    this.members,
    this.repeat,
    this.allDay = 0,
  });

  factory EventModels.fromJson(Map<String, dynamic> json) => _$EventModelsFromJson(json);

  Map<String, dynamic> toJson() => _$EventModelsToJson(this);

  EventModels copyWith({
    String? uuid,
    int? status,
    String? createdAt,
    String? createdBy,
    String? activityId,
    String? listId,
    String? familyId,
    String? name,
    String? description,
    String? fromDate,
    String? toDate,
    String? color,
    String? notificationStatus,
    String? notificationTime,
    ActivityModel? activity,
    List<String>? memberIds,
    List<Account>? members,
  }) {
    return EventModels(
      uuid: uuid ?? this.uuid,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      activityId: activityId ?? this.activityId,
      listId: listId ?? this.listId,
      familyId: familyId ?? this.familyId,
      name: name ?? this.name,
      description: description ?? this.description,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      color: color ?? this.color,
      notificationStatus: notificationStatus ?? this.notificationStatus,
      notificationTime: notificationTime ?? this.notificationTime,
      activity: activity ?? this.activity,
      memberIds: memberIds ?? this.memberIds,
      members: members ?? this.members,
    );
  }


  static parseIncludeMember(dynamic json) {
    if (json == null || (json is String && json.isEmpty)) {
      return <Account>[];
    }

    if (json is String) {
      List<String> ids = json.split(',');
      return ids.map((id) => Account(familyMemberUuid: id, uuid: id)).toList();
    }

    return (json as List<dynamic>).map((e) {
      var res = Account.fromJson(e as Map<String, dynamic>);
      res.familyMemberUuid ??= res.uuid;
      return res;
    }).toList();
  }

  static RepeatConfig? _repeatTypeFromJson(dynamic json) {
    if (json == null || json.isEmpty) {
      return null;
    }
    if(json is String) {
      return RepeatConfig.fromJson(jsonDecode(json));
    }
    return RepeatConfig.fromJson(json);
  }

  static Map<String, dynamic>? _repeatTypeToJson(RepeatConfig? repeat) {
    if (repeat == null) {
      return null;
    }
    return repeat.toJson();
  }

  @override
  String toString() {
    return 'EventModels(uuid: $uuid, status: $status, createdAt: $createdAt, createdBy: $createdBy, activityId: $activityId, listId: $listId, familyId: $familyId, name: $name, description: $description, fromDate: $fromDate, toDate: $toDate, color: $color, notificationStatus: $notificationStatus, notificationTime: $notificationTime, activity: $activity, memberIds: $memberIds, members: $members)';
  }

}
