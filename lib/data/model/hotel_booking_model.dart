
import 'package:json_annotation/json_annotation.dart';

part 'hotel_booking_model.g.dart';

@JsonSerializable()
class HotelBookingModel {
  String? hotelId;
  String? hotelName;
  String? imageUrl;
  String? location;
  String? provider;
  String? checkInDate;
  String? checkOutDate;

  List<dynamic>? bookingResults;

  HotelBookingModel({
    this.hotelId,
    this.hotelName,
    this.imageUrl,
    this.location,
    this.provider,
    this.checkInDate,
    this.checkOutDate,
    this.bookingResults,
  });

  factory HotelBookingModel.fromJson(Map<String, dynamic> json) =>
      _$HotelBookingModelFromJson(json);

  Map<String, dynamic> toJson() => _$HotelBookingModelToJson(this);



}