import 'package:family_app/data/model/thread_message/thread_message_related_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'thread_message.g.dart';

@JsonSerializable()
class ThreadMessage {
  String? uuid;
  int? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'thread_id')
  String? threadId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  String? userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_log')
  String? userLog;
  String? message;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_type')
  String? messageType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_type_id')
  String? messageTypeId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_status')
  String? messageStatus;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'extra_data')
  String? extraData;
  List<dynamic>? activities;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'related_data')
  ThreadMessageRelatedData? relatedData;

  ThreadMessage({
    this.uuid,
    this.status,
    this.createdAt,
    this.threadId,
    this.userId,
    this.userLog,
    this.message,
    this.messageType,
    this.messageTypeId,
    this.messageStatus,
    this.extraData,
    this.activities,
    this.relatedData,
  });

  factory ThreadMessage.fromJson(Map<String, dynamic> json) =>
      _$ThreadMessageFromJson(json);

  Map<String, dynamic> toJson() => _$ThreadMessageToJson(this);
}
