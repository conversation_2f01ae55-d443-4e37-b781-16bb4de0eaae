import 'dart:developer';

import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:json_annotation/json_annotation.dart';

import 'hotel_model.dart';

part 'hotel_offer_model.g.dart';

@JsonSerializable()
class AmadeusHotelOfferResponse{
  List<AmadeusHotelOfferData> data;
  List<dynamic> warnings;
  List<dynamic> errors;

  AmadeusHotelOfferResponse({
    this.data = const [],
    this.warnings = const [],
    this.errors = const [],
  });

  factory AmadeusHotelOfferResponse.fromJson(Map<String, dynamic> json) =>
      _$AmadeusHotelOfferResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AmadeusHotelOfferResponseToJson(this);

}


@JsonSerializable()
class AmadeusHotelOfferData {
  String? type;
  AmadeusHotelModel? hotel;
  bool? isAvailable;

  List<AmadeusHotelOfferModel> offers;

  AmadeusHotelOfferData({
    this.type,
    this.hotel,
    this.isAvailable,
    this.offers = const [],
  });

  factory AmadeusHotelOfferData.fromJson(Map<String, dynamic> json) =>
      _$AmadeusHotelOfferDataFromJson(json);

  Map<String, dynamic> toJson() => _$AmadeusHotelOfferDataToJson(this);


}

@JsonSerializable()
class AmadeusHotelOfferModel extends HotelOfferModel {
  String? rateCode;
  Map<String, dynamic>? rateFamilyEstimated;
  @JsonKey(name: "price")
  AmadeusHotelOfferPrice? priceObj;

  @JsonKey(name: "description")
  Map<String, dynamic>? descriptionObj;
  Map<String, dynamic>? guests;
  AmadeusRoom? room;




  double get price => priceObj?.total ?? 0.0;
  String get currency => priceObj?.currency ?? "USD";
  String? get description => room?.description?['text'] ?? "";

  int get adults => guests?['adults'] ?? 1;
  int get children => guests?['children'] ?? 0;
  String? get beds => "${room?.typeEstimated?["beds"] ?? ""}";





  AmadeusHotelOfferModel({
    super.id,
    super.checkInDate,
    super.checkOutDate,
    this.rateCode,
    this.descriptionObj,
    this.rateFamilyEstimated,
    this.priceObj,
    this.room,
    this.guests,
  });

  factory AmadeusHotelOfferModel.fromJson(Map<String, dynamic> json) =>
      _$AmadeusHotelOfferModelFromJson(json);

  Map<String, dynamic> toJson() => _$AmadeusHotelOfferModelToJson(this);
}

@JsonSerializable()
class AmadeusHotelOfferPrice {
  String? currency;
  @JsonKey(fromJson: _parseDouble)
  double? total;
  @JsonKey(fromJson: _parseDouble)
  double? base;

  AmadeusHotelOfferPrice({
    this.currency,
    this.total,
    this.base,
  });

  factory AmadeusHotelOfferPrice.fromJson(Map<String, dynamic> json) =>
      _$AmadeusHotelOfferPriceFromJson(json);

  Map<String, dynamic> toJson() => _$AmadeusHotelOfferPriceToJson(this);

  static double? _parseDouble(dynamic value) {
    if (value is String) {
      return double.tryParse(value);
    } else if (value is num) {
      return value.toDouble();
    }
    return null;
  }
}

@JsonSerializable()
class AmadeusRoom {
  String? type;
  Map<String, dynamic>? typeEstimated;
  Map<String, dynamic>? description;

  AmadeusRoom({this.type, this.typeEstimated, this.description});

  factory AmadeusRoom.fromJson(Map<String, dynamic> json) =>
      _$AmadeusRoomFromJson(json);
  Map<String, dynamic> toJson() => _$AmadeusRoomToJson(this);




}
