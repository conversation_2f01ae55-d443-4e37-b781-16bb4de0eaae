// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:json_annotation/json_annotation.dart';

part 'account.g.dart';

@JsonSerializable()
class Account {
  String? uuid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'family_uuid')
  String? familyUuid;
  @J<PERSON><PERSON><PERSON>(name: 'family_member_uuid')
  String? familyMemberUuid;
  String? email;
  String? relationship;
  int? status;
  String? role;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'full_name')
  String? fullName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'photo_url')
  String? photoUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'invited_by')
  String? invitedBy;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'invited_at')
  String? invitedAt;
  @J<PERSON><PERSON><PERSON>(name: 'accepted_at')
  String? acceptedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'rejected_at')
  String? rejectedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active')
  int? isActive;
  @J<PERSON><PERSON><PERSON>(name: 'family_name')
  String? familyName;
  String? description;
  String? note;
  String? color;
  String? token;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'active_family')
  String? activeFamily;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'invite_url')
  String? invite_url;

  Account(
      {this.uuid,
      this.familyUuid,
      this.familyMemberUuid,
      this.email,
      this.relationship,
      this.status,
      this.role,
      this.fullName,
      this.photoUrl,
      this.invitedBy,
      this.invitedAt,
      this.acceptedAt,
      this.rejectedAt,
      this.isActive,
      this.note,
      this.color,
      this.familyName,
      this.description,
      this.activeFamily,
      this.token,
      this.invite_url});

  factory Account.fromJson(Map<String, dynamic> json) => _$AccountFromJson(json);

  Map<String, dynamic> toJson() => _$AccountToJson(this);

  Account copyWith(
      {String? uuid,
      String? familyUuid,
      String? familyMemberUuid,
      String? email,
      String? relationship,
      int? status,
      String? role,
      String? fullName,
      String? photoUrl,
      String? invitedBy,
      String? invitedAt,
      String? acceptedAt,
      String? rejectedAt,
      int? isActive,
      String? familyName,
      String? description,
      String? note,
      String? color,
      String? token,
      String? invite_url}) {
    return Account(
        uuid: uuid ?? this.uuid,
        familyUuid: familyUuid ?? this.familyUuid,
        familyMemberUuid: familyMemberUuid ?? this.familyMemberUuid,
        email: email ?? this.email,
        relationship: relationship ?? this.relationship,
        status: status ?? this.status,
        role: role ?? this.role,
        fullName: fullName ?? this.fullName,
        photoUrl: photoUrl ?? this.photoUrl,
        invitedBy: invitedBy ?? this.invitedBy,
        invitedAt: invitedAt ?? this.invitedAt,
        acceptedAt: acceptedAt ?? this.acceptedAt,
        rejectedAt: rejectedAt ?? this.rejectedAt,
        isActive: isActive ?? this.isActive,
        note: note ?? this.note,
        color: color ?? this.color,
        familyName: familyName ?? this.familyName,
        description: description ?? this.description,
        token: token ?? this.token,
        invite_url: invite_url ?? this.invite_url);
  }

  @override
  String toString() {
    return 'Account{ familyUuid: $familyUuid,   email: $email,   role: $role, fullName: $fullName, familyName: $familyName} \n';
  }
}
