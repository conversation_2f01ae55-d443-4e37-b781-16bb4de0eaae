import 'package:collection/collection.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'memory_model.g.dart';

@JsonSerializable()
class MemoryModel {
  final String? uuid;
  final int? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;
  @J<PERSON><PERSON><PERSON>(name: 'family_id')
  final String? familyId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final String? userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'file_id')
  final String? fileId;
  @J<PERSON><PERSON><PERSON>(name: 'activity_id')
  final String? activityId;
  @J<PERSON><PERSON><PERSON>(name: 'attachment_type')
  final String? attachmentType;
  final String? caption;
  final String? lat;
  final String? lon;
  final String? tags;
  final String? members;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'extra_data')
  final String? extraData;
  final List<StorageModel>? files;

  const MemoryModel({
    this.uuid,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.familyId,
    this.userId,
    this.fileId,
    this.activityId,
    this.attachmentType,
    this.caption,
    this.lat,
    this.lon,
    this.tags,
    this.members,
    this.extraData,
    this.files,
  });

  @override
  String toString() {
    return 'MemoryModel(uuid: $uuid, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, familyId: $familyId, userId: $userId, fileId: $fileId, activityId: $activityId, attachmentType: $attachmentType, caption: $caption, lat: $lat, lon: $lon, tags: $tags, members: $members, extraData: $extraData, files: $files)';
  }

  factory MemoryModel.fromJson(Map<String, dynamic> json) {
    return _$MemoryModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$MemoryModelToJson(this);

  MemoryModel copyWith({
    String? uuid,
    int? status,
    String? createdAt,
    String? updatedAt,
    String? familyId,
    String? userId,
    String? fileId,
    String? activityId,
    String? attachmentType,
    String? caption,
    String? lat,
    String? lon,
    String? tags,
    String? members,
    String? extraData,
    List<StorageModel>? files,
  }) {
    return MemoryModel(
      uuid: uuid ?? this.uuid,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      familyId: familyId ?? this.familyId,
      userId: userId ?? this.userId,
      fileId: fileId ?? this.fileId,
      activityId: activityId ?? this.activityId,
      attachmentType: attachmentType ?? this.attachmentType,
      caption: caption ?? this.caption,
      lat: lat ?? this.lat,
      lon: lon ?? this.lon,
      tags: tags ?? this.tags,
      members: members ?? this.members,
      extraData: extraData ?? this.extraData,
      files: files ?? this.files,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! MemoryModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      uuid.hashCode ^
      status.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      familyId.hashCode ^
      userId.hashCode ^
      fileId.hashCode ^
      activityId.hashCode ^
      attachmentType.hashCode ^
      caption.hashCode ^
      lat.hashCode ^
      lon.hashCode ^
      tags.hashCode ^
      members.hashCode ^
      extraData.hashCode ^
      files.hashCode;
}
