import 'package:dio/dio.dart';
import 'package:family_app/data/model/base_response.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'event_api.g.dart';

@RestApi()
abstract class EventAPI {
  factory EventAPI(Dio dio, {String? baseUrl}) = _EventAPI;

  @POST('/event/')
  Future<BaseResponse> createEvent(@Body() Map<String, dynamic> body);

  @GET('/event/{id}')
  Future<BaseResponse> getEventById(@Path() String id, @Queries() Map<String, dynamic> queries);

  @GET('/event/all/{id}') 
  Future<BaseResponse> getAllEventByActivity(@Path() String id);

  @GET('/event/all/family/{familyId}')
  Future<BaseResponse> getAllEventInFamily(@Path() String familyId, @Queries() Map<String, dynamic> queries);

  @PUT('/event/{id}')
  Future<BaseResponse> updateEvent(@Path() String id, @Body() Map<String, dynamic> body);

  @DELETE('/event/{id}')
  Future<BaseResponse> deleteEvent(@Path() String id);
}
