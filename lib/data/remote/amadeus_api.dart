import 'package:dio/dio.dart';
import 'package:family_app/data/model/amadeus/authorization.dart';
import 'package:family_app/data/model/amadeus/hotel_offer_model.dart';
import 'package:family_app/data/model/amadeus/hotel_rating.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

import '../model/base_response.dart';

part 'amadeus_api.g.dart';

@RestApi()
abstract class AmadeusAPI {
  factory AmadeusAPI(Dio dio, {String? baseUrl}) = _AmadeusAPI;

  @POST('/v1/security/oauth2/token')
  @FormUrlEncoded()
  Future<AmadeusAuth> oauth2(@Body() Map<String, dynamic> body);

  @GET('/v1/reference-data/locations/hotels/by-city')
  Future<BaseResponse> getHotelByCity(
    @Query('cityCode') String cityCode, {
    @Query('ratings') String? ratings,
    @Query('amenities') String? amenities,
  });

  @GET('/v1/reference-data/locations/hotels/by-hotels')
  Future<BaseResponse> getHotelByIds(@Query('hotelIds') String hotelIds);

  @GET('/v1/reference-data/locations/cities')
  Future<BaseResponse> getCityByName(
    @Query('keyword') String keyword,
    @Query('countryCode') String countryCode,
    @Query('max') int max,
  );

  @GET('/v3/shopping/hotel-offers')
  Future<AmadeusHotelOfferResponse> getHotelOffers(@Query('hotelIds') String hotelIds, @Query('adults') int adults,
      {@Query('checkInDate') String? checkInDate,
      @Query('checkOutDate') String? checkOutDate,
      @Query('roomQuantity') int? roomQuantity,
      @Query('rateCode') String? rateCode,
      @Query('currencyCode') String? currencyCode});

  @POST('/v2/booking/hotel-orders')
  Future<BaseResponse> bookHotel(@Body() Map<String, dynamic> body);
  
  
  @GET('/v2/e-reputation/hotel-sentiments')
  Future<AMAHotelRatingResponse> getHotelRatings(@Query('hotelIds') String hotelIds);
}
