import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:dio/dio.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/usecase/sign_in_usecase.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/utils/loading.dart';

class AmadeusInterceptor extends InterceptorsWrapper {
  final Dio _dio;
  final LocalStorage _localStorage;

  AmadeusInterceptor({
    required Dio dio,
    required LocalStorage localStorage,
  })  : _dio = dio,
        _localStorage = localStorage;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // options.headers.putIfAbsent("Content-Type", () => "application/x-www-form-urlencoded");
    final token = _localStorage.amadeusAccessToken;
    final tokenExpired = _localStorage.amadeusTokenExpired;
    bool isExpired = _isTokenExpired(tokenExpired);
    if (token != null && token.isNotEmpty && !isExpired) {
      options.headers.putIfAbsent("Authorization", () => "Bearer $token".trim());
    }
    super.onRequest(options, handler);
  }

  bool _isTokenExpired(String? tokenExpired) {
    if (tokenExpired == null) return true;
    final expiredDate = DateTime.parse(tokenExpired);
    return expiredDate.isBefore(DateTime.now());
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.data is Map) {
      final map = response.data as Map;
      if ((map['code'] ?? 200) != 200) {
        throw DioException.badResponse(
          statusCode: map['code'] ?? 200,
          requestOptions: response.requestOptions,
          response: response,
        );
      }
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    handler.next(err);
  }

  Future<void> _renewRequest(DioException error, ErrorInterceptorHandler handler) async {
    try {
      final requestOptions = error.requestOptions;
      var requestData = requestOptions.data;
      final options = Options(
        method: requestOptions.method,
        headers: requestOptions.headers,
      );
      if (requestData is FormData) {
        requestData = requestData.clone();
      }

      // return errorHandler.resolve(request);
      var request = await _dio.request(
        "${requestOptions.baseUrl}${requestOptions.path}",
        data: requestData,
        queryParameters: requestOptions.queryParameters,
        options: options,
      );
      handler.resolve(request);
    } catch (exception) {
      if (exception is DioException) {
        handler.next(exception);
      } else {
        handler.next(error);
      }
    }
  }
}
