import 'dart:async' show Timer;

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/authentication/active_account/active_account_cubit.dart';
import 'package:family_app/screen/authentication/active_account/active_account_state.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/header.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

@RoutePage()
class ActiveAccountPage extends BaseBlocProvider<ActiveAccountState, ActiveAccountCubit> {
  final String email, password, uuid;

  const ActiveAccountPage(this.email, this.password, this.uuid, {super.key});

  @override
  Widget buildPage() => const ActiveAccountView();

  @override
  ActiveAccountCubit createCubit() => ActiveAccountCubit(
        email,
        password,
        uuid,
        authenRepository: locator.get(),
        signInUsecase: locator.get(),
        verifyAccountUsecase: locator.get(),
      );
}

class ActiveAccountView extends StatefulWidget {
  const ActiveAccountView({super.key});

  @override
  State<ActiveAccountView> createState() => _ActiveAccountViewState();
}

class _ActiveAccountViewState extends BaseBlocPageStateV2<ActiveAccountView, ActiveAccountState, ActiveAccountCubit> {
  final _timeLeft = ValueNotifier(60);
  Timer? _timer;

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool listenWhen(ActiveAccountState previous, ActiveAccountState current) {
    if (previous.status != current.status) {
      switch (current.status) {
        case ActiveAccountStatus.none:
          break;

        case ActiveAccountStatus.loading:
          break;

        case ActiveAccountStatus.getEmailSuccess:
          break;

        case ActiveAccountStatus.error:
          showSimpleToast(LocaleKeys.an_error_occurred_text.tr());
          break;

        case ActiveAccountStatus.loginSuccess:
          context.router.replaceAll([const HomeRoute()]);
          break;

        case ActiveAccountStatus.signUpFamily:
          context.replaceRoute(const SignUpFamilyRoute());
          break;
      }
    }

    return super.listenWhen(previous, current);
  }

  @override
  void initState() {
    _startCountdown();
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget buildBody(BuildContext context, ActiveAccountCubit cubit, ActiveAccountState state) {
    return Padding(
      padding: paddingV2(vertical: 16, horizontal: 8).add(EdgeInsets.only(top: context.top)),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Header(LocaleKeys.active_account.tr()),
        SizedBox(height: 8.h2),
        Container(
          width: double.infinity,
          padding: paddingV2(all: 16),
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(24.w)),
          child: Column(children: [
            SvgPicture.asset(Assets.images.userWarning.path, width: 80.w2),
            Text(LocaleKeys.active_account_desc.tr(),
                style: AppStyle.regular16V2(color: appTheme.grayV2), textAlign: TextAlign.center),
            SizedBox(height: 24.h2),
            ValueListenableBuilder(
              valueListenable: _timeLeft,
              builder: (context, timeLeft, child) => PrimaryButtonV2(
                text: timeLeft > 1
                    ? LocaleKeys.active_account_time_left.tr(namedArgs: {'time': '$timeLeft'})
                    : LocaleKeys.active_account_resend.tr(),
                bg: timeLeft > 1 ? appTheme.backgroundV2 : const Color(0xffeae9f6),
                color: timeLeft > 1 ? const Color(0xff6C6C6C) : const Color(0xff4a42b5),
                isLoading: state.status == ActiveAccountStatus.loading,
                isActive: timeLeft < 1,
                onTap: () {
                  _startCountdown();
                  cubit.onSubmit();
                },
                useOpacity: false,
              ),
            ),
          ]),
        ),
      ]),
    );
  }

  void _startCountdown() {
    _timer?.cancel();
    _timeLeft.value = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeLeft.value < 1) return timer.cancel();

      _timeLeft.value = _timeLeft.value - 1;
    });
  }
}
