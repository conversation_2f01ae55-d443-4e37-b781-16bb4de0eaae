import 'dart:developer';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/screen/main/calendar/calendar_cubit.dart';
import 'package:family_app/screen/main/event/detail_event/detail_event_parameter.dart';
import 'package:family_app/utils/loading.dart';

import 'detail_event_state.dart';

class DetailEventCubit extends BaseCubit<DetailEventState> {
  final IEventRepository eventRepository;
  // final CalendarCubit calendarCubit;
  final DetailEventParameter parameter;

  DetailEventCubit({
    required this.parameter,
    // required this.calendarCubit,
    required this.eventRepository,
  }) : super(DetailEventState());

  @override
  void onInit() async {
    super.onInit();
    emit(state.copyWith(eventModels: parameter.eventModels));
    showLoading();
    try {
      final result = await eventRepository.getEventById(parameter.eventModels?.uuid ?? '');
      if (result != null) {
        emit(state.copyWith(eventModels: result));
      }
    } catch (e) {
      log(e.toString());
    }
    if (state.eventModels == null || state.eventModels?.uuid?.isEmpty == true) {
      emit(state.copyWith(isNotFound: true));
    }
    dismissLoading();
  }

  void updateEventModel(EventModels event) {
    emit(state.copyWith(eventModels: event));
  }

  Future<bool> onDelete() async {
    try {
      showLoading();

      final result = await eventRepository.deleteEvent(state.eventModels?.uuid ?? '');
      if (result) {
        if (locator.isRegistered<CalendarCubit>()) {
          locator.get<CalendarCubit>().removeEvent(state.eventModels?.uuid ?? '');
        }
        return true;
      }
      return false;
    } catch (e) {
      return false;
    } finally {
      dismissLoading();
    }
  }
}
