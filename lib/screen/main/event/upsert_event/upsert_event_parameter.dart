import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/message_data.dart';

class UpsertEventParameter {
  final EventModels? model;
  final DateTime? dateTime;
  final MessageData? messageData;
  final String activityId;

  UpsertEventParameter({this.model, this.messageData, this.dateTime, this.activityId = ''});
}
