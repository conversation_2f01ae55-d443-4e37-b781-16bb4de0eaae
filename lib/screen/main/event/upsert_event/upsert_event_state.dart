import 'dart:ui';

import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/repeat_config_model.dart';
import 'package:flutter/material.dart';

class UpsertEventState extends BaseState {
  final DateTime? startDate;
  final DateTime? endDate;
  final Color? selectedColor;
  final EventModels? eventModels;
  final List<Account> memberList;
  final bool isOn;
  final bool isAllDay;
  final String? content;
  final EventRepeatType repeatType;
  final RepeatConfig? repeatConfig;
  final int? reminder;
  final ActivityModel? activity;
  final List<WeekDay> selectedDayOfWeek;
  final bool weekStartsOnMonday;
  final bool isDeviceEvent;

  UpsertEventState({
    this.startDate,
    this.endDate,
    this.selectedColor,
    this.eventModels,
    this.memberList = const [],
    this.isOn = false,
    this.isAllDay = false,
    this.repeatType = EventRepeatType.none,
    this.repeatConfig,
    this.selectedDayOfWeek = const [],
    this.weekStartsOnMonday = true,
    this.reminder = 0,
    this.content,
    this.activity,
    this.isDeviceEvent = false,
  });

  @override
  List<Object?> get props => [
        startDate,
        endDate,
        selectedColor,
        eventModels,
        memberList,
        reminder,
        isOn,
        isAllDay,
        repeatType,
        repeatConfig,
        selectedDayOfWeek,
        weekStartsOnMonday,
        content,
        activity,
        isDeviceEvent,
      ];

  UpsertEventState copyWith({
    DateTime? startDate,
    DateTime? endDate,
    Color? selectedColor,
    EventModels? eventModels,
    List<Account>? memberList,
    int? reminder,
    bool? isOn,
    bool? isAllDay,
    String? content,
    EventRepeatType? repeatType,
    RepeatConfig? repeatConfig,
    List<WeekDay>? selectedDayOfWeek,
    bool? weekStartsOnMonday,
    bool? isDeviceEvent,
    ActivityModel? activity,

  }) {
    return UpsertEventState(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      selectedColor: selectedColor ?? this.selectedColor,
      eventModels: eventModels ?? this.eventModels,
      memberList: memberList ?? this.memberList,
      reminder: reminder ?? this.reminder,
      isOn: isOn ?? this.isOn,
      isAllDay: isAllDay ?? this.isAllDay,
      content: content ?? this.content,
      repeatType: repeatType ?? this.repeatType,
      selectedDayOfWeek: selectedDayOfWeek ?? this.selectedDayOfWeek,
      weekStartsOnMonday: weekStartsOnMonday ?? this.weekStartsOnMonday,
      activity: activity ?? this.activity,
      repeatConfig: repeatConfig ?? this.repeatConfig,
      isDeviceEvent: isDeviceEvent ?? this.isDeviceEvent,
    );
  }
}
