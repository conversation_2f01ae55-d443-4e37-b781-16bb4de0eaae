import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/config/theme/app_theme_util.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/device_event.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/repeat_config_model.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/data/usecase/event_usecase.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

import 'upsert_event_state.dart';

class UpsertEventCubit extends BaseCubit<UpsertEventState> {
  final EventUsecase eventUsecase;
  final IActivityRepository activityRepository;
  final UpsertEventParameter parameter;
  final AccountService accountService;
  final IEventRepository eventRepository;

  UpsertEventCubit(
      {required this.eventUsecase,
      required this.parameter,
      required this.eventRepository,
      required this.accountService,
      required this.activityRepository})
      : super(UpsertEventState());

  late final TextFieldHandler title;
  late final TextFieldHandler description;
  late final FormTextFieldHandler formHandler;

  @override
  void onInit() async {
    super.onInit();
    title = TextFieldHandler(
      field: 'title',
      hintText: LocaleKeys.untitled_event.tr(),
      isRequired: true,
      initializeText: parameter.model?.name ?? '',
    );
    final messageData = parameter.messageData;
    description = TextFieldHandler(
      field: 'description',
      hintText: LocaleKeys.description.tr(),
      isRequired: true,
      initializeText:
          messageData?.description ?? parameter.model?.description ?? '',
    );
    formHandler = FormTextFieldHandler(
        handlers: [title, description], validateForm: (map) async {});

    if (parameter.model != null) {
      _fillEditData(parameter.model!.uuid, parameter.model);
    } else {
      //create
      var defaultReminder = locator.get<LocalStorage>().defaultReminder;
      emit(state.copyWith(
        reminder: defaultReminder,
        selectedColor: themeUtil.selectionColor().first,
      ));
    }

    LocalStorage localStorage = locator.get<LocalStorage>();
    var weekStartsOnMonday = localStorage.isStartOfWeekMonday;
    emit(state.copyWith(
      weekStartsOnMonday: weekStartsOnMonday,
      startDate: messageData?.fromDate?.toLocalDT ??
          parameter.model?.fromDate?.toLocalDT ??
          parameter.dateTime?.startDay,
      endDate: messageData?.toDate?.toLocalDT ??
          parameter.model?.toDate?.toLocalDT ??
          parameter.dateTime?.endDay,
      isDeviceEvent: parameter.model is DeviceEventModel,
    ));
  }

  _fillEditData(String? uuid, EventModels? model) async {
    //device calendar event
    if (model is DeviceEventModel) {
      emit(state.copyWith(
          isAllDay: model.event.allDay ?? false, isDeviceEvent: true));
    } else if (parameter.model?.uuid?.isNotEmpty == true) {
      var res = await eventRepository.getEventById(parameter.model!.uuid!);
      if (res != null) {
        //reminder
        var notificationTime = parameter.model?.notificationTime;
        logd("notificationTime: $notificationTime");
        if (notificationTime != null && notificationTime.isNotEmpty && res.repeat != null) {
          final dateTime = DateTime.parse(notificationTime);
          final fromDate = res.fromDate?.toLocalDT ?? parameter.dateTime?.startDay;
          final duration = fromDate!.difference(dateTime);
          if (duration.inMinutes > 0) {
            emit(state.copyWith(reminder: duration.inMinutes));
          }
        }
        //repeat
        if (res.repeat != null) {
          var repeat = res.repeat!;
          if ((repeat.byDayOfWeek ?? []).isNotEmpty) {
            emit(state.copyWith(
                repeatType: EventRepeatType.custom,
                selectedDayOfWeek: repeat.byDayOfWeek));
          } else {
            switch (repeat.frequency) {
              case Frequency.daily:
                emit(state.copyWith(repeatType: EventRepeatType.daily));
                break;
              case Frequency.weekly:
                emit(state.copyWith(repeatType: EventRepeatType.weekly));
                break;
              case Frequency.monthly:
                emit(state.copyWith(repeatType: EventRepeatType.monthly));
                break;
              case Frequency.yearly:
                emit(state.copyWith(repeatType: EventRepeatType.yearly));
                break;
            }
          }

          if(repeat.reminder != null && repeat.reminder! > 0) {
            emit(state.copyWith(reminder: repeat.reminder));
          }
        } else {
          emit(state.copyWith(repeatType: EventRepeatType.none));
        }

        emit(state.copyWith(
          selectedColor: res.color.toColor,
          activity: res.activity,
          memberList: res.members ?? accountService.memberInFamily.value,
          isAllDay: res.allDay == 1,
        ));

        final activityId = res.activityId ?? parameter.activityId;
        if (activityId.isNotEmpty) {
          final activityModels =
              await activityRepository.getActivityById(activityId);
          emit(state.copyWith(activity: activityModels));
        }
      } else {
        //not found
      }
    }
  }

  void toggleSwitch(bool value) {
    String content;
    if (value) {
      // if (state.startDate != null && state.endDate != null) {
      // final duration = state.startDate!.difference(DateTime.now());
      content = "$REMIND_TIME mins before";
      // } else {
      //   content = "Notification is On";
      // }
    } else {
      content = "Event Reminder";
    }

    emit(state.copyWith(isOn: value, content: content));
  }

  void updateSelectedColor(Color color) {
    emit(state.copyWith(selectedColor: color));
  }

  void updateSelectedMember(List<Account> selectedMember) async {
    emit(state.copyWith(memberList: selectedMember));
  }

  void updateStartDate(DateTime newDateTime) {
    if (state.endDate != null && state.endDate!.isBefore(newDateTime)) {
      // return showSimpleToast(LocaleKeys.end_time_has_been_reset_because_it_cannot_be_earlier_than_start_time_text.tr());
      emit(state.copyWith(
          startDate: newDateTime,
          endDate: newDateTime.add(const Duration(hours: 1))));
    } else {
      emit(state.copyWith(startDate: newDateTime));
    }
  }

  void updateEndDate(DateTime newDateTime) {
    if (state.startDate != null && newDateTime.isBefore(state.startDate!)) {
      return showSimpleToast(
          LocaleKeys.end_time_cannot_be_earlier_than_start_time_text.tr());
    }

    emit(state.copyWith(endDate: newDateTime));
  }

  void createEvent() async {
    try {
      formHandler.onSubmit();

      if (formHandler.isFormError.value) {
        return;
      } else if (state.selectedColor == null) {
        return showSimpleToast(LocaleKeys.please_select_a_color_text.tr());
      } else if (state.startDate == null) {
        return showSimpleToast(LocaleKeys.please_select_a_start_time_text.tr());
      } else if (state.endDate == null) {
        return showSimpleToast(LocaleKeys.please_select_an_end_time_text.tr());
      } else if (title.text.isEmpty) {
        return showSimpleToast(LocaleKeys.please_input_title.tr());
      }
      var difference = state.endDate!.difference(state.startDate!);
      if (difference.inMinutes < 5) {
        return showSimpleToast(LocaleKeys.limit_event_duration.tr());
      }

      if (state.repeatType == EventRepeatType.custom) {
        if (state.selectedDayOfWeek.isEmpty) {
          return showSimpleToast(LocaleKeys.please_select_day_of_week.tr());
        }
      }

      //Relse if (state.activity == null) {
      //   return showSimpleToast(LocaleKeys.please_select_belong.tr());
      // }

      showLoading();

      DateTime startDate = state.startDate!;
      DateTime endDate = state.endDate!;


      if (state.isAllDay) {
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        endDate = DateTime(endDate.year, endDate.month, endDate.day, 23, 59);
      }

      RepeatConfig? repeatConfig;
      switch (state.repeatType) {
        case EventRepeatType.none:
          repeatConfig = null;
          break;
        case EventRepeatType.daily:
          repeatConfig = RepeatConfig(
            frequency: Frequency.daily,
          );
          break;
        case EventRepeatType.weekly:
          repeatConfig = RepeatConfig(
            frequency: Frequency.weekly,
          );
          break;
        case EventRepeatType.monthly:
          repeatConfig = RepeatConfig(
            frequency: Frequency.monthly,
          );
          break;
        case EventRepeatType.yearly:
          repeatConfig = RepeatConfig(
            frequency: Frequency.yearly,
          );
          break;
        case EventRepeatType.custom:
          repeatConfig = RepeatConfig(
            frequency: Frequency.weekly,
            byDayOfWeek: state.selectedDayOfWeek,
          );
          break;
      }


      DateTime? reminderTime;
      if (state.reminder != null && state.reminder! > 0) {
        if(repeatConfig != null){
          repeatConfig.reminder = state.reminder;
        }else{
          reminderTime = startDate.subtract(Duration(minutes: state.reminder!));
        }

      }

      final result = await eventUsecase.call(
        EventParameter(
          id: parameter.model?.uuid ?? '',
          name: title.text.trim(),
          fromDate: startDate.toUtc().toIso8601String(),
          toDate: endDate.toUtc().toIso8601String(),
          color: state.selectedColor?.text ?? '',
          caption: '',
          allDay: state.isAllDay ? 1 : 0,
          description: description.text.trim(),
          familyId: accountService.familyId,
          activityId: state.activity?.uuid ?? '',
          notificationStatus: 'some_message',
          notificationTime: reminderTime?.toUtc().toIso8601String() ?? '',
          repeat: repeatConfig?.toJSONString(),
          // repeatType: state.repeatType,
          members:
              state.memberList.map((e) => e.familyMemberUuid ?? '').toList(),
        ),
      );
      emit(state.copyWith(eventModels: result));
    } catch (e) {
      print(e);
    } finally {
      dismissLoading();
    }
  }

  Future<List<ActivityModel>> getAllActivities(
      {int page = 1, int limit = LIMIT, String? name}) async {
    try {
      final result = await activityRepository
          .getAllActivities(accountService.familyId, name: name);
      return result;
    } catch (e) {
      return [];
    }
  }

  void updateSelectedActivity(ActivityModel activity) async {
    emit(state.copyWith(activity: activity));
  }

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }

  void handelEventAllDay(bool value) {
    emit(state.copyWith(isAllDay: value));
  }

  onChangeReminder(int? value) {
    emit(state.copyWith(reminder: value));
  }

  onChangeEventRepeatType(EventRepeatType value) {
    emit(state.copyWith(repeatType: value));
  }

  onChangeDayOfWeek(WeekDay e) {
    var dayOfWeeks = state.selectedDayOfWeek.toList();
    if (dayOfWeeks.contains(e)) {
      dayOfWeeks.remove(e);
    } else {
      dayOfWeeks.add(e);
    }
    emit(state.copyWith(selectedDayOfWeek: dayOfWeeks));
  }
}
