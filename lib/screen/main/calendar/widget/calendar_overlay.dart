import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/calendar/calendar_screen.dart';
import 'package:family_app/screen/main/calendar/overlay/calendar_switch_overlay.dart';
import 'package:family_app/screen/main/calendar/painter/overlay_painter.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

// const calendarTypes = <CalendarView>[
//   CalendarView.week,
//   CalendarView.day,
//   CalendarView.month,
// ];

class CalendarOverlay extends StatelessWidget {
  const CalendarOverlay({super.key, this.onTap, required this.calendarView});

  final ItemOverlayClick? onTap;
  final FLCalendarView calendarView;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 122,
      padding: padding(horizontal: 4, vertical: 4),
      decoration: BoxDecoration(color: appTheme.background, boxShadow: [
        BoxShadow(
            color: Color(0xFF7D7E80).withOpacity(.12),
            // blurStyle: BlurStyle.outer,
            offset: Offset(0, 2),
            blurRadius: 100)
      ]),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildItemWidget(
            context,
            () => onTap?.call(FLCalendarView.agenda),
            FLCalendarView.agenda,
          ),
          Divider(color: appTheme.borderColor),
          _buildItemWidget(
            context,
            () => onTap?.call(FLCalendarView.day),
            FLCalendarView.day,
          ),
          Divider(color: appTheme.borderColor),
          _buildItemWidget(
            context,
            () => onTap?.call(FLCalendarView.week),
            FLCalendarView.week,
          ),
          Divider(color: appTheme.borderColor),
          _buildItemWidget(
            context,
            () => onTap?.call(FLCalendarView.month),
            FLCalendarView.month,
          ),
        ],
      ),
    );
  }

  _buildItemWidget(BuildContext context, void Function() onTap, FLCalendarView view) {
    return InkWell(
      onTap: () => onTap.call(),
      child: Container(
        width: double.infinity,
        padding: padding(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: calendarView == view ? appTheme.primaryColorV2.withValues(alpha: 0.15) : appTheme.background,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ImageAssetCustom(imagePath: view.imagePath, size: 20),
            const SizedBox(width: 8),
            Text(view.name, style: AppStyle.regular14())
          ],
        ),
      ),
    );
  }
}

extension CalendarViewExtension on FLCalendarView {
  String get imagePath {
    if (this == FLCalendarView.day) {
      return Assets.images.calendarDay.path;
    } else if (this == FLCalendarView.week) {
      return Assets.images.calendarWeek.path;
    } else if (this == FLCalendarView.month) {
      return Assets.images.calendarMonth.path;
    } else if (this == FLCalendarView.agenda) {
      return Assets.images.calendarAgenda.path;
    }

    return Assets.images.calendarMonth.path;
  }

  String get name {
    if (this == FLCalendarView.day) {
      return LocaleKeys.day.tr();
    } else if (this == FLCalendarView.week) {
      return LocaleKeys.week.tr();
    } else if (this == FLCalendarView.month) {
      return LocaleKeys.month.tr();
    } else if (this == FLCalendarView.agenda) {
      return LocaleKeys.agenda.tr();
    }

    return LocaleKeys.month.tr();
  }
}
