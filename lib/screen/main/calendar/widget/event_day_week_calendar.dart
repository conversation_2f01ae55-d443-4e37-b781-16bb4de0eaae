import 'package:auto_route/auto_route.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/calendar/datasource/month_calendar_datasource.dart';
import 'package:family_app/screen/main/calendar/widget/event_widget.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import '../calendar_cubit.dart';
import '../calendar_screen.dart';
import '../calendar_state.dart';
import 'custom_current_timeline.dart';
import 'day_cell_view.dart';

class EventDayCalendar extends StatelessWidget {
  final CalendarCubit cubit;
  final CalendarState state;
  final Function(BuildContext, CalendarTapDetails, CalendarCubit, CalendarState) onCalendarTap;

  const EventDayCalendar({
    super.key,
    required this.cubit,
    required this.state,
    required this.onCalendarTap,
  });

  bool isShowBorderColor(FLCalendarView calendarViewFL) {
    return calendarViewFL == FLCalendarView.day || calendarViewFL == FLCalendarView.week;
  }

  @override
  Widget build(BuildContext context) {
    var hasToday = false;
    for (var event in state.currentDates) {
      if (event.isToday) {
        hasToday = true;
        break;
      }
    }

    var maxCountTopEvent = maxCountAllDay();
    return SizedBox(
      height: 30 * 24 + maxCountTopEvent * 30,
      child: Stack(
        children: [
          SfCalendar(
            view: state.calendarViewType,
            controller: cubit.calendarController,
            initialSelectedDate: state.currentDate,
            initialDisplayDate: state.currentDate,
            dataSource: MeetingDataSource(state.models),
            todayHighlightColor: appTheme.red3CColor,
            cellBorderColor: isShowBorderColor(state.calendarViewFL) ? Colors.grey.shade400 : Colors.transparent,
            headerHeight: 0,
            viewHeaderHeight: 0,
            showCurrentTimeIndicator: false,
            viewHeaderStyle: ViewHeaderStyle(
              dateTextStyle: AppStyle.bold14(color: appTheme.blackColor),
              dayTextStyle: AppStyle.bold14(color: appTheme.blackColor),
            ),
            specialRegions: [
              if (state.calendarViewFL == FLCalendarView.week)
                TimeRegion(
                  startTime: DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 0, 0, 0),
                  endTime: DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59, 59),
                  color: appTheme.calendarDateBackgroundColor.withValues(alpha: 0.12),
                  text: '',
                  enablePointerInteraction: false,
                ),
            ],
            firstDayOfWeek: state.firstDayOfWeek,
            onViewChanged: (viewChangedDetails) => cubit.onChangeCurrentCalendarDate(viewChangedDetails.visibleDates),
            scheduleViewSettings: const ScheduleViewSettings(appointmentItemHeight: 70),
            timeSlotViewSettings: const TimeSlotViewSettings(
              timeFormat: 'HH:mm',
              timeIntervalHeight: 30,
            ),
            onLongPress: (calendarTapDetails) {
              final date = calendarTapDetails.date;
              if (date?.isBeforeNow ?? false) {
                // can't create event in the past
                return;
              }
              context.pushRoute(UpsertEventRoute(upsertEventParameter: UpsertEventParameter(dateTime: date)));
            },
            monthViewSettings: MonthViewSettings(
              appointmentDisplayMode: state.calendarViewFL == FLCalendarView.month
                  ? MonthAppointmentDisplayMode.appointment
                  : MonthAppointmentDisplayMode.indicator,
            ),
            monthCellBuilder: (context, details) {
              return DayCellView(
                currentMonth: state.currentMonth,
                date: details.date,
                calendarViewFL: state.calendarViewFL,
              );
            },
            appointmentBuilder: (BuildContext context, details) {
              final event = details.appointments.first as EventModels;
              if (state.calendarViewFL == FLCalendarView.month) {
                return GestureDetector(
                  onTap: () => cubit.onTapEvent(context, event),
                  child: Container(
                    height: 20,
                    decoration: BoxDecoration(
                      color: event.color?.toColor ?? appTheme.primaryColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: Text(
                      event.name ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.fade,
                      style: AppStyle.regular10V2(color: Colors.white),
                    ),
                  ),
                );
              }

              return EventWidget(
                event: event,
                onTap: (event) => cubit.onTapEvent(context, event),
              );
            },
            onTap: (calendarTapDetails) => onCalendarTap(context, calendarTapDetails, cubit, state),
          ),
          if(hasToday)
            const CustomCurrentTimeLine(),
        ],
      ),
    );
  }

  int maxCountAllDay(){
    int maxCount = 0;
    for (var currentDay in state.currentDates) {
      List<EventModels> displayEvents = filterCurrentDayEvents(state.models, currentDay);
      int allDayEventCount = countAllDayEvent(displayEvents);
      if (allDayEventCount > maxCount) {
        maxCount = allDayEventCount;
      }
    }
    return maxCount;
  }

  List<EventModels> filterCurrentDayEvents(events, DateTime currentDay) {
    return events.where((event) => isCurrentDayEvent(event, currentDay)).toList();
  }


  int countAllDayEvent(List<EventModels> events){
    int count = events.where((event) => isAllDay(event)).length;
    return count;
  }


  bool isCurrentDayEvent(EventModels event, DateTime currentDay) {
    final startTime = event.fromDate?.toLocalDT;
    final endTime = event.toDate?.toLocalDT;
    if (startTime == null || endTime == null) {
      return false;
    }
    if(startTime.isSameDay(currentDay) || endTime.isSameDay(currentDay)){
      return true;
    }

    int startMilis = startTime.millisecondsSinceEpoch;
    int endMilis = endTime.millisecondsSinceEpoch;
    int currentMilis = currentDay.millisecondsSinceEpoch;

    if (startMilis <= currentMilis && endMilis >= currentMilis) {
      return true;
    }

    return false;

  }

  bool isAllDay(EventModels event) {
    final startTime = event.fromDate?.toLocalDT;
    final endTime = event.toDate?.toLocalDT;
    if (startTime == null || endTime == null) {
      return false;
    }
    if (startTime.isSameDay(endTime)) {
      if (startTime.hour < 7 && endTime.hour > 22) {
        return true;
      }
    }else {
      return true;
    }
    return false;
  }
}
