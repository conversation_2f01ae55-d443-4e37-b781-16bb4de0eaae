import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/calendar/datasource/month_calendar_datasource.dart';
import 'package:family_app/screen/main/calendar/widget/event_widget.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import 'custom_current_timeline.dart';

double _timeSlotIntervalHeight = 30.0;

class DayRangeEventView extends StatelessWidget {
  const DayRangeEventView({
    super.key,
    required this.events,
    required this.currentDay,
    required this.onTap,
  });

  final DateTime currentDay;
  final List<EventModels> events;
  final void Function(EventModels) onTap;

  @override
  Widget build(BuildContext context) {
    List<EventModels> displayEvents = filterCurrentDayEvents();
    int allDayEventCount = countAllDayEvent(displayEvents);

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            height: 50,
            padding: const EdgeInsets.only(left: 10),
            alignment: Alignment.centerLeft,
            child: Text(
              currentDay.eventDate_ddMMMCommaYYYY,
              style: AppStyle.bold14(color: appTheme.grayV2),
            ),
          ),
          SizedBox(
            height: 30 * 24 + allDayEventCount * 30,
            child: Stack(
              children: [
                SfCalendar(
                  key: ValueKey(currentDay),
                  view: CalendarView.day,
                  initialSelectedDate: currentDay,
                  initialDisplayDate: currentDay,
                  dataSource: MeetingDataSource(displayEvents),
                  headerHeight: 0,
                  viewHeaderHeight: 0,
                  cellBorderColor: Colors.grey.shade400,
                  todayHighlightColor: appTheme.red3CColor,
                  viewNavigationMode: ViewNavigationMode.none,
                  showCurrentTimeIndicator: false,
                  timeSlotViewSettings: TimeSlotViewSettings(
                    timeFormat: 'HH:mm',
                    timeIntervalHeight: _timeSlotIntervalHeight,
                  ),
                  appointmentBuilder: (BuildContext context, details) {
                    final event = details.appointments.first as EventModels;
                    return EventWidget(
                      event: event,
                      onTap: (event) => onTap(event),
                    );
                  },
                ),
                const CustomCurrentTimeLine(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<EventModels> filterCurrentDayEvents(){
    return events.where((event) => isCurrentDayEvent(event)).toList();
  }


  int countAllDayEvent(List<EventModels> events){
    int count = events.where((event) => isAllDay(event)).length;
    print("countAllDayEvent: $count");
    return count;
  }


  bool isCurrentDayEvent(EventModels event) {
    final startTime = event.fromDate?.toLocalDT;
    final endTime = event.toDate?.toLocalDT;
    if (startTime == null || endTime == null) {
      return false;
    }
    if(startTime.isSameDay(currentDay) || endTime.isSameDay(currentDay)){
      return true;
    }

    int startMilis = startTime.millisecondsSinceEpoch;
    int endMilis = endTime.millisecondsSinceEpoch;
    int currentMilis = currentDay.millisecondsSinceEpoch;

    if (startMilis <= currentMilis && endMilis >= currentMilis) {
      return true;
    }

    return false;

  }

  bool isAllDay(EventModels event) {
    final startTime = event.fromDate?.toLocalDT;
    final endTime = event.toDate?.toLocalDT;
    if (startTime == null || endTime == null) {
      return false;
    }


    if (startTime.isSameDay(endTime)) {
      if (startTime.hour < 7 && endTime.hour > 22) {
        return true;
      }
    }else {
      return true;
    }
    return false;
  }



}


