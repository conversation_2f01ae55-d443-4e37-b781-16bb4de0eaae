import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/calendar/calendar_screen.dart';
import 'package:flutter/material.dart';

class DayCellView extends StatelessWidget {
  const DayCellView({
    super.key, 
    required this.currentMonth, 
    required this.date, 
    required this.calendarViewFL,
    this.holidays,
  });

  final DateTime currentMonth;
  final DateTime date;
  final FLCalendarView calendarViewFL;
  final Map<String, String>? holidays;

  @override
  Widget build(BuildContext context) {
    final isToday = date.isToday;
    final isNotSameCurrentMonth = currentMonth.month != date.month;
    final isNotVisible = isNotSameCurrentMonth;

    // Check if the current date is a holiday
    final holidayKey = '${date.month}-${date.day}';
    final isHoliday = holidays?.containsKey(holidayKey) ?? false;
    final holidayName = isHoliday ? holidays![holidayKey] : null;

    // Choose text color based on conditions
    final textColor = isToday
        ? appTheme.whiteText
        : isHoliday
            ? appTheme.orangeColorV2
            : isNotVisible
                ? appTheme.hintColor
                : appTheme.blackTextV2;

    final textView = Text(
      '${date.day}',
      style: AppStyle.bold14V2(color: textColor),
    );

    if (calendarViewFL == FLCalendarView.month) {
      return Column(
        children: [
          isToday
              ? Container(
                  padding: padding(top: 2, bottom: 5, left: 8, right: 7),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: appTheme.primaryColorV2,
                  ),
                  child: Center(child: textView),
                )
              : Tooltip(
                  message: holidayName ?? '',
                  textStyle: AppStyle.regular12(color: Colors.white),
                  decoration: BoxDecoration(
                    color: Colors.black87,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: textView,
                ),
        ],
      );
    }

    return Container(
      child: isToday
          ? Center(
              child: Container(
                padding: padding(horizontal: 8, vertical: 5),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: appTheme.primaryColorV2,
                ),
                child: textView,
              ),
            )
          : Tooltip(
              message: holidayName ?? '',
              textStyle: AppStyle.regular12(color: Colors.white),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Center(child: textView),
            ),
    );
  }
}
