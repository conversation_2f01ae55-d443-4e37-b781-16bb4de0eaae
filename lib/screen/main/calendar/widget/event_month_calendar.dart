import 'package:auto_route/auto_route.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/calendar/datasource/month_calendar_datasource.dart';
import 'package:family_app/screen/main/calendar/widget/event_widget.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import '../calendar_cubit.dart';
import '../calendar_screen.dart';
import '../calendar_state.dart';
import 'day_cell_view.dart';

class EventCalendar extends StatelessWidget {
  final CalendarCubit cubit;
  final CalendarState state;
  final Function(BuildContext, CalendarTapDetails, CalendarCubit, CalendarState) onCalendarTap;

  const EventCalendar({
    super.key,
    required this.cubit,
    required this.state,
    required this.onCalendarTap,
  });

  bool isShowBorderColor(FLCalendarView calendarViewFL) {
    return calendarViewFL == FLCalendarView.day || calendarViewFL == FLCalendarView.week;
  }

  @override
  Widget build(BuildContext context) {
    return SfCalendar(
      view: state.calendarViewType,
      controller: cubit.calendarController,
      initialSelectedDate: state.currentMonth,
      initialDisplayDate: state.currentMonth,
      dataSource: MeetingDataSource(state.models),
      todayHighlightColor: appTheme.red3CColor,
      showCurrentTimeIndicator: false,
      cellBorderColor: isShowBorderColor(state.calendarViewFL) ? Colors.grey.shade400 : Colors.transparent,
      headerHeight: 0,
      viewHeaderHeight: 0,
      viewHeaderStyle: ViewHeaderStyle(
        dateTextStyle: AppStyle.bold14(color: appTheme.blackColor),
        dayTextStyle: AppStyle.bold14(color: appTheme.blackColor),
      ),
      specialRegions: [
        if (state.calendarViewFL == FLCalendarView.week)
          TimeRegion(
            startTime: DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 0, 0, 0),
            endTime: DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59, 59),
            color: appTheme.calendarDateBackgroundColor.withValues(alpha: 0.12),
            text: '',
            enablePointerInteraction: false,
          ),
      ],
      firstDayOfWeek: state.firstDayOfWeek,
      onViewChanged: (viewChangedDetails) => cubit.onChangeCurrentCalendarDate(viewChangedDetails.visibleDates),
      scheduleViewSettings: const ScheduleViewSettings(appointmentItemHeight: 70),
      timeSlotViewSettings: const TimeSlotViewSettings(
        timeFormat: 'HH:mm',
        timeIntervalHeight: 30,
      ),
      onLongPress: (calendarTapDetails) {
        final date = calendarTapDetails.date;
        if (date?.isBeforeNow ?? false) {
          // can't create event in the past
          return;
        }
        context.pushRoute(UpsertEventRoute(upsertEventParameter: UpsertEventParameter(dateTime: date)));
      },
      monthViewSettings: MonthViewSettings(
        appointmentDisplayMode: state.calendarViewFL == FLCalendarView.month
            ? MonthAppointmentDisplayMode.appointment
            : MonthAppointmentDisplayMode.indicator,
      ),
      monthCellBuilder: (context, details) {
        return DayCellView(
          currentMonth: state.currentMonth,
          date: details.date,
          calendarViewFL: state.calendarViewFL,
          holidays: state.holidays,
        );
      },
      appointmentBuilder: (BuildContext context, details) {
        final event = details.appointments.first as EventModels;
        if (state.calendarViewFL == FLCalendarView.month) {
          return GestureDetector(
            onTap: () => cubit.onTapEvent(context, event),
            child: Container(
              height: 20,
              decoration: BoxDecoration(
                color: event.color?.toColor ?? appTheme.primaryColor,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: Text(
                event.name ?? '',
                maxLines: 1,
                overflow: TextOverflow.fade,
                style: AppStyle.regular10V2(color: Colors.white),
              ),
            ),
          );
        }

        return EventWidget(
          event: event,
          onTap: (event) => cubit.onTapEvent(context, event),
        );
      },
      onTap: (calendarTapDetails) => onCalendarTap(context, calendarTapDetails, cubit, state),
    );
  }
}
