import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class MeetingDataSource extends CalendarDataSource<EventModels> {
  /// Creates a meeting data source, which used to set the appointment
  /// collection to the calendar
  MeetingDataSource(List<EventModels> source) {
    appointments = source;
  }

  @override
  DateTime getStartTime(int index) {
    return _getMeetingData(index).fromDate?.toLocalDT ?? DateTime.now();
  }

  @override
  DateTime getEndTime(int index) {
    var startTime = getStartTime(index);
    var endTime =  _getMeetingData(index).toDate?.toLocalDT;

    if(endTime == null || startTime.millisecondsSinceEpoch == endTime.millisecondsSinceEpoch) {
      return startTime.add(const Duration(minutes: 5));
    }
    return _getMeetingData(index).toDate?.toLocalDT ?? DateTime.now();
  }

  @override
  String getSubject(int index) {
    return _getMeetingData(index).name ?? '';
  }

  @override
  Color getColor(int index) {
    return _getMeetingData(index).color?.toColor ?? appTheme.primaryColor;
  }

  @override
  bool isAllDay(int index) {
    return _getMeetingData(index).isAllDay;
  }

  EventModels _getMeetingData(int index) {
    return appointments![index];
  }
}

extension EventModelExtension on EventModels {
  bool get isAllDay {
    final startTime = fromDate?.toLocalDT;
    final endTime = toDate?.toLocalDT;
    if (startTime == null || endTime == null) {
      return false;
    }
    if (startTime.isSameDay(endTime)) {
      if (startTime.hour < 7 && endTime.hour > 22) {
        return true;
      }
    }
    return false;
  }




}
