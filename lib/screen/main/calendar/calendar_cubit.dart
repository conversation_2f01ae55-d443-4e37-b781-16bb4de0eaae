import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/calendar_service.dart';
import 'package:family_app/config/service/holiday_service.dart';
import 'package:family_app/config/service/location_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/calendar/calendar_screen.dart';
import 'package:family_app/screen/main/calendar/calendar_state.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class CalendarCubit extends BaseCubit<CalendarState> {
  final IEventRepository eventRepository;
  final AccountService accountService;
  final CalendarService calendarService;
  String? _userCountry;
  bool _isInitialized = false;

  CalendarCubit({
    required this.eventRepository,
    required this.accountService,
    required this.calendarService,
  }) : super(CalendarState(currentMonth: DateTime.now())) {
    logd("CalendarCubit Init: ${state.currentMonth}");
  }

  CalendarController calendarController = CalendarController();

  StreamController<DateTime> selectedDateStreamController = StreamController<DateTime>.broadcast();

  // CalendarController calendarController1 = CalendarController();

  @override
  void onInit() async {
    locator.registerSingleton<CalendarCubit>(this);

    super.onInit();
    await initializeUserCountry();
  }

  Future<void> initializeUserCountry() async {
    if (_isInitialized) return;
    var startOfWeekMonday = CalendarService.instance.isStartOfWeekMonday();
    emit(state.copyWith(weekStartForMonday: startOfWeekMonday));
    try {
      _userCountry = await LocationService.getUserCountry();
      _isInitialized = true;

      await Future.wait([
        onFetchEvent(),
        onFetchHolidays(),
      ]);
    } catch (e) {
      print('Error initializing user country: $e');
      _userCountry = 'US';
      _isInitialized = true;

      await Future.wait([
        onFetchEvent(),
        onFetchHolidays(),
      ]);
    }
  }

  @override
  Future<void> close() {
    locator.unregister<CalendarCubit>();
    return super.close();
  }

  onTapEvent(BuildContext context, EventModels event) async {
    await context.pushRoute(UpsertEventRoute(upsertEventParameter: UpsertEventParameter(model: event)));
    onFetchEvent();
  }

  void onChangeCurrentCalendarDate(List<DateTime> date) {
    if (date.isEmpty) return;
    if (state.calendarViewFL == FLCalendarView.week) {
      emit(state.copyWith(currentMonth: date.first, currentDates: date));
    } else {
      final currentMonth = date[date.length ~/ 2];
      emit(state.copyWith(currentMonth: currentMonth, currentDates: date));
    }

    onFetchEvent();
    onFetchHolidays();
  }

  void onChangeCalendarView(FLCalendarView view) {
    emit(state.copyWith(calendarViewFL: view));
    calendarController.view = state.calendarViewType;
  }

  Future onFetchEvent() async {
    late final DateTime startTime;
    late final DateTime endTime;
    if (state.calendarViewFL == FLCalendarView.month || state.calendarViewFL == FLCalendarView.agenda) {
      startTime = state.currentMonth.startOfMonth;
      endTime = state.currentMonth.endOfMonth;
    } else if (state.calendarViewFL == FLCalendarView.week) {
      startTime = state.currentMonth.startDayOfWeek;
      endTime = state.currentMonth.endDayOfWeek;
    } else {
      startTime = state.currentMonth.startDayOfWeek;
      endTime = state.currentMonth.endDayOfWeek;
    }
    var startOfWeekMonday = CalendarService.instance.isStartOfWeekMonday();
    emit(state.copyWith(weekStartForMonday: startOfWeekMonday));

    logd("Fetch event from $startTime to $endTime startOfWeekMonday $startOfWeekMonday");
    try {
      final result = await eventRepository.getEventInFamily(accountService.familyId, from: startTime, to: endTime);
      CalendarService calendarService = locator.get();
      // await calendarService.syncEvent(startTime, endTime, result);
      var deviceEvents = await calendarService.retrieveEvents(startTime, endTime);
      print("Device events: ${deviceEvents.length}");



      emit(state.copyWith(models: [...result, ...deviceEvents]));
    } catch (e) {
      print(e);
    }
    dismissLoading();
  }

  Future<void> onFetchHolidays() async {
    if (!_isInitialized) return;

    emit(state.copyWith(isLoadingHolidays: true));
    try {
      final holidays = await HolidayService.getHolidays(
        _userCountry ?? 'US',
        state.currentMonth.year,
        state.currentMonth.month,
      );
      emit(state.copyWith(holidays: holidays, isLoadingHolidays: false));
    } catch (e) {
      print('Error fetching holidays: $e');
      emit(state.copyWith(isLoadingHolidays: false));
    }
  }

  void onUpsertEvent(EventModels event, bool isUpdate) {
    late DateTime startTime;
    late DateTime endTime;
    if (state.calendarViewFL == FLCalendarView.month) {
      startTime = state.currentMonth.startOfMonth;
      endTime = state.currentMonth.endOfMonth;
    } else if (state.calendarViewFL == FLCalendarView.week) {
      startTime = state.currentMonth.startDayOfWeek;
      endTime = state.currentMonth.endDayOfWeek;
    } else {
      startTime = state.currentMonth.startDay;
      endTime = state.currentMonth.endDay;
    }
    final eventStart = event.fromDate?.toLocalDT;
    final eventEnd = event.toDate?.toLocalDT;
    if ((eventStart?.isAfter(startTime) ?? false) && (eventEnd?.isBefore(endTime) ?? false)) {
      final models = [...state.models];
      if (isUpdate) {
        final index = models.indexWhere((element) => element.uuid == event.uuid);
        if (index != -1) {
          models[index] = event;
        }
      } else {
        models.add(event);
      }
      emit(state.copyWith(models: models));
    }
  }

  List<DateTime> getRangeDate() {
    late DateTime startTime;
    late DateTime endTime;
    if (state.calendarViewFL == FLCalendarView.month) {
      startTime = state.currentMonth.startOfMonth;
      endTime = state.currentMonth.endOfMonth;
    } else if (state.calendarViewFL == FLCalendarView.week) {
      startTime = state.currentMonth.startDayOfWeek;
      endTime = state.currentMonth.endDayOfWeek;
    } else {
      startTime = state.currentMonth.startDay;
      endTime = state.currentMonth.endDay;
    }
    return [startTime, endTime];
  }

  void onAddEventInRangeTime(List<String> eventId, ActivityModel? activity) {
    final newList = [...state.models];
    for (final id in eventId) {
      final index = newList.indexWhere((e) => e.uuid == id);
      if (index != -1) {
        newList[index] = newList[index].copyWith(activityId: activity?.uuid, activity: activity);
      }
    }
    emit(state.copyWith(models: newList));
  }

  // final eventStart = event.fromDate?.toLocalDT;
  //   final eventEnd = event.toDate?.toLocalDT;
  //   if ((eventStart?.isAfter(startTime) ?? false) && (eventEnd?.isBefore(endTime) ?? false)) {
  //     final models = [...state.models];
  //     if (isUpdate) {
  //       final index = models.indexWhere((element) => element.uuid == event.uuid);
  //       if (index != -1) {
  //         models[index] = event;
  //       }
  //     } else {
  //       models.add(event);
  //     }
  //     emit(state.copyWith(models: models));
  //   }

  void removeEvent(String id) {
    final models = [...state.models];
    final index = models.indexWhere((element) => element.uuid == id);
    if (index != -1) {
      models.removeAt(index);
    }
    emit(state.copyWith(models: models));
  }

  Future<void> onCreateNewEvent(BuildContext context, DateTime selectedDate) async {
    await context.pushRoute(UpsertEventRoute(upsertEventParameter: UpsertEventParameter(dateTime: selectedDate)));
    onFetchEvent();
  }
}
