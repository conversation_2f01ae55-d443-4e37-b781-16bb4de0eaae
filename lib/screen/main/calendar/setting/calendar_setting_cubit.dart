import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/calendar_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'calendar_setting_state.dart';

class CalendarSettingCubit extends BaseCubit<CalendarSettingState> {
  CalendarSettingCubit(): super(CalendarSettingState(publicHolidayCalendar: LocaleKeys.england.tr())){
    init();
  }

  Future<void> init() async {
    var pref = await SharedPreferences.getInstance();
    var syncWithDeviceCalendar = CalendarService.instance.isEnableCalendarSync();
    var startOfWeekMonday = CalendarService.instance.isStartOfWeekMonday();
    var publicHolidayCalendar = pref.getString('publicHolidayCalendar') ?? LocaleKeys.england.tr();
    var defaultReminder = locator.get<LocalStorage>().defaultReminder;
    emit(state.copyWith(
      syncWithDeviceCalendar: syncWithDeviceCalendar,
      startOfWeekMonday: startOfWeekMonday,
      publicHolidayCalendar: publicHolidayCalendar,
      reminderMinutes: defaultReminder,
    ));
  }

  Future<void> onChangeSyncWithDeviceCalendar() async {
    var syncWithDeviceCalendar = !state.syncWithDeviceCalendar;
    logd('Sync with device calendar: $syncWithDeviceCalendar');
    emit(state.copyWith(syncWithDeviceCalendar: syncWithDeviceCalendar));
    CalendarService.instance.setEnableCalendarSync(syncWithDeviceCalendar);
  }

  Future<void> onChangeStartOfWeekMonday() async {
    var startOfWeekMonday = !state.startOfWeekMonday;
    logd('Start of week Monday: $startOfWeekMonday');
    emit(state.copyWith(startOfWeekMonday: startOfWeekMonday));
    await CalendarService.instance.setStartOfWeekMonday(startOfWeekMonday);
    HomeCubit homeCubit = locator.get<HomeCubit>();
    homeCubit.refreshUIOnly();


  }

  void onChangeReminderMinutes(int e) {
    logd('Reminder minutes: $e');
    emit(state.copyWith(reminderMinutes: e));
    locator.get<LocalStorage>().cacheDefaultReminder(e);
  }

  @override
  Future<void> close() {

    return super.close();
  }
}