import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_bottomsheet_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/timezone.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'calendar_setting_cubit.dart';
import 'calendar_setting_state.dart';

class CalendarSettingPage extends BaseBlocProvider<CalendarSettingState, CalendarSettingCubit> {
  const CalendarSettingPage({
    super.key,
  });

  @override
  Widget buildPage() => const CalendarSettingView();

  @override
  CalendarSettingCubit createCubit() => CalendarSettingCubit();
}

class CalendarSettingView extends StatefulWidget {
  const CalendarSettingView({super.key});

  @override
  State<StatefulWidget> createState() => _CalendarSettingPageState();
}

class _CalendarSettingPageState
    extends BaseBlocBottomSheetPageState<CalendarSettingView, CalendarSettingState, CalendarSettingCubit> {
  @override
  String get title => LocaleKeys.setting.tr();

  @override
  Widget buildAppBar(BuildContext context, CalendarSettingCubit cubit, CalendarSettingState state) {
    return Container(
      padding: padding(horizontal: 16),
      height: 44,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Text(
                  LocaleKeys.cancel.tr(),
                  style: AppStyle.medium16(color: appTheme.primaryColorV2),
                ),
              ),
              const Spacer(),
            ],
          ),
          Text(
            title,
            textAlign: TextAlign.center,
            style: AppStyle.medium16(color: appTheme.blackColor),
          )
        ],
      ),
    );
  }

  @override
  Widget buildBody(BuildContext context, CalendarSettingCubit cubit, CalendarSettingState state) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(LocaleKeys.general_settings.tr(), style: AppStyle.bold14V2()),
          ListTile(
            leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
            title: Text(LocaleKeys.public_holidays.tr(), style: AppStyle.regular14V2()),
            subtitle: Text(LocaleKeys.england.tr(), style: AppStyle.regular12V2()),
            onTap: () {
              // Handle Public Holidays setting
            },
          ),
          // ListTile(
          //   leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
          //   title: Text(LocaleKeys.week_numbers.tr(), style: AppStyle.regular14V2()),
          //   subtitle: Text(LocaleKeys.off.tr(), style: AppStyle.regular12V2()),
          //   onTap: () {
          //     // Handle Week Numbers setting
          //   },
          // ),
          ListTile(
            leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
            title: Text(LocaleKeys.start_of_week.tr(), style: AppStyle.regular14V2()),
            subtitle: Text(state.startOfWeekMonday ? LocaleKeys.monday.tr() : LocaleKeys.sunday.tr(),
                style: AppStyle.regular12V2()),
            onTap: () {
              cubit.onChangeStartOfWeekMonday();
            },
          ),
          ListTile(
            leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
            title: Text(LocaleKeys.default_reminder.tr(), style: AppStyle.regular14V2()),
            subtitle: Text(getDisplayReminder(state.reminderMinutes), style: AppStyle.regular12V2()),
            onTap: () {
              // Handle Week Numbers setting
              BottomSheetUtils.showScrollable(context,
                  isDismissible: true,
                  child: Container(
                    width: double.infinity,
                    color: Colors.white,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: setupReminderMinutes
                          .map((e) => InkWell(
                                child: Container(
                                    height: 50,
                                    alignment: Alignment.center,
                                    color: state.reminderMinutes == e ? appTheme.primaryColorV2.withValues(alpha: 0.1) : Colors.white,
                                    child: Text(getDisplayReminder(e), style: AppStyle.regular14V2())),
                                onTap: () {
                                  cubit.onChangeReminderMinutes(e);
                                  Navigator.of(context).pop();
                                },
                              ))
                          .toList(),
                    ),
                  ));
            },
          ),
          ListTile(
            leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
            title: Text(LocaleKeys.sync_with_device_calendar.tr(), style: AppStyle.regular14V2()),
            subtitle: Text(state.syncWithDeviceCalendar ? LocaleKeys.on.tr() : LocaleKeys.off.tr(),
                style: AppStyle.regular12V2()),
            onTap: () {
              cubit.onChangeSyncWithDeviceCalendar();
            },
          ),
        ],
      ),
    );
  }

  String getDisplayReminder(int minutes) {
    if (minutes == 0) {
      return LocaleKeys.none.tr();
    } else if (minutes <= 60) {
      return "$minutes ${LocaleKeys.minutes.tr()}";
    } else {
      return "${minutes ~/ 60} ${LocaleKeys.hours.tr()}";
    }
  }
}
