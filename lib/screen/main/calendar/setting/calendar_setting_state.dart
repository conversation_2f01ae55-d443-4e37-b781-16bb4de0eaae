import 'package:family_app/base/widget/cubit/base_state.dart';

class CalendarSettingState extends BaseState {
  final bool syncWithDeviceCalendar;
  final bool startOfWeekMonday;
  final String? publicHolidayCalendar;
  final int reminderMinutes;

  CalendarSettingState({
    this.syncWithDeviceCalendar = false,
    this.startOfWeekMonday = false,
    this.publicHolidayCalendar,
    this.reminderMinutes = 0,
  });

  CalendarSettingState copyWith({
    bool? syncWithDeviceCalendar,
    bool? startOfWeekMonday,
    String? publicHolidayCalendar,
    int? reminderMinutes,
  }) {
    return CalendarSettingState(
      syncWithDeviceCalendar: syncWithDeviceCalendar ?? this.syncWithDeviceCalendar,
      startOfWeekMonday: startOfWeekMonday ?? this.startOfWeekMonday,
      publicHolidayCalendar: publicHolidayCalendar ?? this.publicHolidayCalendar,
      reminderMinutes: reminderMinutes ?? this.reminderMinutes,
    );
  }

  @override
  List<Object?> get props => [
        syncWithDeviceCalendar,
        startOfWeekMonday,
        publicHolidayCalendar,
        reminderMinutes,
      ];
}
