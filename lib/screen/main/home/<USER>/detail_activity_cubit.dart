import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/list_log_item.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/screen/main/calendar/calendar_cubit.dart';
import 'package:family_app/screen/main/home/<USER>/detail_activity_parameter.dart';
import 'package:family_app/screen/main/main_cubit.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';

import 'detail_activity_state.dart';

class DetailActivityCubit extends BaseCubit<DetailActivityState> {
  final IActivityRepository activityRepository;
  final IEventRepository eventRepository;
  final MainCubit mainCubit;
  final DetailActivityParameter parameter;

  DetailActivityCubit({
    required this.parameter,
    required this.activityRepository,
    required this.mainCubit,
    required this.eventRepository,
  }) : super(DetailActivityState());

  @override
  void onInit() async {
    super.onInit();
    refresh();
  }

  Future refresh() async {
    showLoading();
    emit(state.copyWith(activity: parameter.activity));
    final activityId = parameter.activity?.uuid ?? parameter.activityId;
    try {
      final result = await Future.wait([
        activityRepository.getActivityById(activityId),
        // eventRepository.getAllEventByActivity(activityId),
        activityRepository.getAllItemLogInActivity(activityId),
      ]);
      var activity = result[0] as ActivityModel;
      var log = result[1] as List<ListLog>;
      var events = activity.includedEvents ?? <EventModels>[];

      activity = activity.copyWith(includedEvents: events);
      emit(state.copyWith(activity: activity, logs: log));
    } catch (e) {
      log(e.toString());
    }
    if (state.activity == null || state.activity?.uuid?.isEmpty == true) {
      emit(state.copyWith(state: DetailState.notFound));
    }
    dismissLoading();
  }

  void onLoadLog() async {
    final result = await activityRepository.getAllItemLogInActivity(state.activity?.uuid ?? '');
    emit(state.copyWith(logs: result));
  }

  void updateActivity() {
    refresh();
    // emit(state.copyWith(activity: activity));
  }

  Future<void> onDeleteActivity() async {
    try {
      showLoading();
      final result = await activityRepository.deleteActivity(state.activity?.uuid ?? '');
      if (result) {
        mainCubit.deleteActivity(state.activity?.uuid ?? '');
        if (locator.isRegistered<CalendarCubit>()) {
          locator.get<CalendarCubit>().onFetchEvent();
        }
        emit(state.copyWith(state: DetailState.delete));
      }
    } catch (e) {
      emit(state.copyWith(state: DetailState.none));
      showSimpleToast(LocaleKeys.action_fail.tr());
    } finally {
      dismissLoading();
    }
  }
}
