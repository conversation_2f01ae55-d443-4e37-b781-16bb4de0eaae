//use for the TASH card section, each card represents a task
// A Task can be an item in a Shopping list, or Todo List ,
// To qualitfy to be shown on the front page, the task must have a deadline on its own (not from the list that it bleongs to), and also the deadline is TODAY@ch
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/persons_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class TaskCard extends StatelessWidget {
  final String category, title, subtitle, dueDate;
  final double progress;
  final int avatarCount;
  final Item aItem;
  final Function(Item) onTap;

  const TaskCard({
    super.key,
    required this.category,
    required this.title,
    required this.subtitle,
    required this.dueDate,
    required this.progress,
    required this.avatarCount,
    required this.aItem,
    required this.onTap,
  });

  (String, Color) get _typeBadge {
    switch (category) {
      case 'shopping' || 'Shopping':
        return (LocaleKeys.shopping.tr(), appTheme.orangeColorV2);

      case 'todo' || 'Todo':
        return (LocaleKeys.todos.tr(), appTheme.blueColorV2);

      default:
        return ('Other', const Color(0xFF725550));
    }
  }

  @override
  Widget build(BuildContext context) {
    final radius = BorderRadius.circular(16.w2);

    return Padding(
      padding: paddingV2(right: 8),
      child: Material(
        borderRadius: radius,
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onTap(aItem),
          borderRadius: radius,
          child: Ink(
            padding: paddingV2(all: 8),
            decoration: BoxDecoration(color: _typeBadge.$2.withValues(alpha: 0.12), borderRadius: radius),
            child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Ink(
                  padding: paddingV2(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(9999), color: Colors.white),
                  child: Text(_typeBadge.$1, style: AppStyle.bold10V2(color: _typeBadge.$2)),
                ),
                PersonsView(
                  accounts: aItem.assignment ?? [],
                  avatarSize: 24,
                  spacing: -12,
                ),
                // Transform.translate(
                //   offset: Offset((lengthAssign - 1) * 12.w2, 0),
                //   child: Row(
                //     children: List.generate(lengthAssign, (i) {
                //       final r = Avatar(aItem.assignees![i].photoUrl, size: 24.w2, name: aItem.assignees![i].fullName);
                //       if (i == 0) return r;
                //       return Transform.translate(
                //         offset: Offset(-12.w2 * i, 0),
                //         child: i != lengthAssign - 1 || aItem.assignees!.length == lengthAssign
                //             ? r
                //             : Stack(children: [
                //                 r,
                //                 Positioned.fill(
                //                   child: Container(
                //                     padding: paddingV2(horizontal: 2),
                //                     alignment: Alignment.center,
                //                     decoration: const BoxDecoration(
                //                         color: Color.fromRGBO(0, 0, 0, 0.56), shape: BoxShape.circle),
                //                     child: Text('+${aItem.assignees!.length - lengthAssign}',
                //                         style: AppStyle.bold10V2(color: Colors.white)),
                //                   ),
                //                 ),
                //               ]),
                //       );
                //     }),
                //   ),
                // ),
              ]),
              // SizedBox(height: 8.h2),
              Padding(
                padding: paddingV2(horizontal: 4),
                child: Text(title, overflow: TextOverflow.ellipsis, maxLines: 1, style: AppStyle.bold14V2()),
              ),
              Padding(
                padding: paddingV2(horizontal: 4, bottom: 8),
                child: Row(children: [
                  Container(
                    width: 10.67.w2,
                    height: 10.67.w2,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: appTheme.grayV2, width: 1.w2),
                    ),
                  ),
                  SizedBox(width: 4.67.w2),
                  Expanded(
                    child: Text(subtitle,
                        style: AppStyle.regular12V2(color: appTheme.grayV2),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis),
                  ),
                ]),
              ),
              SizedBox(height: 4.h2),
              Row(children: [
                Container(
                  padding: paddingV2(vertical: 2, left: 4, right: 8),
                  decoration: BoxDecoration(
                      color: const Color.fromRGBO(0, 0, 0, 0.04), borderRadius: BorderRadius.circular(9999)),
                  child: Row(children: [
                    SvgPicture.asset(Assets.icons.icTime.path, width: 16.w2),
                    SizedBox(width: 2.w2),
                    RichText(
                      text: TextSpan(style: AppStyle.regular10V2(), children: [
                        TextSpan(text: '${LocaleKeys.due.tr()}: '),
                        TextSpan(text: dueDate, style: const TextStyle(fontWeight: FontWeight.bold)),
                      ]),
                    ),
                  ]),
                ),
                SizedBox(width: 8.w2),
                // Expanded(
                //   child: ClipRRect(
                //     borderRadius: BorderRadius.circular(4.h2),
                //     child: LinearProgressIndicator(
                //         value: progress, color: _typeBadge.$2, backgroundColor: Colors.grey[200]),
                //   ),
                // ),
                // SizedBox(width: 4.w2),
                // Text('${(progress * 100).toStringAsFixed(0)}%', style: AppStyle.regular10V2(color: appTheme.grayV2)),
              ]),
            ]),
          ),
        ),
      ),
    );
  }

/* dummy =1 meaning using dummy data */
  static Widget fromModel(Item item, {required Function(Item) onTap}) {
    return TaskCard(
      category: item.listCategory ?? '--',
      title: item.parentName ?? '',
      subtitle: item.name ?? '',
      dueDate: item.due_date?.toLocalDT.ddMM ?? '',
      progress: 0.5,
      avatarCount: item.assignment?.length ?? 0,
      aItem: item,
      onTap: onTap,
    );
  }

// // TESTing only -
// static Widget fromDummyModel(DummyItem item) {
//   return TaskCard(
//     category: item.category,
//     title: item.name,
//     subtitle: item.description,
//     dueDate: item.planDate,
//     progress: item.progress / 100,
//     avatarCount: item.includedMembers.length,
//
//   );
// }
}