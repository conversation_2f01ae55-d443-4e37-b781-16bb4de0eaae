import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/screen/main/home/<USER>/activity_home_state.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';

class ActivityHomeCubit extends BaseCubit<ActivityHomeState> {
  final IActivityRepository activityRepository;

  ActivityHomeCubit({required this.activityRepository})
      : super(ActivityHomeState());

  final AccountService accountService = locator.get();

  @override
  Future<void> close() {
    locator.unregister<ActivityHomeCubit>();
    return super.close();
  }

  @override
  void onInit() {
    locator.registerSingleton(this);
    super.onInit();

    fetchActivity();
  }

  Future<void> fetchActivity() async {
    logd("reloading eventsss >>>> ");
    emit(state.copyWith(status: ActivityHomeStatus.loading));
    try {
      // final result = <ActivityModels>[...mainCubit.state.activityList];
      final result = await activityRepository
          .getFamilyActivities(accountService.familyId, limit: 100);
      // final result = await _getMockActivities(); // getActivityList(); //_getMockActivities();
      // Sort the activities by date

      for (var activity in result) {
        if (activity.activityType == 'birthday_registry') {
          activity.imagePath = activity.getImagePath();
        } else {
          if (activity.city != null) {
            activity.imagePath = await provider.fetchImageUrl(activity.city!);
          } else if (activity.country != null) {
            activity.imagePath =
                await provider.fetchImageUrl(activity.country!);
          }
        }
      }

      //Calc the trip duration in days
      for (var activity in result) {
        if (activity.fromDate != null && activity.toDate != null) {
          activity.trip_duration = activity.toDate!.toLocalDT
                  .difference(activity.fromDate!.toLocalDT)
                  .inDays +
              1;
        }
      }
      emit(state.copyWith(
          activityList: result, status: ActivityHomeStatus.success));
    } catch (e) {
      emit(state.copyWith(status: ActivityHomeStatus.error));
      print(e);
    } finally {
      emit(state.copyWith(status: ActivityHomeStatus.done));
    }
  }

  void updateActivity(ActivityModel? activity) {
    if (activity == null) return;
    List<ActivityModel> activeActivities = [
      activity,
      ...state.activityList,
    ];

    emit(state.copyWith(activityList: activeActivities));
  }

  Future<List<ActivityModel>> getActivityList(
      {int page = 1, int limit = LIMIT}) async {
    try {
      final result = await activityRepository
          .getFamilyActivities(accountService.familyId, limit: 100);
      return result;
    } catch (e) {
      print('getActivityList error: $e');
      return [];
    }
  }

  void removeActivity(ActivityModel activity) async {
    emit(state.copyWith(status: ActivityHomeStatus.loading));
    try {
      final result =
          await activityRepository.deleteActivity(activity.uuid ?? '');
      if (result) {
        await fetchActivity();
      }
      emit(state.copyWith(status: ActivityHomeStatus.success));
    } catch (e) {
      emit(state.copyWith(status: ActivityHomeStatus.error));
      print('getActivityList error: $e');
    } finally {
      emit(state.copyWith(status: ActivityHomeStatus.done));
    }
  }

  // Mock data for demonstration
  Future<List<ActivityModel>> _getMockActivities() async {
    return [
      ActivityModel(
          uuid: '1demo',
          fromDate: DateTime(2024, 12, 13).MMM_d_yyyy,
          toDate: DateTime(2024, 12, 18).MMM_d_yyyy,
          caption: "Vietnam southern and middle trip...",
          abbreviation: "05d",
          imagePath: 'assets/images/vietnam_trip.png'),
      ActivityModel(
          uuid: '21demo',
          fromDate: DateTime(2024, 12, 26).MMM_d_yyyy,
          caption: "Birthday Registry for John",
          abbreviation: "BR",
          imagePath: 'assets/images/vietnam_trip.png'),
    ];
  }
}
