import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_detail_parameter.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/home/<USER>/activity_home_cubit.dart';
import 'package:family_app/screen/main/home/<USER>/activity_home_state.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/round_item_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

@RoutePage()
class ActivityHomePage extends BaseBlocProvider<ActivityHomeState, ActivityHomeCubit> {
  const ActivityHomePage({super.key});

  @override
  Widget buildPage() => const ActivityHomeView();

  @override
  ActivityHomeCubit createCubit() => ActivityHomeCubit(activityRepository: locator.get());
}

class ActivityHomeView extends StatefulWidget {
  const ActivityHomeView({super.key});

  @override
  State<ActivityHomeView> createState() => _ActivityHomeViewState();
}

class _ActivityHomeViewState extends BaseBlocPageState<ActivityHomeView,
    ActivityHomeState, ActivityHomeCubit> {
  @override
  bool? get isBottomSafeArea => false;

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  bool listenWhen(ActivityHomeState previous, ActivityHomeState current) {
    switch (current.status) {
      case ActivityHomeStatus.loading:
        _refreshIndicatorKey.currentState?.show();
        break;
      default:
        _refreshIndicatorKey.currentState?.deactivate();
        break;
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, ActivityHomeCubit cubit, ActivityHomeState state) {
    return CustomAppBar2(
      title: LocaleKeys.activities_text.tr(),
      showBack: true,
      actions: [
        GestureDetector(
          onTap: () {
            // goToChatPage();
            goToSelectActivityType();
          },
          behavior: HitTestBehavior.opaque,
          child: CircleItem(
            backgroundColor: appTheme.backgroundV2,
            padding: padding(all: 8),
            child: SvgPicture.asset(Assets.icons.icPlus.path),
          ),
        )
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, ActivityHomeCubit cubit, ActivityHomeState state) {
    if (state.status == ActivityHomeStatus.loading) {
      return const Center(child: CircularProgressIndicator());
    }
    return state.activityList.isNotEmpty
        ? _buildActivityView(cubit, state)
        : _buildEmptyView2();
  }

  Widget _buildActivityView(ActivityHomeCubit cubit, ActivityHomeState state) {
    //phung : disable banner for now
    state.isBannerVisible = false;

    return RefreshIndicator(
      key: _refreshIndicatorKey,
      onRefresh: cubit.fetchActivity,
      child: Column(
        children: [
          // Banner Section
          if (state.isBannerVisible) _buildBannerView(),
          // Activities List
          Expanded(
            child: ListView.builder(
              itemCount: state.activityList.length,
              itemBuilder: (context, index) {
                final activity = state.activityList[index];
                return _buildActivityItem(activity, cubit, state);
              },
              padding: const EdgeInsets.all(8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBannerView() {
    return Dismissible(
      key: const Key('banner'),
      onDismissed: (_) => {}, //cubit.dismissBanner(),
      child: Container(
        margin: const EdgeInsets.all(16.0),
        width: double.infinity, // Match the width of the screen
        decoration: BoxDecoration(
          color: Colors.blue[100],
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: AspectRatio(
          aspectRatio: 16 / 9, // Keep the width-height ratio
          child: Stack(
            fit: StackFit.expand,
            children: [
              Image.asset(
                Assets.images.tripPlanning.path,
                fit: BoxFit.cover,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 16.0, top: 16.0), // Add left and top margin
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'How to plan a trip?',
                      style: AppStyle.bannerTitle,
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 16,
                right: 16,
                child: Container(
                  decoration: BoxDecoration(
                    color: appTheme.buttonColor, // Blue background color
                    borderRadius: BorderRadius.circular(8.0), // Rounded corners
                  ),
                  child: TextButton(
                    onPressed: () {},
                    child: Text('Got it', style: AppStyle.bannerButton),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyView2() {
    return Padding(
      padding: padding(horizontal: 5, vertical: 2),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start, // Stick to the top
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white, // White background color
              borderRadius: BorderRadius.circular(20), // Rounded corners
            ),
            padding: const EdgeInsets.all(16), // Padding inside the container
            child: Column(
              children: [
                // 1st row: Big icon
                Center(
                  child: Assets.images.emptyActivity.image(width: 136, height: 108),
                ),
                const SizedBox(height: 18),
                // 2nd row: Texts
                Center(
                  child: Column(
                    children: [
                      Text(
                        LocaleKeys.no_activities_yet.tr(),
                        style: AppStyle.emptyView2NoActivities,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        LocaleKeys.start_creating_activities.tr(),
                        style: AppStyle.emptyView2StartCreating,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 18),
                _buildNewTrip(),
                const SizedBox(height: 8),
                _buildNewBirthday(),
                const SizedBox(height: 8),
                // 4th row: Text
                Center(
                  child: Text(
                    LocaleKeys.how_to_plan_trip.tr(),
                    style: AppStyle.emptyView2HowToPlanTrip,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewTrip() {
    return _buildNewActivity(true);
  }

  Widget _buildNewBirthday() {
    return _buildNewActivity(false);
  }

  Widget _buildNewActivity(bool isTrip) {
    final text = isTrip ? 'New trip' : 'New birthday registry';
    final iconPath = isTrip ? Assets.icons.icBeachChair.path : Assets.icons.icBirthday.path;
    final textColor = isTrip ? const Color(0xFF3448F0) : const Color(0xFF2E7D32);
    final boxColor = isTrip ? const Color(0x1F3448F0) : const Color(0x1F2E7D32);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32), // Add padding
      // Add Material widget to make InkWell have ripple effect
      child: Material(
          color: Colors.transparent,
          child: InkWell(
              onTap: () {
                if (isTrip) {
                  goToChatPage();
                } else {
                  context.pushRoute(const BirthdayRegistryUpsertRoute());
                }
              },
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: const EdgeInsets.symmetric(
                    horizontal: 32, vertical: 12), // Adjust padding as needed
                decoration: BoxDecoration(
                  color: boxColor,
                  borderRadius: BorderRadius.circular(16), // Rounded corners
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  // Make Row take only needed space
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      iconPath,
                      // width: 24, // Adjust icon size as needed
                      // height: 24, // Adjust icon size as needed
                    ),
                    const SizedBox(width: 8),
                    // Add spacing between icon and text
                    Text(
                      text,
                      style: TextStyle(
                        color: textColor, // Text color
                        fontSize: 16, // Adjust font size as needed
                        fontWeight: FontWeight.bold, // Bold font weight
                      ),
                    ),
                  ],
                ),
              ))),
    );
  }

  Widget _buildActivityItem(ActivityModel activity, ActivityHomeCubit cubit, ActivityHomeState state) {
    return GestureDetector(
      onTap: () {
        goToActivityDetail(activity);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(8),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 8,
          children: [
            Container(
              clipBehavior: Clip.antiAlias,
              decoration: ShapeDecoration(
                image: DecorationImage(
                  image: CachedNetworkImageProvider(activity.imagePath ?? ''),
                  fit: BoxFit.cover,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
                child: AspectRatio(
                  aspectRatio: 370 / 200,
                  child: Stack(
                    children: [
                      Positioned(
                        right: 8,
                        top: 8,
                        child: Container(
                          clipBehavior: Clip.antiAlias,
                          decoration: ShapeDecoration(
                            color: Colors.black.withValues(alpha: 0.38),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
                          ),
                          child: SizedBox(
                            width: 36,
                            height: 36,
                            child: PopupMenuButton(
                              onSelected: (command) {
                                switch (command) {
                                  case 'delete':
                                    _showDeleteConfirmationDialog(context, cubit, activity);
                                    break;
                                  default:
                                    break;
                                }
                              },
                              padding: const EdgeInsets.all(4.0),
                              icon: ImageAssetCustom(imagePath: Assets.icons.threeDot.path),
                              //Menu
                              itemBuilder: (BuildContext context) => [
                                PopupMenuItem(value: 'delete', child: Text(LocaleKeys.delete.tr(), style: AppStyle.textSmR)),
                              ],
                              color: Colors.white,
                              menuPadding: const EdgeInsets.symmetric(vertical: 4),
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    activity.activityType == 'birthday_registry' ? LocaleKeys.birthday_registry.tr() : LocaleKeys.trip_text.tr(),
                    style: AppStyle.textXsS.copyWith(color: activity.activityType == 'birthday_registry'
                        ? appTheme.greenV2
                        : appTheme.blueColorV2,
                    ),
                  ),
                  Text(activity.name ?? '', style: AppStyle.textMdS),
                  Text(
                    activity.activityType == 'birthday_registry'
                        ? activity.fromDate?.toDateTime().timeddMMMyyyy ?? ''
                        : "${activity.fromDate?.toDateTime().shortDayReverse ?? ''} - "
                        "${activity.toDate?.toDateTime().shortDayReverse ?? ''
                        ''} (${activity.trip_duration} ${LocaleKeys.days.tr()})",
                    style: AppStyle.textSmR.copyWith(color: appTheme.grayV2),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmationDialog(
      BuildContext context, ActivityHomeCubit cubit, ActivityModel activity) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(LocaleKeys.delete_activity.tr()),
          content: Text(LocaleKeys.confirm_content_delete_activity.tr()),
          actions: <Widget>[
            TextButton(
              child: Text(LocaleKeys.cancel.tr()),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(LocaleKeys.delete.tr()),
              onPressed: () {
                Navigator.of(context).pop();
                cubit.removeActivity(activity);
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildModeView({
    Color? roundColor,
    String highlightText = '',
    String title = '',
    String content = '',
  }) {
    return RoundItemView(
      viewPadding: padding(top: 8, bottom: 9, horizontal: 12),
      child: Row(
        children: [
          RoundItemView(viewSize: 40, text: highlightText, backgroundColor: roundColor),
          const SizedBox(width: 12),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: AppStyle.modeViewTitle),
              const SizedBox(height: 5),
              Text(content, style: AppStyle.modeViewContent)
            ],
          ))
        ],
      ),
    );
  }

  // Phung: no floating icon in new UI
  // // override buildFloatingActionButton to show floating action button
  // @override
  // Widget? buildFloatingActionButton(BuildContext context, ActivityHomeCubit cubit, ActivityHomeState state) {
  //   return FloatingActionButton(
  //     onPressed: goToChatPage,
  //     // Load image from assets, use Assets.images.ic_add.path
  //     // Remove backgroundColor and child properties
  //     // backgroundColor: Colors.transparent, // No background color
  //     elevation: 0, // No shadow
  //     child: const Icon(Icons.add), // Use white color for the icon
  //   );
  // }

  void goToActivityDetail(ActivityModel activity) async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      switch (activity.activityType) {
        case 'birthday_registry':
          context.pushRoute(BirthdayRegistryDetailRoute(
              parameter: BirthdayRegistryDetailParameter(
                  activityId: activity.uuid, activity: activity)));
          break;
        default:
          context.pushRoute(TripDetailRoute(
              parameter: TripDetailParameter(
                  activityId: activity.uuid, activity: activity)));
          break;
      }
    }
  }

  void goToChatPage() async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      context.pushRoute(ChatRoute(parameter: ChatParameter(chatContext: ChatContext.getGeneralChatContext(token))));
    }
  }

  void goToSelectActivityType() async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      context.pushRoute(const ActivitySelectTypeRoute());
    }
  }
}
