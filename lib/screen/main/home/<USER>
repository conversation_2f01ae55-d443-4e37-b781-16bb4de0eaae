import 'dart:async';
import 'dart:isolate';
import 'dart:ui';

import 'package:auto_route/auto_route.dart';
import 'package:dartx/dartx.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/calendar_service.dart';
import 'package:family_app/config/service/holiday_service.dart';
import 'package:family_app/config/service/location_service.dart';
import 'package:family_app/config/service/notification/notification_service.dart';
import 'package:family_app/config/service/socket/base_socket_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/category.dart' as model;
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/category/icategory_repository.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/thread/ithread_repository.dart';
import 'package:family_app/data/usecase/sign_out_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_parameter.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_parameter.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_screen.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/mixin/list_item_mixin.dart';
import 'package:family_app/utils/snackbar.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart' show debugPrint;

class HomeCubit extends BaseCubit<HomeState> with ListItemMixin {
  final ICategoryRepository categoryRepository;
  final AccountService accountService;
  final IActivityRepository activityRepository;
  final BaseSocketService socketService;
  final NotificationService notificationService;
  final IFamilyRepository familyRepository;
  final SignOutUsecase signOutUsecase;
  final IEventRepository eventRepository;
  final IThreadRepository threadRepository;

  // late final MainCubit mainCubit = locator.get();

  HomeCubit({
    required this.categoryRepository,
    required this.accountService,
    required this.activityRepository,
    required this.socketService,
    required this.familyRepository,
    required this.notificationService,
    required this.signOutUsecase,
    required this.eventRepository,
    required this.threadRepository,
  }) : super(HomeState(currentMonth: DateTime.now()));

  final ReceivePort _receivePort = ReceivePort();

  // Add a new field to track holiday loading state
  Map<String, String>? _currentMonthHolidays;
  bool _isLoadingHolidays = false;

  final ValueNotifier<int> todayIndex = ValueNotifier(0);

  @override
  void onInit() {
    locator.registerSingleton(this);
    super.onInit();
    print("HOME CUBIT INIT.. LOADING");
    emit(state.copyWith(status: HomeStatus.loading));

    accountService.initMyProfile();
    initializeUserCountry();
    onRefresh();

    updateFcmToken();
    notificationService.onHandleInitialMessage();
    _setupIsolated();

    var calendarService = locator.get<CalendarService>();
    calendarService.retrieveCalendars();
    //emit(state.copyWith(status: HomeStatus.none));
  }

  void updateFcmToken() async {
    try {
      logd("update FCM token");

      await notificationService.onRequestPermission();
      // if (result == true) {
      final fcmToken = await notificationService.getFcmToken();

      logd("token is: $fcmToken");

      await familyRepository.updateNotificationSetting(fcmToken ?? '');
      // }
    } catch (e) {
      loge(e.toString());
    }
  }

  Future<void> onRefresh() async {
    //1. GET all the LISTs :
    //XXX: need 3 APIs call to get the LISTs .. wtf!
    //  final categories = await categoryRepository.getAllCategory();
    final lists = await listRepository.getListByFamilyId(accountService.familyId);
    // without this API call, the LISTs category or type will not be set correctly.
//    final newList = await getItemCategoryForListItem(categories, lists);

    //2. GET all the ACTIVITIES  (the latest 10 activities only)
    final activityList = await activityRepository.getFamilyActivities(accountService.familyId);

    //3. GET the latest MEMORY
    MemoryModel? latestMemory;
    String? latestMemoryUserName;
    List<MemoryModel>? memoryList;
    try {
      memoryList = await familyRepository
          .getMemoryListByFamily(accountService.familyId, type: 'list');
      // Sort the memory list by date descending
      memoryList.sort((a, b) => b.updatedAt?.toDateTime().compareTo(a.updatedAt?.toDateTime() ?? DateTime(0)) ?? 0);
      latestMemory = memoryList.firstOrNull;

      AppLogger.d("Latest memory uuid: ${latestMemory?.uuid}, user id: ${latestMemory?.userId}");
      if (latestMemory?.userId != null) {
        final latestMemoryUser = await familyRepository.getUserInfo(latestMemory!.userId!);
        latestMemoryUserName = latestMemoryUser.fullName;
      } else {
        latestMemory = const MemoryModel();
        latestMemoryUserName = null;
      }
    } catch (e) {
      AppLogger.e("Error fetching memory list: $e");
    }

    final now = DateTime.now();
    final events = await () async {
      try {


        return await eventRepository.getEventInFamily(accountService.familyId,
            from: now.firstDayOfMonth, to: now.lastDayOfMonth);
      } catch (e) {
        debugPrint('get event error: ${e.toString()}');

        return <EventModels>[];
      }
    }();
    CalendarService calendarService = locator.get();
    // deviceCalendarService.syncEvent(now.firstDayOfMonth, now.lastDayOfMonth, events);
    var deviceEvents = await calendarService.retrieveEvents(now.firstDayOfMonth, now.lastDayOfMonth);
    print("Device events: ${deviceEvents.length}");



    int getTotalUnreadMessages(List<ThreadFamily> messages) {
      return messages.fold<int>(
        0,
        (total, thread) => total + (thread.unread ?? 0),
      );
    }

    final messages = await () async {
      try {
        return await threadRepository.getAllThread(accountService.familyId);
      } catch (e) {
        debugPrint('get list message error: ${e.toString()}');

        return <ThreadFamily>[];
      }
    }();

    final totalUnreadMessages = getTotalUnreadMessages(messages);

    //4. GET all the TASKs due today: Task can be an item within any List
    final List<Item> tasksDueToday = await familyRepository.getTaskDueToday(accountService.familyId);

    //for each item in taskDueToday, match the item.list_uuid with newList.uuid and set the listName
    for (var task in tasksDueToday) {
      // logd("task: category: ${task.listCategory} , task: ${task.listUuid}");
      for (var list in lists) {
        if (task.listUuid == list.uuid) {
          logd("set task: ${task.name} to list: ${list.name}");
          task.listName = list.name;
        }
      }
    }

    //5. Load and image for activity to show on Dashboard
    for (var activity in activityList) {
      if (activity.activityType == 'birthday_registry') {
        activity.imagePath = activity.getImagePath();
      } else {
        if (activity.city != null) {
          activity.imagePath = await provider.fetchImageUrl(activity.city!);
        } else if (activity.country != null) {
          activity.imagePath = await provider.fetchImageUrl(activity.country!);
        }
      }
    }

    final a = DateTime.now().toString();

    //6. Emit the state - DONE
    emit(state.copyWith(
      events: [...events, ...deviceEvents],
      listItems: lists,
      activityList: activityList,
      latestMemory: latestMemory,
      latestMemoryUser: latestMemoryUserName,
      taskCardIndex: 0,
      tasksDueToday: tasksDueToday,
      status: HomeStatus.none,
      allMemories: memoryList,
      messages: messages,
      totalUnreadMessages: totalUnreadMessages,
    ));

    AppLogger.d(
        "EMIT after loading in HOME cubit list: ${state.listItems.length} , activity: ${state.activityList.length}");

    AppLogger.d(" 222 LISTS for family:");
    for (var list in lists) {
      AppLogger.d(" ${list.name} -  ${list.uuid} - ${list.categoryId}");
    }
  }

  Future<void> onRefreshMemoryCard() async {
    emit(state.copyWith(status: HomeStatus.loading));
    MemoryModel? latestMemory = null;
    String? latestMemoryUserName = null;
    List<MemoryModel>? memoryList;
    try {

      memoryList = await familyRepository
          .getMemoryListByFamily(accountService.familyId, type: 'list');
      // Sort the memory list by date descending
      memoryList.sort((a, b) =>
          b.updatedAt
              ?.toDateTime()
              .compareTo(a.updatedAt?.toDateTime() ?? DateTime(0)) ??
          0);
      latestMemory = memoryList.firstOrNull;

      AppLogger.d(
          "Refresh Memory card: Latest memory uuid: ${latestMemory?.uuid}, user id: ${latestMemory?.userId}");
      if (latestMemory?.userId != null) {
        final latestMemoryUser = await familyRepository.getUserInfo(latestMemory!.userId!);
        latestMemoryUserName = latestMemoryUser.fullName;
      }
    } catch (e) {
      AppLogger.e("Error fetching memory list: $e");
    }
    emit(state.copyWith(
        latestMemory: latestMemory,
        latestMemoryUser: latestMemoryUserName,
        status: HomeStatus.none,
        allMemories: memoryList));
  }

  Future<List<ListItem>> getItemCategoryForListItem(List<model.Category> categories, List<ListItem> allLists) async {
    final allListWithCategory = await Future.wait(allLists.map((aList) => getDetailListItem(categories, aList)));
    return allListWithCategory;
  }

  @override
  Future<void> close() {
    locator.unregister(instance: this);
    _receivePort.close();
    return super.close();
  }

  /** Async function to do API call to update the LISTs of the family. 
   * could be Todo list or shopping list. 
   */
  Future<void> refreshList() async {
    // onRefresh();
    final newLists = await listRepository.getListByFamilyId(accountService.familyId);
    emit(state.copyWith(listItems: newLists));
  }

  // Local Update the lists , but not accurate this way. Use refreshList() instead
  // void updateListItem1(ListItem item) {
  //   logd("updating list item: $item to all list");

  //   final newLists = [...state.listItems];
  //   final index = state.listItems.indexWhere((e) => e.uuid == item.uuid);
  //   if (index != -1) {
  //     newLists[index] = item;
  //   } else {
  //     newLists.add(item);
  //   }
  //   emit(state.copyWith(listItems: newLists));
  // }

  void updateListActivity(ActivityModel item) {
    final newActivity = [...state.activityList];
    final index = state.activityList.indexWhere((e) => e.uuid == item.uuid);
    if (index != -1) {
      newActivity[index] = item;
    } else {
      newActivity.add(item);
    }
    emit(state.copyWith(activityList: newActivity));
  }

  Future<void> updateItemStatusInList(ListItem listItem, int listIndex, int itemIndex) async {
    final items = <Item>[...(listItem.items ?? <Item>[])];
    final originalStatus = items[itemIndex].status;
    final newStatus = items[itemIndex].status == 0 ? 1 : 0;
    items[itemIndex].status = newStatus;
    listItem.items = items;
    updateListItemByType(listItem, listIndex: listIndex);
    try {
      final result = await listRepository.changeStatusItemInList(items[itemIndex].uuid ?? '', newStatus);
      if (result != null) {
        items[itemIndex] = result;
      } else {
        items[itemIndex].status = originalStatus;
      }
      updateListItemByType(listItem, listIndex: listIndex);
    } catch (e) {
      items[itemIndex].status = originalStatus;
      updateListItemByType(listItem, listIndex: listIndex);
    }
  }

  void updateListItemByType(ListItem listItem, {int? listIndex}) {
    final index = listIndex ?? state.listItems.indexWhere((e) => e.uuid == listItem.uuid);
    if (index != -1) {
      final list = <ListItem>[...(state.listItems)];
      list[index] = listItem;
      emit(state.copyWith(listItems: list, count: state.count + 1));
    }
  }

  void upsertActivity(ActivityModel? activity) {
    final acts = [...state.activityList];
    final index = state.activityList.indexWhere((e) => e.uuid == activity?.uuid);
    if (index != -1) {
      acts[index] = activity!;
    } else {
      acts.add(activity!);
    }
    emit(state.copyWith(activityList: acts));
  }

  void deleteActivity(String id) {
    final acts = [...state.activityList];
    final index = state.activityList.indexWhere((e) => e.uuid == id);
    if (index != -1) {
      acts.removeAt(index);
    }
    emit(state.copyWith(activityList: acts));
  }

  void deleteListItem(String id) {
    final items = [...state.listItems];
    final activities = [...state.activityList];
    final index = state.listItems.indexWhere((e) => e.uuid == id);
    if (index != -1) {
      items.removeAt(index);
    }

    for (var i = 0; i < activities.length; i++) {
      activities[i].includedLists?.removeWhere((e) => e.uuid == id);
    }
    emit(state.copyWith(listItems: items, activityList: activities));
  }

  void onRemoveEvent(String eventId) {
    final activities = [...state.activityList];

    for (var i = 0; i < activities.length; i++) {
      activities[i].includedEvents?.removeWhere((e) => e.uuid == eventId);
    }
    emit(state.copyWith(activityList: activities));
  }

  void updateActivityInListItem(List<String> listItemIds, ActivityModel activity) {
    final list = [...state.listItems];
    for (final id in listItemIds) {
      final index = list.indexWhere((e) => e.uuid == id);
      if (index != -1) {
        list[index].setActivity(activity);
      }
    }
    emit(state.copyWith(listItems: list));
  }

  void onRemoveActivityIdInList(List<String> listItemIds) {
    final list = [...state.listItems];
    for (final id in listItemIds) {
      final index = list.indexWhere((e) => e.uuid == id);
      if (index != -1) {
        list[index].setActivity(null);
      }
    }
    emit(state.copyWith(listItems: list));
  }

  void onTaskCardIndexChanged(int index) {
    emit(state.copyWith(taskCardIndex: index));
  }

  void _setupIsolated() async {
    IsolateNameServer.removePortNameMapping(AppConfig.notificationRoleReceivePort);
    IsolateNameServer.registerPortWithName(_receivePort.sendPort, AppConfig.notificationRoleReceivePort);

    _receivePort.listen((valueData) async {
      if (valueData is! Map<String, dynamic>) return;
      try {
        final message = RemoteMessage.fromMap(valueData);
        if (message.data['type'] == 'role') {
          await accountService.initMyProfile();
        }
      } catch (e) {
        // No-op
      }
    });
  }

  void onLogout() async {
    await signOutUsecase.call();
    emit(state.copyWith(status: HomeStatus.logoutSuccess));
  }

  Future<void> initializeUserCountry() async {
    final country = await LocationService.getUserCountry();
    emit(state.copyWith(
      userCountry: country,
    ));

    // Fetch holidays after country is detected
    await fetchHolidaysForCurrentMonth(country ?? 'US');
  }

  // New method to fetch holidays
  Future<void> fetchHolidaysForCurrentMonth(String countryCode) async {
    if (_isLoadingHolidays) return;

    _isLoadingHolidays = true;
    emit(state.copyWith(isLoadingHolidays: true));

    try {
      if (kReleaseMode) {
        final now = DateTime.now();
        _currentMonthHolidays = await HolidayService.getHolidays(
          countryCode,
          now.year,
          now.month,
        );
        emit(state.copyWith(
          holidays: _currentMonthHolidays,
          isLoadingHolidays: false,
        ));
      } else {
        // In development, skip the API call and just emit empty or mock data
        emit(state.copyWith(
          holidays: {},
          isLoadingHolidays: false,
        ));
      }
    } catch (e) {
      print('Error fetching holidays in cubit: $e');
      emit(state.copyWith(
        holidays: {},
        isLoadingHolidays: false,
      ));
    } finally {
      _isLoadingHolidays = false;
    }
  }

  // Method to get holiday name for a specific day
  String? getHolidayForDay(int month, int day) {
    final key = '$month-$day';
    return state.holidays?[key];
  }

  onCreateListItem(BuildContext context, bool isViewer, ListType? type) async {
    if (isViewer) {
      showSimpleToast(LocaleKeys.viewer_not_permission_list.tr());
      return;
    }
    // context.router.push(UpsertListItemRoute());


    UpsertListItemParameter? parameter;
    if(type != null) {
      parameter = UpsertListItemParameter(type: type);
    }

    var result = await BottomSheetUtils.showScrollable(context, child: UpsertListItemPage(parameter: parameter,));
    if (result != null && result is ListItem) {
      context.pushRoute(ListDetailRoute(
          parameter: ListDetailParameter(
        type: result.listType,
        listItem: result,
      )));
    }
  }

  refreshUIOnly(){
    emit(state.copyWith(updateUICount: state.updateUICount + 1));
  }

  onTapCheckListItem(BuildContext context, ListItem listItem) {
    context.pushRoute(ListDetailRoute(parameter: ListDetailParameter(type: listItem.listType, listItem: listItem)));
  }

  onTapTodoItem(BuildContext context, Item item) {
    logd("onTapTodoItem: ${item.name} - ${item.uuid}");
    context.pushRoute(ListDetailRoute(parameter: ListDetailParameter(uuid: item.listUuid ?? "")));
  }
}
