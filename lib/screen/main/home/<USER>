import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/data/model/upcoming.dart';
import 'package:flutter/material.dart';

enum HomeStatus { none, loading, error, logoutSuccess }

class HomeState extends BaseState {
  final List<ListItem> listItems; //XXX : i don't know wtf is this.
  final int count;
  final List<ActivityModel> activityList;
  final MemoryModel? latestMemory;
  final String? latestMemoryUser; // XXX : not used
  final Upcoming? upcoming;
  final DateTime currentMonth;
  final HomeStatus status;
  final List<Item> tasksDueToday;
  final List<EventModels> events;
  final List<ThreadFamily> messages;
  final int totalUnreadMessages;

  final List<MemoryModel>? allMemories;
  final String? userCountry;
  final Map<String, String>? holidays;
  final bool? isLoadingHolidays;
  final bool isShowAISuggestion;
  final int updateUICount;


  HomeState({
    this.listItems = const [],
    this.count = 0,
    this.activityList = const [],
    this.latestMemory,
    this.latestMemoryUser,
    this.allMemories,
    this.upcoming,
    required this.currentMonth,
    this.status = HomeStatus.loading,
    this.tasksDueToday = const [],
    this.events = const [],
    this.messages = const [],
    this.totalUnreadMessages = 0,
    this.holidays,
    this.isLoadingHolidays = false,
    this.isShowAISuggestion = true,
    this.updateUICount = 0,
    this.userCountry,
  });

  HomeState copyWith({
    List<ListItem>? listItems,
    // List<ActivityModels>? activityItems,
    int? count,
    List<ActivityModel>? activityList,
    MemoryModel? latestMemory,
    String? latestMemoryUser,
    List<MemoryModel>? allMemories,
    Upcoming? upcoming,
    DateTime? currentMonth,
    HomeStatus? status,
    int taskCardIndex = -1,
    List<Item>? tasksDueToday,
    List<EventModels>? events,
    List<ThreadFamily>? messages,
    int? totalUnreadMessages,
    Map<String, String>? holidays,
    bool? isLoadingHolidays,
    bool? isShowAISuggestion,
    int? updateUICount,
    String? userCountry,
  }) {
    return HomeState(
      listItems: listItems ?? this.listItems,
      // activityItems: activityItems ?? this.activityItems,
      count: count ?? this.count,
      activityList: activityList ?? this.activityList,
      latestMemory: latestMemory ?? this.latestMemory,
      latestMemoryUser: latestMemoryUser ?? this.latestMemoryUser,
      allMemories: allMemories ?? this.allMemories,
      upcoming: upcoming ?? this.upcoming,
      currentMonth: currentMonth ?? this.currentMonth,
      status: status ?? this.status,
      tasksDueToday: tasksDueToday ?? this.tasksDueToday,
      events: events ?? this.events,
      messages: messages ?? this.messages,
      totalUnreadMessages: totalUnreadMessages ?? this.totalUnreadMessages,
      holidays: holidays ?? this.holidays,
      isLoadingHolidays: isLoadingHolidays ?? this.isLoadingHolidays,
      userCountry: userCountry ?? this.userCountry,
      isShowAISuggestion: isShowAISuggestion ?? this.isShowAISuggestion,
      updateUICount: updateUICount ?? this.updateUICount,
    );
  }

  @override
  List<Object?> get props => [
        listItems,
        // activityItems,
        count,
        activityList,
        latestMemory,
        latestMemoryUser,
        upcoming,
        currentMonth,
        status,
        tasksDueToday,
        allMemories,
        events,
        holidays,
        isLoadingHolidays,
        isShowAISuggestion,
        updateUICount,
        userCountry,
      ];
}
