import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';

class HomeCardSubtitle extends StatelessWidget {
  const HomeCardSubtitle({super.key, this.content = '', this.imagePath = ''});

  final String imagePath;
  final String content;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ImageAssetCustom(imagePath: imagePath, size: 16),
        SizedBox(width: 4),
        Text(content, style: AppStyle.regular12(color: appTheme.whiteText))
      ],
    );
  }
}
