import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/timezone.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:family_app/data/usecase/model/upsert_memory_param.dart';
import 'package:family_app/data/usecase/upsert_item_usecase.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/upload.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:image_picker/image_picker.dart';

import 'upsert_sub_item_parameter.dart';
import 'upsert_sub_item_state.dart';

class UpsertSubItemCubit extends BaseCubit<UpsertSubItemState> {
  UpsertSubItemParameter? parameter;
  final IFamilyRepository familyRepository;
  final IListRepository listRepository;
  final UpsertItemUsecase usecase;

  UpsertSubItemCubit({
    this.parameter,
    required this.usecase,
    required this.familyRepository,
    required this.listRepository,
  }) : super(UpsertSubItemState(title: parameter?.item?.name ?? ''));

  final AccountService accountService = locator.get();

  late final TextFieldHandler name;
  late final FormTextFieldHandler form;
  final TextEditingController noteTextEditingController =
    TextEditingController();
  final TextEditingController pointEditingController = TextEditingController();
  final focusNote = FocusNode();

  @override
  void onInit() {
    _initItemData();
    super.onInit();
  }

  _initItemData() async {
    name = TextFieldHandler(
      field: 'name',
      isRequired: true,
      hintText: LocaleKeys.untitled_checklist.tr(),
      initializeText: parameter?.item?.name ?? '',
    );
    form = FormTextFieldHandler(
        handlers: [name], validateForm: (map) => createSubItem());
    if (parameter?.item?.uuid != null) {
      Item? subItem =
          await listRepository.getItemDetail(parameter!.item!.uuid!);
      emit(state.copyWith(item: subItem, assignees: subItem?.assignment));

      String tzString = parameter?.item?.timezone ?? '';
      Timezone? timezone;
      if (tzString.isNotEmpty) {
        timezone =
            listTimezone.firstWhereOrNull((tz) => tz.name == tzString || tz.abbr == tzString || tz.offset == tzString);
        if (timezone != null) {
          emit(state.copyWith(timezone: timezone));
        }
      }
      if (timezone == null) {
        detectAndSetTimezone();
      }

      pointEditingController.text = subItem?.point.toString() ?? '';

      DateTime? dueDate = parameter?.item?.due_date?.convertDateTime;
      if (dueDate != null) {
        emit(state.copyWith(
          isSetDateAndReminder: true,
          dueDate: DateTime(dueDate.year, dueDate.month, dueDate.day),
          time: TimeOfDay(hour: dueDate.hour, minute: dueDate.minute),
        ));

        DateTime? notifDate =
            parameter?.item?.notificationTimeUTC?.convertDateTime;
        if (notifDate != null) {
          int diff = dueDate.difference(notifDate).inMinutes;
          emit(state.copyWith(reminder: diff));
        }
      }

      String? description = parameter?.item?.description;
      if (description != null && description.isNotEmpty) {
        noteTextEditingController.text = description;
        emit(state.copyWith(
          isSetNote: true,
          note: description,
        ));
      }

      final memoryList = await familyRepository.getMemoryListByFamily(accountService.familyId);
      for (var item in memoryList) {
        if (item.activityId == parameter?.item?.uuid && item.attachmentType == 'list_item') {
          emit(state.copyWith(
            memory: item,
            isSetPhoto: true,
          ));
          break;
        }
      }
    } else {
      var assignees = parameter?.listItem?.assignment ?? [];
      if (assignees.isEmpty) {
        Account? my = accountService.memberInFamily.value.firstWhereOrNull(
            (element) => element.familyMemberUuid == accountService.account?.uuid);
        if (my != null) {
          assignees.add(my);
        }
      }
      emit(state.copyWith(
        assignees: assignees,
      ));
      detectAndSetTimezone();
    }
  }

  void detectAndSetTimezone() {
    final userTimezoneName = DateTime.now().timeZoneName;
    final userTimezoneOffset =
        _formatTimezoneOffset(DateTime.now().timeZoneOffset);

    logd("detectAndSetTimezone Timezone name: $userTimezoneName");
    logd("detectAndSetTimezone Timezone offset: $userTimezoneOffset");

    final defaultTimezone = listTimezone.firstWhere(
      (tz) =>
          tz.name == userTimezoneName ||
          tz.abbr == userTimezoneName ||
          tz.offset == userTimezoneOffset,
      orElse: () {
        return listTimezone.first;
      },
    );

    emit(state.copyWith(timezone: defaultTimezone));
  }

  String _formatTimezoneOffset(Duration offset) {
    final hours = offset.inHours;
    final minutes = offset.inMinutes.remainder(60).abs();
    final sign = offset.isNegative ? '-' : '+';
    final hoursFormatted = hours.abs().toString();
    final minutesFormatted =
        minutes > 0 ? ':${minutes.toString().padLeft(2, '0')}' : '';
    return 'UTC$sign$hoursFormatted$minutesFormatted';
  }

  Future<void> createSubItem() async {
    logd("createSubItem");
    name.validateField();
    if (name.isValid == false) {
      logd('createSubItem ${name.field} is not valid');
      return;
    }

    logd("createSubItem name: ${name.text}");
    logd("createSubItem timezone offset: ${state.timezone!.offset}");
    showLoading();
    UpsertItemParam param = UpsertItemParam(
      itemId: parameter?.item?.uuid,
      familyUuid: accountService.familyId,
      listUuid: parameter?.listUuid,
      name: name.text,
      point: int.tryParse(pointEditingController.text) ?? 0,
      // includedMembers: state.assignees.map((e) => e.familyMemberUuid!).toList(),
      assignment: state.assignees.map((e) => e.familyMemberUuid!).toList(),
    );

    if (state.dueDate != null && state.time != null) {
      param = param.copyWith(timeZone: state.timezone?.offset);
      var dueDate = DateTime(
        state.dueDate!.year,
        state.dueDate!.month,
        state.dueDate!.day,
        state.time!.hour,
        state.time!.minute,
      );
      param = param.copyWith(due_date: dueDate.toUtc().toIso8601String(), listCategory: '');
      if (state.reminder != null && state.reminder! > 0) {
        var notifDate = dueDate.subtract(Duration(minutes: state.reminder ?? 0));
        param = param.copyWith(notificationTime: notifDate.toUtc().toIso8601String());
      }
    }else {
      param = param.copyWith(due_date: DateTime.now().toUtc().toIso8601String());
    }

    if (noteTextEditingController.text.isNotEmpty) {
      param = param.copyWith(description: noteTextEditingController.text);
    }

    logd("createSubItem param: $param");
    Item? updatedItem;
    if (parameter?.item?.uuid != null) {
      updatedItem =
          await listRepository.updateItemInList(parameter!.item!.uuid!, param);
    } else {
      updatedItem = await listRepository.createItemInList(param);
    }
    HomeCubit homeCubit = locator.get();
    homeCubit.onRefresh();

    if (updatedItem == null) {
      dismissLoading();
      return;
    }

    if (state.photo != null) {
      StorageModel? storageModel;
      final file = File(state.photo!);
      logd('Uploading file: ${file.path}');
      storageModel = await uploadImage(file);
      if (storageModel.uuid?.isNotEmpty == true) {
        logd('Uploaded file succeeded: ${storageModel.fileName}');
        final attachStorageParams = const UpsertMemoryParam().copyWith(
          familyId: accountService.familyId,
          activityId: updatedItem.uuid,
          caption: updatedItem.name,
          attachmentType: 'list_item',
          fileId: [storageModel.uuid!],
        );
        final resultAttachStorageToActivity = await familyRepository.attachStorageToActivity(attachStorageParams);
        logd('Result of attach storage: $resultAttachStorageToActivity');
      } else {
        logd('Error uploading file: ${file.path}');
      }
    }

    if (state.removeMemory?.uuid != null) {
      await familyRepository.removeStorageFromMemory(state.removeMemory!.uuid!);
    }

    await listRepository.getListByFamilyId(accountService.familyId);

    dismissLoading();
    emit(state.copyWith(item: updatedItem, updated: true));
  }

  Future<StorageModel> uploadImage(File imageFile) async {
    try {
      final upload = Upload(
        familyId: accountService.familyId,
      );
      final storageModel = await upload.uploadImage(imageFile, null);
      if (storageModel.uuid != null) {
        AppLogger.d('Image uploaded successfully.');
        return storageModel;
      } else {
        AppLogger.d('Image upload failed.');
      }
    } catch (e) {
      AppLogger.d('Image upload error: $e');
    }
    return const StorageModel();
  }

  updateMemberSelected(List<Account> p1) {
    emit(state.copyWith(assignees: p1));
  }

  onSetDateAndReminder() {
    emit(state.copyWith(isSetDateAndReminder: !state.isSetDateAndReminder));
  }

  onSelectDate(BuildContext context) {
    showDatePicker(
      context: context,
      initialDate: state.dueDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    ).then((value) {
      if (value != null) {
        emit(state.copyWith(dueDate: value));
      }
    });
  }

  onSelectTime(BuildContext context) async {
    final TimeOfDay? timePicked = await showTimePicker(
      context: context,
      initialEntryMode: TimePickerEntryMode.input,
      initialTime: state.time ?? TimeOfDay.now(),
    );
    if (timePicked != null) {
      emit(state.copyWith(time: timePicked));
    }
  }

  handleTimezone(Timezone v) {
    emit(state.copyWith(timezone: v));
  }

  onChangeReminder(int? value) {
    emit(state.copyWith(reminder: value));
  }

  onExpandNotSet() {
    emit(state.copyWith(isSetNote: !state.isSetNote));
  }

  onExpandPhotoSet() {
    emit(state.copyWith(isSetPhoto: !state.isSetPhoto));
  }

  onPickPhoto(ImageSource value) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: value);
    if (image != null) {
      emit(state.clearPhoto());
      emit(state.copyWith(photo: image.path, isSetPhoto: true));
    }
  }

  onDeletePhoto() {
    emit(state.clearPhoto());
  }
}
