import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_detail_parameter.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_wishlists_upsert_screen.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'birthday_registry_detail_cubit.dart';
import 'birthday_registry_detail_state.dart';

@RoutePage()
class BirthdayRegistryDetailPage extends BaseBlocProvider<
    BirthdayRegistryDetailState, BirthdayRegistryDetailCubit> {
  const BirthdayRegistryDetailPage({required this.parameter, super.key});

  final BirthdayRegistryDetailParameter parameter;

  @override
  Widget buildPage() => const BirthdayRegistryDetailView();

  @override
  BirthdayRegistryDetailCubit createCubit() => BirthdayRegistryDetailCubit(
      activityRepository: locator.get(), parameter: parameter);
}

class BirthdayRegistryDetailView extends StatefulWidget {
  const BirthdayRegistryDetailView({super.key});

  @override
  State<BirthdayRegistryDetailView> createState() =>
      _BirthdayRegistryDetailViewState();
}

class _BirthdayRegistryDetailViewState extends BaseBlocPageState<
    BirthdayRegistryDetailView,
    BirthdayRegistryDetailState,
    BirthdayRegistryDetailCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  void initState() {
    super.initState();
    isTopSafeArea = false;
  }

  @override
  bool listenWhen(BirthdayRegistryDetailState previous,
      BirthdayRegistryDetailState current) {
    switch (current.status) {
      case BirthdayRegistryDetailStatus.loading:
        showLoading();
        break;
      default:
        dismissLoading();
        break;
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, BirthdayRegistryDetailCubit cubit,
      BirthdayRegistryDetailState state) {
    return Container(
        margin: const EdgeInsets.all(8),
        child: ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(MediaQuery.of(context).viewPadding.top > 0
                ? MediaQuery.of(context).viewPadding.top
                : 24),
            topRight: Radius.circular(MediaQuery.of(context).viewPadding.top > 0
                ? MediaQuery.of(context).viewPadding.top
                : 24),
            bottomLeft: const Radius.circular(24),
            bottomRight: const Radius.circular(24),
          ),
          child: Stack(
            children: [
              Column(children: [
                AspectRatio(
                  aspectRatio: 4.0 / 3.0, // Keep the width-height ratio
                  child: Stack(
                    children: [
                      Container(
                        width: double.infinity, // Match the width of the screen
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: (state.activity?.imagePath != null &&
                                    state.activity?.imagePath?.isNotEmpty ==
                                        true)
                                ? CachedNetworkImageProvider(
                                    state.activity?.imagePath ?? '')
                                : Image.asset(Assets
                                        .images.birthdayRegistryCover.path)
                                    .image,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withOpacity(0.0),
                              Colors.black.withOpacity(0.8),
                            ],
                            stops: [0.5, 1],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ]),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        state.activity?.name ?? '',
                        style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 20.0),
                      ),
                      Text(
                        state.activity?.fromDate?.toLocalDT.MMM_d_yyyy ?? '',
                        style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.normal,
                            fontSize: 16.0),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                top: 44,
                left: 4,
                right: 4,
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CircleItem(
                          onTap: () => context.popRoute(),
                          padding: padding(all: 2),
                          backgroundColor: appTheme.blackColor.withOpacity(.2),
                          child: Transform.rotate(
                            angle: pi,
                            child: Assets.icons.arrowRight.svg(
                                width: 32.w,
                                height: 32.w,
                                colorFilter: const ColorFilter.mode(
                                    Colors.white, BlendMode.srcIn)),
                          )),
                      /* TODO: Nguyen disable sharing for now
                      const Expanded(child: SizedBox()),
                      CircleItem(
                        onTap: () {
                          // Handle map icon press
                        },
                        padding: padding(all: 8),
                        backgroundColor: appTheme.blackColor.withOpacity(.2),
                        child: const Icon(Icons.map,
                            color: Colors.white, size: 22),
                      ),
                      const SizedBox(width: 4),
                      CircleItem(
                        onTap: () {
                          // Handle share icon press
                        },
                        padding: padding(all: 8),
                        backgroundColor: appTheme.blackColor.withOpacity(.2),
                        child: const Icon(Icons.share,
                            color: Colors.white, size: 22),
                      ),
                      const SizedBox(width: 4),
                      CircleItem(
                        onTap: () {
                          // Handle settings icon press
                          // _goToEditTrip(state);
                        },
                        padding: padding(all: 8),
                        backgroundColor: appTheme.blackColor.withOpacity(.2),
                        child: const Icon(Icons.settings,
                            color: Colors.white, size: 22),
                      ),
                     */
                    ]),
              ),
            ],
          ),
        ));
  }

  @override
  Widget buildBody(BuildContext context, BirthdayRegistryDetailCubit cubit,
      BirthdayRegistryDetailState state) {
    if (state.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    return DefaultTabController(
      length: state.tabs.length,
      initialIndex: state.selectedIndex,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 50,
            child: _buildTabHeader(cubit, state),
          ),
          Expanded(
            child: TabBarView(
              children: state.tabs.asMap().entries.map((entry) {
                int tabIndex = entry.key;
                /* return tabIndex == 0
                    ? _buildSchedule(cubit, state)
                    : _buildWishLists(cubit, state); */
                return _buildWishLists(cubit, state);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabHeader(
      BirthdayRegistryDetailCubit cubit, BirthdayRegistryDetailState state) {
    return TabBar(
      onTap: (value) => cubit.setSelectedIndex(value),
      isScrollable: true,
      padding: const EdgeInsets.all(8),
      indicator: BoxDecoration(
        // Customize the indicator
        color: const Color(0x1F4E46B4), // Indicator background color
        borderRadius: BorderRadius.circular(20), // Rounded corners
        border: Border.all(color: const Color(0x1F4E46B4)), // Border color
      ),
      splashBorderRadius: BorderRadius.circular(20),
      dividerColor: Colors.transparent,
      indicatorSize: TabBarIndicatorSize.tab,
      indicatorPadding: EdgeInsets.zero,
      indicatorWeight: 2.0,
      tabAlignment: TabAlignment.start,
      tabs: state.tabs.asMap().entries.map((entry) {
        int tabIndex = entry.key;
        return _buildTabHeaderItemView(cubit, state, tabIndex);
      }).toList(),
    );
  }

  Widget _buildTabHeaderItemView(BirthdayRegistryDetailCubit cubit,
      BirthdayRegistryDetailState state, int index) {
    final isSelected = index == state.selectedIndex;
    final label = state.tabs[index];
    return Tab(
      child: Text(label,
          style: TextStyle(
            color:
                isSelected ? const Color(0xFF4E46B4) : const Color(0xFF595D62),
            fontWeight: FontWeight.bold,
          )),
    );
  }

  Widget _buildSchedule(
      BirthdayRegistryDetailCubit cubit, BirthdayRegistryDetailState state) {
    final scheduleList = state.scheduleActivities;
    final count = scheduleList.length;
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: count + 1, // Add 1 for the "Add new item"
      itemBuilder: (context, index) {
        if (index == count) {
          return _buildAddNewScheduleTile(context, cubit, state);
        }

        final activity = scheduleList[index];
        return _buildActivityTimelineTile(
            context, cubit, state, activity, index, count);
      },
    );
  }

  Widget _buildAddNewScheduleTile(BuildContext context,
      BirthdayRegistryDetailCubit cubit, BirthdayRegistryDetailState state) {
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32,
        height: 32,
        color: Colors.white,
        indicator: Container(
          padding: padding(left: 3),
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            // color: Colors.white, // Background color for the add icon
          ),
          child:
              Assets.icons.icTimelineAdd.svg(width: 32, height: 32), // Add icon
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.alto200,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding: const EdgeInsets.only(left: 8, right: 8, top: 4, bottom: 4),
        child: SizedBox(
          height: 40,
          child: Align(
            alignment: Alignment.centerLeft,
            child: TextButton(
              onPressed: () {
                // BottomSheetUtils.showHeightReturnBool(context,
                //         height: 0.3,
                //         child: TripSelectActivityTypeBts(
                //             parameter: TripSelectActivityTypeParameter(state.activity!, dayIndex)))
                //     .then((value) {
                //   AppLogger.d('Add new item pressed return value: $value');
                //   if (value == true) {
                //     // Reload the page
                //     cubit.fetchBirthdayRegistryDetail();
                //   }
                // });
              },
              child: const Text(
                'Add new place',
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4E46B4)),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActivityTimelineTile(
      BuildContext context,
      BirthdayRegistryDetailCubit cubit,
      BirthdayRegistryDetailState state,
      Activity activity,
      int timelineIndex,
      int timelineLength) {
    final isHotel = activity.description.toLowerCase().contains('hotel') ||
        activity.description.toLowerCase().contains('hostel');
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32, // Increased indicator size
        height: 32,
        // padding: const EdgeInsets.all(4),
        color: Colors.white,
        indicator: Container(
          padding: padding(left: 3),
          // Custom indicator with icon
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            // color: Colors.white, // Or your icon background color
          ),
          child: isHotel
              ? Assets.icons.icHotel.svg(width: 32, height: 32)
              : Assets.icons.icPosition.svg(width: 32, height: 32),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.alto200,
        thickness: 2, // Adjust line thickness
      ),
      // isFirst: index == 0, // Mark the first item
      // isLast: index == length - 1, // Mark the last item
      lineXY: 0.09,
      // Adjust this value (0.0 to 1.0) to position the line
      endChild: Padding(
        padding: const EdgeInsets.only(left: 8, right: 8, top: 8, bottom: 8),
        child: SizedBox(
          child: _buildActivityItemView2(
              context, cubit, state, activity, timelineIndex),
        ),
      ),
    );
  }

  Widget _buildActivityItemView2(
      BuildContext context,
      BirthdayRegistryDetailCubit cubit,
      BirthdayRegistryDetailState state,
      Activity activity,
      int timelineIndex) {
    final imageUrl = activity.activityImage;
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: appTheme.whiteText,
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            SizedBox(
              width: 130,
              child: AspectRatio(
                aspectRatio: 96.0 / 74.0,
                child: Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: imageUrl?.isEmpty == false
                          ? CachedNetworkImageProvider(imageUrl!)
                          : Image.asset(timelineIndex == 0
                                  ? Assets.demoImages.image5.path
                                  : Assets.demoImages.image6.path)
                              .image,
                      fit: BoxFit.cover,
                    ),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              // Ensure the text description is inside the parent view
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                // Center the text vertically
                children: [
                  Text(
                    activity.description,
                    style: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.visible, // Ensure text is visible
                  ),
                  // const SizedBox(height: 4),
                  _buildLocationView(context, cubit, state, activity),
                  // const SizedBox(height: 4),
                  _buildTimeView(context, cubit, state, activity),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationView(
      BuildContext context,
      BirthdayRegistryDetailCubit cubit,
      BirthdayRegistryDetailState state,
      Activity activity) {
    return ListTile(
      onTap: () {
        // Open map view
        AppLogger.d('On location pressed');
      },
      contentPadding: EdgeInsets.zero,
      minTileHeight: 0,
      minVerticalPadding: 4,
      minLeadingWidth: 0,
      horizontalTitleGap: 8,
      leading: Assets.icons.icLocation.svg(width: 16, height: 16),
      title: Text(
        activity.venue ?? '',
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  Widget _buildTimeView(BuildContext context, BirthdayRegistryDetailCubit cubit,
      BirthdayRegistryDetailState state, Activity activity) {
    final isHotel = activity.description.toLowerCase().contains('hotel') ||
        activity.description.toLowerCase().contains('hostel');
    return Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isHotel ? 'Check in' : 'Start time',
            style: const TextStyle(fontSize: 12),
          ),
          // const SizedBox(height: 4),
          Text(
            activity.time == null
                ? ''
                : (activity.time == 'AM' ? '10:00' : '16:00'),
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ],
      ),
      const SizedBox(width: 16),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isHotel ? 'Check out' : 'End time',
            style: const TextStyle(fontSize: 12),
          ),
          // const SizedBox(height: 4),
          Text(
            activity.time == null
                ? ''
                : (activity.time == 'AM' ? '12:00' : '18:00'),
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    ]);
  }

  Widget _buildWishLists(
      BirthdayRegistryDetailCubit cubit, BirthdayRegistryDetailState state) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: state.wishlists!.length + 1, // Add 1 for the "Add new item"
      itemBuilder: (context, index) {
        // Add new item is the first item
        if (index == 0) {
          return _buildAddNewWishListItem(cubit, state);
        }
        return _buildWishListItem(cubit, state, state.wishlists![index - 1]);
      },
    );
  }

  Widget _buildAddNewWishListItem(
      BirthdayRegistryDetailCubit cubit, BirthdayRegistryDetailState state) {
    void addNewItem() {
      BirthdayRegistryWishlistsUpsertBts.show(context, state.activity!)
          .then((result) {
        if (result == true) {
          cubit.fetchWishlists();
        }
      });
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () {
          addNewItem();
        },
        borderRadius: BorderRadius.circular(16), // Rounded corners
        child: DottedBorder(
          padding: const EdgeInsets.all(8),
          // Adjust padding as needed
          borderType: BorderType.RRect,
          // Rounded corners
          radius: const Radius.circular(16),
          // Rounded corners
          color: Colors.grey.withOpacity(0.20),
          // Dotted line color
          dashPattern: const [4, 4],
          // Dotted line pattern
          child: Align(
            alignment: Alignment.centerLeft,
            child: Row(
              mainAxisSize: MainAxisSize.min, // Make Row take only needed space
              children: [
                Checkbox(
                  value: false, // Initial checkbox state
                  onChanged: (bool? value) {
                    // Handle checkbox state change if needed
                  },
                  materialTapTargetSize:
                      MaterialTapTargetSize.shrinkWrap, // Shrink tap target
                ),
                const SizedBox(width: 8),
                // Add spacing between checkbox and text
                Text(
                  'New item',
                  style: TextStyle(
                    color: appTheme.grayV2, // Text color
                    fontSize: 16, // Adjust font size as needed
                    fontWeight: FontWeight.bold, // Bold font weight
                  ),
                ),
                const Spacer(),
                // Push the add icon to the right
                IconButton(
                  icon: const Icon(
                    Icons.add,
                    color: Colors.deepPurple, // Add icon color
                  ),
                  onPressed: () {
                    addNewItem();
                  },
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWishListItem(BirthdayRegistryDetailCubit cubit,
      BirthdayRegistryDetailState state, Item? wishlistsItem) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      // Adjust padding as needed
      child: InkWell(
        onTap: () {
          if (state.activity != null) {
            if (wishlistsItem.uuid == null) {
              final activity = state.activity!; // Cache the value
              BirthdayRegistryWishlistsUpsertBts.show(context, activity,
                      wishlistsItem: wishlistsItem)
                  .then((result) {
                if (result == true) {
                  cubit.fetchWishlists();
                }
              });
            } else {
              cubit.upsertActivityItemStatus(
                  wishlistsItem, !(wishlistsItem.status == 1));
            }
          }
        },
        borderRadius: BorderRadius.circular(16), // Rounded corners
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16), // Rounded corners
            color: Colors.grey.withOpacity(0.10),
          ),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Row(
              mainAxisSize: MainAxisSize.min, // Make Row take only needed space
              children: [
                Checkbox(
                  value: wishlistsItem!.status == 1, // Initial checkbox state
                  onChanged: (bool? value) {
                    cubit.upsertActivityItemStatus(wishlistsItem, value);
                  },
                  materialTapTargetSize:
                      MaterialTapTargetSize.shrinkWrap, // Shrink tap target
                ),
                const SizedBox(width: 8),
                // Add spacing between checkbox and text
                Expanded(
                  child: Text(
                    wishlistsItem.name!,
                    style: TextStyle(
                      color: Colors.black87, // Text color
                      fontSize: 16, // Adjust font size as needed
                      fontWeight: FontWeight.bold, // Bold font weight
                      decoration: wishlistsItem.status == 1
                          ? TextDecoration.lineThrough
                          : TextDecoration.none,
                    ),
                    maxLines: 3,
                  ),
                ),
                IconButton(
                    icon: const Icon(Icons.delete, color: Colors.black87),
                    onPressed: () => {
                          _showDeleteConfirmationDialog(
                              context, cubit, wishlistsItem),
                        }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context,
      BirthdayRegistryDetailCubit cubit, Item wishlistsItem) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete item'),
          content: const Text('Are you sure you want to delete this item?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Delete'),
              onPressed: () {
                Navigator.of(context).pop();
                cubit.deleteActivityItem(wishlistsItem);
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget? buildFloatingActionButton(BuildContext context,
      BirthdayRegistryDetailCubit cubit, BirthdayRegistryDetailState state) {
    return ElevatedButton(
      onPressed: () {
        AppLogger.d("Ask AI button to edit birthday registry");
        _goToEditBirthdayRegistry(cubit, state);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF4E46B4),
        // Background color (purple)
        foregroundColor: Colors.white,
        // Text and icon color
        textStyle: const TextStyle(
          fontSize: 16, // Adjust the font size as needed
          fontWeight: FontWeight.w500, // Medium fontWeight
        ),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        // Adjust padding as needed
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20), // Rounded corners
        ),
        elevation: 3, // Add a subtle elevation (shadow)
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        // Ensure the Row takes only the space it needs
        children: [
          Assets.icons.icLightningBolt.svg(
            height: 20, // Adjust icon size
            width: 20, // Ensure the icon is white
          ),
          const SizedBox(width: 8), // Spacing between icon and text
          Text(LocaleKeys.ask_ai.tr()),
        ],
      ),
    );
  }

  Future<void> _goToEditBirthdayRegistry(BirthdayRegistryDetailCubit cubit,
      BirthdayRegistryDetailState state) async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      context.pushRoute(ChatRoute(
          parameter: ChatParameter(
              chatContext:
                  ChatContext.getEditEventContext(token, state.activityId))));
    }
  }
}
