import 'dart:convert';
import 'dart:io';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/usecase/model/activity_parameter.dart';
import 'package:family_app/data/usecase/model/upsert_memory_param.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_upsert_state.dart';
import 'package:family_app/screen/main/home/<USER>/activity_home_cubit.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/upload.dart';

class BirthdayRegistryUpsertCubit
    extends BaseCubit<BirthdayRegistryUpsertState> {
  final IActivityRepository activityRepository;
  final IFamilyRepository familyRepository;
  final ActivityHomeCubit activityCubit = locator.get();

  BirthdayRegistryUpsertCubit(
      {required this.activityRepository, required this.familyRepository})
      : super(BirthdayRegistryUpsertState());

  final AccountService accountService = locator.get();

  @override
  Future<void> close() {
    locator.unregister<BirthdayRegistryUpsertCubit>();
    return super.close();
  }

  @override
  void onInit() {
    locator.registerSingleton(this);
    super.onInit();
    _initData();
  }

  Future<void> _initData() async {
    emit(state.copyWith(status: BirthdayRegistryUpsertStatus.loading));
    final members =
        await familyRepository.getUserInFamily(accountService.familyId);
    emit(state.copyWith(status: BirthdayRegistryUpsertStatus.done, members:
    members));
  }

  void updateCoverImageFile(String? path) {
    emit(state.updateCoverImageFilePath(path));
  }

  void updateCaption(String? caption) {
    bool captionValid = false;
    if (caption?.isEmpty ?? true) {
      captionValid = false;
    } else {
      captionValid = true;
    }
    emit(state.updateCaption(caption, captionValid));
  }

  void updateMember(Account? member) {
    emit(state.copyWith(selectedMember: member, isMemberValid: member != null));
  }

  void updateDateTime(DateTime? dateTime) {
    emit(state.copyWith(dateTime: dateTime, isDateTimeValid: dateTime != null));
  }

  bool _validateFields() {
    bool isCaptionValid = false;
    bool isMemberValid = false;
    bool isDateTimeValid = false;

    if (state.caption?.isEmpty ?? true) {
      AppLogger.d('Birthday registry caption is empty');
      isCaptionValid = false;
    } else {
      isCaptionValid = true;
    }

    isMemberValid = state.selectedMember != null;
    if (!isMemberValid) {
      AppLogger.d('Member is empty');
    }

    isDateTimeValid = state.dateTime != null;
    if (!isDateTimeValid) {
      AppLogger.d('Departure date is empty');
    }

    emit(state.copyWith(
      isCaptionValid: isCaptionValid,
      isMemberValid: isMemberValid,
      isDateTimeValid: isDateTimeValid,
    ));
    return isCaptionValid && isMemberValid && isDateTimeValid;
  }

  Future<void> saveBirthdayRegistry() async {
    if (!_validateFields()) {
      return;
    }
    emit(state.copyWith(status: BirthdayRegistryUpsertStatus
        .loading));
    try {
      // Upload files
      StorageModel? storageModel;
      if (state.coverImageFilePath != null) {
        final file = File(state.coverImageFilePath!);
        AppLogger.d('Uploading file: ${file.path}');
        storageModel = await uploadImage(file);
        if (storageModel.uuid?.isNotEmpty == true) {
          AppLogger.d('Uploaded file succeeded: ${storageModel.fileName}');
        } else {
          AppLogger.d('Error uploading file: ${file.path}');
        }
      }

      // Get list of uuids from storage models
      List<String>? attachmentUuids;
      if (storageModel?.uuid?.isNotEmpty == true) {
        attachmentUuids = [storageModel!.uuid!];
      }

      // Create activity parameter
      final param = toCreateActivityParameter();
      final activityParams = param.copyWith(
        attachments: attachmentUuids,
        includedMembers: [state.selectedMember!.uuid!],
      );

      // Save activity
      AppLogger.d('Create birthday registry param: ${jsonEncode(activityParams)}');
      final result = await activityRepository.createActivity(activityParams);
      AppLogger.d('Create birthday registry result: $result');
      final attachStorageParams = const UpsertMemoryParam().copyWith(
        familyId: accountService.familyId,
        activityId: result.uuid,
        caption: result.name,
        attachmentType: 'birthday_registry',
        fileId: attachmentUuids,
      );
      final resultAttachStorageToActivity = await familyRepository.attachStorageToActivity(attachStorageParams);
      AppLogger.d('Result of attach storage: $resultAttachStorageToActivity');
      emit(state.copyWith(status: BirthdayRegistryUpsertStatus.success));
      activityCubit.fetchActivity();
    } catch (e) {
      AppLogger.e('Error saving transfer: $e');
      emit(state.copyWith(status: BirthdayRegistryUpsertStatus.error));
    }
    finally {
      emit(state.copyWith(status: BirthdayRegistryUpsertStatus.done));
    }
  }

  Future<StorageModel> uploadImage(File imageFile) async {
    try {
      final upload = Upload(
        familyId: accountService.familyId,
      );
      final storageModel = await upload.uploadImage(imageFile, null);
      if (storageModel.uuid != null) {
        AppLogger.d('Image uploaded successfully.');
        return storageModel;
      } else {
        AppLogger.d('Image upload failed.');
      }
    } catch (e) {
      AppLogger.d('Image upload error: $e');
    }
    return const StorageModel();
  }

  CreateActivityParameter toCreateActivityParameter() {
    final dateTime = state.dateTime!;
    final birthdayRegistryDate = DateTime.utc(dateTime.year, dateTime.month, dateTime.day, 0, 0).toUtc().toIso8601String();
    return CreateActivityParameter(
      name: state.caption!,
      fromDate: birthdayRegistryDate,
      toDate: birthdayRegistryDate,
      caption: '',
      description: state.caption,
      color: '#133483',
      activityType: 'birthday_registry',
      familyId: accountService.familyId,
    );
  }
}
