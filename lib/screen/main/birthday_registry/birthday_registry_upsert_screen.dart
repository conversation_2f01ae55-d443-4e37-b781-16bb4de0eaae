import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_upsert_cubit.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_upsert_state.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';

@RoutePage()
class BirthdayRegistryUpsertPage extends BaseBlocProvider<
    BirthdayRegistryUpsertState, BirthdayRegistryUpsertCubit> {
  const BirthdayRegistryUpsertPage({super.key});

  @override
  Widget buildPage() => const BirthdayRegistryUpsertView();

  @override
  BirthdayRegistryUpsertCubit createCubit() => BirthdayRegistryUpsertCubit(
      activityRepository: locator.get(), familyRepository: locator.get());
}

class BirthdayRegistryUpsertView extends StatefulWidget {
  const BirthdayRegistryUpsertView({super.key});

  @override
  State<BirthdayRegistryUpsertView> createState() =>
      _BirthdayRegistryUpsertViewState();
}

class _BirthdayRegistryUpsertViewState extends BaseBlocPageState<
    BirthdayRegistryUpsertView,
    BirthdayRegistryUpsertState,
    BirthdayRegistryUpsertCubit> {
  @override
  bool? get isBottomSafeArea => false;

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool listenWhen(BirthdayRegistryUpsertState previous,
      BirthdayRegistryUpsertState current) {
    switch (current.status) {
      case BirthdayRegistryUpsertStatus.loading:
        showLoading();
        break;
      case BirthdayRegistryUpsertStatus.success:
        dismissLoading();
        Navigator.of(context).pop(true);
        break;
      default:
        dismissLoading();
        break;
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, BirthdayRegistryUpsertCubit cubit,
      BirthdayRegistryUpsertState state) {
    return CustomAppBar2(
      title: 'New birthday registry',
      showBack: true,
      actions: [
        GestureDetector(
          onTap: () {
            AppLogger.d('Save birthday registry pressed');
            cubit.saveBirthdayRegistry();
          },
          behavior: HitTestBehavior.opaque,
          child: CircleItem(
            backgroundColor: appTheme.backgroundV2,
            padding: padding(all: 8),
            child: SvgPicture.asset(Assets.icons.icActionCheck.path),
          ),
        )
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, BirthdayRegistryUpsertCubit cubit,
      BirthdayRegistryUpsertState state) {
    return _buildBodyContainer(context, cubit, state);
  }

  Widget _buildBodyContainer(BuildContext context,
      BirthdayRegistryUpsertCubit cubit, BirthdayRegistryUpsertState state) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.0),
      ),
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _buildCardBody(context, cubit, state),
      ),
    );
  }

  Widget _buildCardBody(BuildContext context, BirthdayRegistryUpsertCubit cubit,
      BirthdayRegistryUpsertState state) {
    return Column(
      children: [
        _buildCover(context, cubit, state),
        const SizedBox(height: 24),
        _buildCaption(context, cubit, state),
        const SizedBox(height: 24),
        _buildMember(context, cubit, state),
        const SizedBox(height: 24),
        _buildDate(context, cubit, state),
      ],
    );
  }

  Widget _buildCover(BuildContext context, BirthdayRegistryUpsertCubit cubit,
      BirthdayRegistryUpsertState state) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        color: Colors.white,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: AspectRatio(
          aspectRatio: 16.0 / 9.0,
          child: Stack(
            children: [
              Center(
                child: state.coverImageFilePath != null
                    ? Image.file(
                        File(state.coverImageFilePath!),
                        fit: BoxFit.fitWidth,
                        width: double.infinity,
                      )
                    : Image.asset(
                        Assets.images.birthdayRegistryCover.path,
                        fit: BoxFit.fitWidth,
                        width: double.infinity,
                      ),
              ),
              Positioned(
                left: 8,
                bottom: 8,
                child: state.coverImageFilePath != null
                    ? _buildEditCover(context, cubit, state)
                    : _buildUploadCover(context, cubit, state),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUploadCover(BuildContext context,
      BirthdayRegistryUpsertCubit cubit, BirthdayRegistryUpsertState state) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () async {
          AppLogger.d('Upload button pressed');
          showImagePicker(context, cubit, state);
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: .5),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            LocaleKeys.upload_cover.tr(),
            style: AppStyle.regular14V2(color: appTheme.whiteText),
          ),
        ),
      ),
    );
  }

  Widget _buildEditCover(BuildContext context,
      BirthdayRegistryUpsertCubit cubit, BirthdayRegistryUpsertState state) {
    return Material(
      color: Colors.transparent,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            onTap: () {
              AppLogger.d('Change button pressed');
              showImagePicker(context, cubit, state);
            },
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: .5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                LocaleKeys.change.tr(),
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () {
              AppLogger.d('Remove button pressed');
              cubit.updateCoverImageFile(null);
            },
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(
                vertical: 4,
                horizontal: 8,
              ),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: .5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                LocaleKeys.remove.tr(),
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void showImagePicker(BuildContext context, BirthdayRegistryUpsertCubit cubit,
      BirthdayRegistryUpsertState state) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      cubit.updateCoverImageFile(image.path);
    }
  }

  Widget _buildCaption(BuildContext context, BirthdayRegistryUpsertCubit cubit,
      BirthdayRegistryUpsertState state) {
    return Padding(
      padding: EdgeInsets.zero,
      child: TextFormField(
        initialValue: state.caption,
        decoration: InputDecoration(
          hintText: 'Untitled birthday registry',
          hintStyle: TextStyle(color: appTheme.borderColorV2),
          border: InputBorder.none,
        ),
        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        textCapitalization: TextCapitalization.sentences,
        onChanged: (value) {
          cubit.updateCaption(value);
        },
      ),
    );
  }

  Widget _buildMember(BuildContext context, BirthdayRegistryUpsertCubit cubit,
      BirthdayRegistryUpsertState state) {
    return Padding(
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.birthday_for.tr(),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.normal,
            ),
          ),
          const SizedBox(height: 4),
          _buildDropdownMenu(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildDropdownMenu(BuildContext context,
      BirthdayRegistryUpsertCubit cubit, BirthdayRegistryUpsertState state) {
    return DropdownMenu<Account>(
      textStyle: const TextStyle(fontSize: 16),
      expandedInsets: EdgeInsets.zero,
      initialSelection: state.selectedMember,
      hintText: 'Select a member',
      inputDecorationTheme: InputDecorationTheme(
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: appTheme.lightGreyColor),
        ),
      ),
      trailingIcon: Transform.rotate(
        angle: 3.14 / 2,
        child: SvgPicture.asset(Assets.icons.iconArrow.path),
      ),
      selectedTrailingIcon: Transform.rotate(
        angle: -3.14 / 2,
        child: SvgPicture.asset(Assets.icons.iconArrow.path),
      ),
      onSelected: (Account? newValue) {
        AppLogger.d('Selected member: ${newValue?.fullName}');
        if (newValue != null) {
          cubit.updateMember(newValue);
        }
      },
      leadingIcon: state.selectedMember != null
          ? _buildDropdownItemView(context, cubit, state, state.selectedMember!)
          : null,
      dropdownMenuEntries:
          state.members.map<DropdownMenuEntry<Account>>((Account member) {
        return DropdownMenuEntry<Account>(
          value: member,
          label: member.fullName ?? '',
          labelWidget: _buildDropdownItemView(context, cubit, state, member),
          trailingIcon: member.uuid == state.selectedMember?.uuid
              ? SvgPicture.asset(Assets.icons.icCheck.path,
                  colorFilter: ColorFilter.mode(
                      appTheme.primaryColorV2, BlendMode.srcATop))
              : null,
          style: const ButtonStyle(
            textStyle: WidgetStatePropertyAll(TextStyle(fontSize: 16)),
            padding: WidgetStatePropertyAll(
                EdgeInsets.only(top: 4, bottom: 4, right: 10)),
          ),
        );
      }).toList(),
      menuStyle: MenuStyle(
        padding: const WidgetStatePropertyAll(EdgeInsets.zero),
        shape: WidgetStatePropertyAll(RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        )),
        backgroundColor: WidgetStatePropertyAll(appTheme.backgroundSelago),
      ),
    );
  }

  Widget _buildDropdownItemView(
      BuildContext context,
      BirthdayRegistryUpsertCubit cubit,
      BirthdayRegistryUpsertState state,
      Account member) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 12,
            child: Text(
              member.fullName != null ? member.fullName![0] : '',
              style: const TextStyle(fontSize: 12),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            member.fullName ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black,
              fontWeight: FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDate(BuildContext context, BirthdayRegistryUpsertCubit cubit,
      BirthdayRegistryUpsertState state) {
    return Padding(
      padding: EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            LocaleKeys.celebrated_on.tr(),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.normal,
            ),
          ),
          InkWell(
            onTap: () => selectDate(context, cubit, state),
            child: Container(
              padding: EdgeInsets.zero,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    state.dateTime != null
                        ? DateFormat('dd/MM/yyyy').format(state.dateTime!)
                        : 'dd / mm / yyyy',
                    style: TextStyle(
                      fontWeight: FontWeight.normal,
                      color: state.dateTime != null
                          ? state.isDateTimeValid
                              ? Colors.black
                              : Colors.red
                          : appTheme.grayV2,
                    ),
                  ),
                  const SizedBox(width: 8),
                  SvgPicture.asset(
                    Assets.icons.icCalendar.path,
                    width: 24.w2,
                    colorFilter:
                        ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> selectDate(
      BuildContext context,
      BirthdayRegistryUpsertCubit cubit,
      BirthdayRegistryUpsertState state) async {
    final date = state.dateTime;
    final DateTime? datePicked = await showDatePicker(
      context: context,
      initialDate: date ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (datePicked != null) {
      cubit.updateDateTime(datePicked);
    }
  }

  void goToChatPage() async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      context.pushRoute(ChatRoute(
          parameter: ChatParameter(
              chatContext: ChatContext.getGeneralChatContext(token))));
    }
  }
}
