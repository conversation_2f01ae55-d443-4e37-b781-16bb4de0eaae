import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/screen/main/memories/memory_select_activity_cubit.dart';
import 'package:family_app/screen/main/memories/memory_select_activity_state.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class MemorySelectActivityBts extends BaseBlocProvider<MemorySelectActivityState, MemorySelectActivityCubit> {
  const MemorySelectActivityBts({Key? key});

  @override
  Widget buildPage() => const MemorySelectActivityScreen();

  @override
  MemorySelectActivityCubit createCubit() => MemorySelectActivityCubit(
        activityRepository: locator.get(),
      );
}

class MemorySelectActivityScreen extends StatefulWidget {
  const MemorySelectActivityScreen({super.key});

  @override
  State<MemorySelectActivityScreen> createState() => _MemorySelectActivityScreenState();
}

class _MemorySelectActivityScreenState
    extends BaseBlocPageState<MemorySelectActivityScreen, MemorySelectActivityState, MemorySelectActivityCubit> {
  final TextEditingController _searchController = TextEditingController();

  @override
  bool listenWhen(MemorySelectActivityState previous, MemorySelectActivityState current) {
    if (current.status == MemorySelectActivityStatus.loading) {
      showLoading();
    } else if (current.status == MemorySelectActivityStatus.error) {
      dismissLoading();
      showSimpleToast(current.errorMessage ?? 'Failed to add memory');
    } else if (current.status == MemorySelectActivityStatus.success) {
      dismissLoading();
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildView(BuildContext context, MemorySelectActivityCubit cubit, MemorySelectActivityState state) {
    return _buildDialog(context, cubit, state);
  }

  Widget _buildDialog(BuildContext context, MemorySelectActivityCubit cubit, MemorySelectActivityState state) {
    return SingleChildScrollView(
      // Added for scrollability
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context, cubit, state),
          _buildDivider(context),
          _buildSearchBox(context, cubit, state),
          _buildDivider(context),
          _buildActivityList(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, MemorySelectActivityCubit cubit, MemorySelectActivityState state) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Row(
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(
              'Back',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Center(
              child: Text('Add activity', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
          ),
          const SizedBox(width: 50),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      height: 1,
      color: Theme.of(context).dividerColor,
    );
  }

  Widget _buildSearchBox(BuildContext context, MemorySelectActivityCubit cubit, MemorySelectActivityState state) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _searchController,
        onChanged: cubit.filterActivities,
        decoration: InputDecoration(
          hintText: 'Search by activity',
          prefixIcon: Container(
            padding: const EdgeInsets.all(16.0),
            child: SvgPicture.asset(Assets.icons.icSearch.path),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30.0),
            borderSide: BorderSide.none, // Remove default border
          ),
          filled: true,
          fillColor: Colors.grey[200], // Background color
          contentPadding: const EdgeInsets.symmetric(vertical: 16.0), // Adjust vertical padding
        ),
      ),
    );
  }

  Widget _buildActivityList(BuildContext context, MemorySelectActivityCubit cubit, MemorySelectActivityState state) {
    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        width: double.infinity,
        height: 300,
        child: ListView.builder(
          itemCount: state.filteredActivityList.length,
          itemBuilder: (context, index) {
            final activity = state.filteredActivityList[index];
            final isSelected = state.selectedActivity?.uuid == activity.uuid;
            return Column(
              children: [
                _buildActivityNameItem(context, cubit, activity, isSelected),
                if (index < state.filteredActivityList.length - 1)
                  const Divider(height: 1), // Add divider between items
              ],
            );
          },
        ));
  }

  Widget _buildActivityNameItem(BuildContext context, MemorySelectActivityCubit cubit, ActivityModel activity, bool isSelected) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop(activity);
      },
      child: Container(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Text(
                activity.name ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (isSelected) SvgPicture.asset(Assets.icons.icCheck.path),
          ],
        ),
      ),
    );
  }
}
