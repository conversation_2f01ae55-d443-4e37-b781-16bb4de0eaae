import 'package:dartx/dartx.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/upload/iupload_repository.dart';
import 'package:family_app/utils/log/app_logger.dart';

import 'memories_state.dart';
// Assuming you have a Memory model

class MemoriesCubit extends BaseCubit<MemoriesState> {
  final IFamilyRepository familyRepository;
  final IUploadRepository uploadRepository;
  final IActivityRepository activityRepository;
  MemoriesCubit({required this.familyRepository, required this.uploadRepository, required this.activityRepository})
      : super(MemoriesState());
  final AccountService accountService = locator.get();

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();

    fetchMemories();
  }

  @override
  Future<void> close() {
    locator.unregister<MemoriesCubit>();
    return super.close();
  }

  Future<void> fetchMemories() async {
    emit(state.copyWith(status: MemoriesStatus.loading));
    try {
      final accountUuid = accountService.account?.uuid;
      final activityList = await activityRepository.getAllActivities(accountService.familyId);
      // Create a map of activity.uuid to activity
      final activityMap = {for (var activity in activityList) activity.uuid: activity};

      final memoryList = await familyRepository
          .getMemoryListByFamily(accountService.familyId, type: 'list');
      emit(state.copyWith(
          status: MemoriesStatus.success, memories: memoryList, activities: activityMap, accountUuid: accountUuid));
    } catch (e) {
      //emit(MemoriesError(message: "Failed to load memories"));
      AppLogger.e("fetchMemories error: $e");
      emit(state.copyWith(status: MemoriesStatus.error, errorMessage: 'Failed to load memories'));
    }
    AppLogger.d("fetchMemories done");
  }

  ActivityModel? getActivityById(String activityId) {
    return state.activities[activityId];
  }
}
