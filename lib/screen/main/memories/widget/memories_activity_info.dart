import 'package:auto_route/auto_route.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';

class MemoriesActivityInfo extends StatelessWidget {
  final String? activityId;
  final String? activityName;
  final Color? textColor;

  const MemoriesActivityInfo({
    super.key,
    required this.activityId,
    required this.activityName,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    if (activityId == null || activityId!.isEmpty) {
      return const SizedBox();
    }

    return InkWell(
      onTap: () {
        // Navigate to trip_detail_screen
        context.pushRoute(TripDetailRoute(
          parameter: TripDetailParameter(activityId: activityId!),
        ));
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: (textColor ?? appTheme.lightBotColor).withValues(alpha: .1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              'assets/icons/ic_bookmark.svg', // Replace with your actual asset path
              color: textColor ?? appTheme.lightBotColor,
            ),
            const SizedBox(width: 8),
            Text(
              activityName ?? '',
              style: TextStyle(color: textColor ?? appTheme.lightBotColor),
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
    );
  }
}
