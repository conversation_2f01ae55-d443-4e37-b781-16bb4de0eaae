import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data_summary.dart';
import 'package:family_app/screen/main/thread_detail/widget/vote_cta_button.dart';
import 'package:family_app/screen/main/thread_detail/widget/vote_title_info.dart';
import 'package:family_app/extension.dart';

import 'vote_card_option.dart';

class VoteCard extends StatelessWidget {
  const VoteCard({
    Key? key,
    required this.relatedData,
    required this.relatedDataSummary,
    required this.relatedDataItems,
    required this.isVoted,
    required this.showVoting,
  }) : super(key: key);

  final ThreadMessageRelatedData relatedData;
  final ThreadMessageRelatedDataSummary relatedDataSummary;
  final List<ItemWithVoters> relatedDataItems;
  final bool isVoted;
  final GestureTapCallback showVoting;

  @override
  Widget build(BuildContext context) {
    const double voteCardPadding = 60;
    const double voteCardOptionPadding = 12;
    final double voteCardWidth = context.screenSize.width - (voteCardPadding * 2);
    final double voteCardOptionWidth = voteCardWidth - (voteCardOptionPadding * 2);

    return Container(
      width: voteCardWidth,
      padding: const EdgeInsets.all(12),
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        color: Colors.black.withValues(alpha: 0.04),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      child: SizedBox(
        width: voteCardOptionWidth,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          spacing: 8,
          children: [
            VoteTitleInfo(
              voteName: relatedData.name,
              isVoted: isVoted,
              fromDate: relatedData.fromDate,
              toDate: relatedData.toDate,
            ),
            SizedBox(
              width: double.infinity,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 4,
                children: [
                  for (final option in relatedDataItems)
                    Container(
                      width: double.infinity,
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        color: appTheme.borderColorV2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(99),
                        ),
                      ),
                      child: VoteCardOption(
                        isVoted: isVoted,
                        option: option,
                        countVote: relatedData.getVoteCountByItemUuid(option.uuid!),
                        totalCountVote: relatedDataSummary.total,
                        voteCardOptionWidth: voteCardOptionWidth,
                      ),
                    ),
                ],
              ),
            ),
            VoteCTAButton(
              text: isVoted ? LocaleKeys.view_results.tr() : LocaleKeys.voting.tr(),
              showVoting: showVoting,
            ),
          ],
        ),
      ),
    );
  }
}
