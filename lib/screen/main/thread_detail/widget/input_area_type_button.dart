import 'package:flutter/material.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/screen/main/thread_detail/voting/thread_detail_voting_upsert_screen.dart';
import 'package:family_app/screen/main/thread_detail/thread_detail_state.dart';
import 'package:family_app/screen/main/thread_detail/thread_detail_cubit.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/popup/popup.dart';

class InputAreaTypeButton extends StatelessWidget {
  final ThreadDetailCubit cubit;
  final ThreadDetailState state;
  final BuildContext parentContext;
  final VoidCallback? onAction;

  const InputAreaTypeButton({
    super.key,
    required this.cubit,
    required this.state,
    required this.parentContext,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    List<_MenuItem> menuItems = [
      _MenuItem(
        label: 'Camera',
        iconPath: Assets.icons.icCamera32.path,
        onTap: () {
          Navigator.pop(parentContext);
          // TODO: Implement camera functionality
        },
      ),
      _MenuItem(
        label: 'Photo',
        iconPath: Assets.icons.picture.path,
        onTap: () {
          Navigator.pop(parentContext);
          // TODO: Implement photo picker functionality
        },
      ),
      _MenuItem(
        label: 'Attach file',
        iconPath: Assets.icons.file.path,
        onTap: () {
          Navigator.pop(parentContext);
          // TODO: Implement attach file functionality
        },
      ),
      _MenuItem(
        label: 'Voice',
        iconPath: Assets.icons.voice.path,
        onTap: () {
          Navigator.pop(parentContext);
          // TODO: Implement voice message functionality
        },
      ),
      _MenuItem(
        label: 'Location',
        iconPath: Assets.icons.location.path,
        onTap: () {
          Navigator.pop(parentContext);
          // TODO: Implement location sharing functionality
        },
      ),
      _MenuItem(
        label: 'Voting',
        iconPath: Assets.icons.iconTrailling.path,
        onTap: () {
          Navigator.pop(parentContext);
          ThreadDetailVotingUpsertBts.show(parentContext, state.threadDetail!.uuid!).then((result) async {
            if (result == true) {
              await cubit.onFetchThreadDetail(state.threadDetail!.uuid!);
              if (onAction != null) onAction!();
            }
          });
        },
      ),
    ];

    return CustomPopup(
      showArrow: false,
      content: Container(
        width: 188,
        padding: const EdgeInsets.all(4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: menuItems
              .map((item) => InkWell(
                    onTap: item.onTap,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(child: Text(item.label, style: AppStyle.textSmR)),
                          ImageAssetCustom(imagePath: item.iconPath),
                        ],
                      ),
                    ),
                  ))
              .toList(),
        ),
      ),
      contentPadding: const EdgeInsets.all(0),
      child: ImageAssetCustom(
        imagePath: Assets.icons.buttonMenu.path,
        width: 40,
      ),
    );
  }
}

class _MenuItem {
  final String label;
  final String iconPath;
  final VoidCallback onTap;
  _MenuItem({required this.label, required this.iconPath, required this.onTap});
}
