import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/theme/style/style_theme.dart';

class VoteTitleInfo extends StatelessWidget {
  final String voteName;
  final bool isVoted;
  final bool isEnded;
  final bool isEnableInfo;
  final String? fromDate;
  final String? toDate;

  const VoteTitleInfo({
    Key? key,
    required this.voteName,
    required this.isVoted,
    this.isEnded = false,
    this.isEnableInfo = true,
    this.fromDate,
    this.toDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool showInfo = isEnableInfo && (fromDate != null && toDate != null);
    return Column(
      spacing: 8,
      children: [
        Text(
          voteName,
          textAlign: TextAlign.center,
          style: AppStyle.textMdS,
        ),
        if (showInfo)
          Text(
            '${DateFormat('dd/MM/yyyy hh:mm').format(fromDate!.toLocalDT)} - ${DateFormat('dd/MM/yyyy hh:mm').format(toDate!.toLocalDT)}',
            textAlign: TextAlign.center,
            style: AppStyle.text2XsR.copyWith(color: appTheme.grayV2),
          ),
        if (isEnded)
          Text(
            LocaleKeys.ended.tr(),
            textAlign: TextAlign.center,
            style: AppStyle.textSmS.copyWith(
              color: appTheme.errorV2,
            ),
          ),
      ],
    );
  }
}
