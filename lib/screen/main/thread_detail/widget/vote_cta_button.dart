import 'package:family_app/main.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/theme/style/style_theme.dart';

class VoteCTAButton extends StatelessWidget {
  final String text;
  final GestureTapCallback showVoting;

  const VoteCTAButton({
    Key? key,
    required this.text,
    required this.showVoting,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(16),
      onTap: showVoting,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: appTheme.primaryColorV2.withValues(alpha: .12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Center(
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: AppStyle.textSmS.copyWith(color: appTheme.primaryColorV2),
          ),
        ),
      ),
    );
  }
}
