import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/main.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/theme/style/style_theme.dart';

class VoteHeading extends StatelessWidget {
  final String fullName;
  final String voteName;
  final bool isVoted;
  final GestureTapCallback showVoting;

  const VoteHeading({
    Key? key,
    required this.fullName,
    required this.voteName,
    required this.isVoted,
    required this.showVoting,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 300,
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: '$fullName ${LocaleKeys.vote_create_heading_text.tr()} “$voteName”.',
              style: AppStyle.textXsS.copyWith(color: appTheme.grayV2),
            ),
            const TextSpan(text: ' '),
            if (!isVoted)
              TextSpan(
                text: LocaleKeys.voting_now.tr(),
                style: AppStyle.textXsS.copyWith(color: appTheme.primaryColorV2),
                recognizer: TapGestureRecognizer()..onTap = showVoting,
              )
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
