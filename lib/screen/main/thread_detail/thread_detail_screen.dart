import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:dartx/dartx.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data_items.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data_summary.dart';
import 'package:family_app/screen/main/thread_detail/add_member_bts.dart';
import 'package:family_app/screen/main/thread_detail/thread_detail_cubit.dart';
import 'package:family_app/screen/main/thread_detail/thread_detail_state.dart';
import 'package:family_app/screen/main/thread_detail/widget/vote_cta_button.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/util.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../base/widget/cubit/base_bloc_page.dart';
import '../../../base/widget/cubit/base_bloc_provider.dart';
import '../../../config/lang/locale_keys.g.dart';
import '../../../config/service/app_service.dart';
import '../../../data/model/account.dart';
import '../../../data/model/thread_family.dart';
import '../../../data/model/thread_message.dart';
import '../../../extension.dart';
import '../../../gen/assets.gen.dart';
import '../../../main.dart';
import '../../../widget/appbar_custom.dart';
import '../../../widget/avatar_circle_view.dart';
import '../../../widget/image_asset_custom.dart';
import '../../../widget/popup/popup.dart';
import 'voting/detail_upsert/screen.dart';
import 'voting/thread_detail_voting_upsert_screen.dart';
import 'widget/input_area_type_button.dart';
import 'widget/vote_card.dart';
import 'widget/vote_heading.dart';

@RoutePage()
class ThreadDetailPage extends BaseBlocProvider<ThreadDetailState, ThreadDetailCubit> {
  const ThreadDetailPage({required this.parameter, super.key});

  final ThreadFamily parameter;

  @override
  Widget buildPage() => const ThreadDetailView();

  @override
  ThreadDetailCubit createCubit() => ThreadDetailCubit(
        familyRepository: locator.get(),
        accountService: locator.get(),
        threadFamily: parameter,
      );
}

class ThreadDetailView extends StatefulWidget {
  const ThreadDetailView({super.key});

  @override
  State<ThreadDetailView> createState() => _ThreadDetailViewState();
}

class _ThreadDetailViewState extends BaseBlocPageState<ThreadDetailView, ThreadDetailState, ThreadDetailCubit> {
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isUserAtBottom = true;
  bool _isFirstLoad = true;
  final FocusNode _focusNode = FocusNode();
  late ThreadDetailCubit cubit;

  String getFormattedTime(String createdAtString) {
    DateTime createdAt = DateFormat('yyyy-MM-dd HH:mm:ss').parse(createdAtString);
    Duration difference = DateTime.now().difference(createdAt);

    if (difference.inHours < 1) {
      return timeago.format(createdAt, locale: 'en_short'); // Ví dụ: "5m ago"
    } else {
      return DateFormat('HH:mm').format(createdAt); // Ví dụ: "14:30"
    }
  }

  @override
  void initState() {
    super.initState();
    cubit = context.read<ThreadDetailCubit>();
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        Future.delayed(const Duration(milliseconds: 300), () {
          _scrollToBottom();
        });
      }
    });
    _scrollController.addListener(_onScrollListener);
    cubit.navigationStream.listen((route) {
      context.pushRoute(route);
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _scrollController.removeListener(_onScrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScrollListener() {
    if (_scrollController.position.atEdge) {
      bool isBottom = _scrollController.position.pixels == _scrollController.position.maxScrollExtent;
      _isUserAtBottom = isBottom;
    }
  }

  @override
  Widget buildAppBar(BuildContext context, ThreadDetailCubit cubit, ThreadDetailState state) {
    return AppBarCustom(
      titleView: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 20, left: 10),
            child: _showAvatar(state.threadDetail ?? cubit.threadFamily),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    cubit.threadFamily.name ?? '',
                    style: AppStyle.bold16(color: Colors.black),
                  ),
                  _showNameMember(state.threadDetail ?? cubit.threadFamily),
                ],
              ),
            ),
          ),
        ],
      ),
      // title: "${state.threadDetail?.name ?? ''} \n ${state.threadDetail?.members?.length ?? 0} members",
      onBack: () {
        cubit.stopAutoRefresh();
        Navigator.pop(context);
      },
      actions: [
        CustomPopup(
          content: Container(
              color: Colors.white,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  InkWell(
                    onTap: () {
                      cubit.showMemberInThread();
                      Navigator.pop(context);
                      AddMemberThreadBts.show(
                        context,
                        selectedMembers: state.memberList.map((member) => member.familyMemberUuid ?? '').toList(),
                        onSelected: cubit.updateSelectedMember,
                      );
                    },
                    child: Row(
                      children: [
                        // Icon(Icons.add),
                        const SizedBox(width: 10),
                        Text(LocaleKeys.add_or_remove_member.tr()),
                      ],
                    ),
                  ),
                  const Divider(),
                  SingleChildScrollView(child: _showMemberInThread(cubit, state))

                  // InkWell(
                  //   onTap: (){
                  //     cubit.showMemberChatInThread();
                  //     Navigator.pop(context);
                  //     ShowMemberThreadBts.show(
                  //       context,
                  //       members: cubit.state.memberListChat,
                  //       onSelected: cubit.onChatWithMember
                  //     );
                  //   },
                  //   child: Row(
                  //     children: [
                  //       // Icon(Icons.wallet_membership),
                  //       SizedBox(width: 10),
                  //       Text(LocaleKeys.show_member.tr()),
                  //     ],
                  //   ),
                  // )
                ],
              )),
          child: ImageAssetCustom(
            imagePath: Assets.icons.buttonThreeDot.path,
            width: 40,
          ),
        ),
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, ThreadDetailCubit cubit, ThreadDetailState state) {
    return BlocListener<ThreadDetailCubit, ThreadDetailState>(
        listener: (context, state) {
          if (state.messages.isNotEmpty) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_isUserAtBottom && _isFirstLoad) {
                _scrollToBottom();
                _isFirstLoad = false;
              }
            });
          }
        },
        child: Stack(children: [
          SafeArea(
            child: RefreshIndicator(
              onRefresh: cubit.onRefresh,
              child: Column(
                children: [
                  Expanded(
                    child: state.isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : Column(
                            children: [
                              _buildMessageList(cubit, state),
                              const Divider(height: 1),
                              _buildInputArea(cubit, state),
                            ],
                          ),
                  ),
                ],
              ),
            ),
          ),
        ]));
  }

  Widget _buildMessageList(ThreadDetailCubit cubit, ThreadDetailState state) {
    final memberList = state.memberList;

    return Expanded(
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(20.0),
        itemCount: state.messages.length,
        itemBuilder: (context, index) {
          final message = state.messages[index];
          final isUserMessage = message.messageType == ThreadMessageType.TEXT;
          final color = isUserMessage ? Colors.black : Colors.blue;
          ThreadMessage? previousMessage = index > 0 ? state.messages[index - 1] : null;
          ThreadMessage? nextMessage = index < state.messages.length - 1 ? state.messages[index + 1] : null;
          bool isLastMessage = nextMessage == null || nextMessage.userId != message.userId;
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildMessageRow(message, isUserMessage, color, previousMessage, isLastMessage, memberList),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInputArea(ThreadDetailCubit cubit, ThreadDetailState state) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
        child: Row(
          children: [
            InputAreaTypeButton(
              cubit: cubit,
              state: state,
              parentContext: context,
              onAction: _scrollToBottom,
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextField(
                  focusNode: _focusNode,
                  controller: _controller,
                  decoration: const InputDecoration(
                    hintText: 'Aa',
                    border: InputBorder.none,
                  ),
                ),
              ),
            ),
            // SizedBox(width: 10),
            // Icon(Icons.emoji_emotions_outlined, color: Colors.black45),
            const SizedBox(width: 10),
            InkWell(
              onTap: () {
                _sendMessage(cubit, state);
              },
              child: ImageAssetCustom(
                imagePath: Assets.icons.buttonSend.path,
                width: 40,
              ),
            )
            // CircleAvatar(
            //   backgroundColor: Colors.grey.shade200,
            //   radius: 24,
            //   child: Icon(Icons.send, color: Colors.deepPurple),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageRow(ThreadMessage message, bool isUserMessage, Color color, ThreadMessage? previousMessage, bool isLastMessage, List<Account> memberList) {
    var isMe = message.userId == accountService.account?.uuid;
    bool showAvatar = !isMe && (previousMessage == null || previousMessage.userId != message.userId);
    Map<String, dynamic> userMap = jsonDecode(message.userLog.toString());
    // Convert Time
    DateTime createdAt = DateTime.parse(message.createdAt.toString());

    // Check if this is the first message of a new day
    bool isFirstMessageOfDay = previousMessage == null || DateFormat('yyyy-MM-dd').format(createdAt) != DateFormat('yyyy-MM-dd').format(DateTime.parse(previousMessage.createdAt.toString()));

    // If it is today, show "Today", otherwise show full date
    String formattedDate = DateFormat('yyyy-MM-dd').format(createdAt) == DateFormat('yyyy-MM-dd').format(DateTime.now()) ? "Today" : DateFormat('EEEE, MMM d, yyyy').format(createdAt); // VD: "Friday, Feb 2, 2024"

    final isTextMessage = message.messageType == ThreadMessageType.TEXT;
    final isVoteMessage = message.messageType == ThreadMessageType.POLL;

    final bool showTextMessage = isTextMessage && message.message != null;
    final bool showVoteMessage = isVoteMessage && message.relatedData != null;
    // Show timestamp if it's the first message of a user or the last message in the group
    bool showTimestamp = isLastMessage && (showTextMessage || showVoteMessage);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isFirstMessageOfDay)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    formattedDate,
                    style: AppStyle.bold14(color: Colors.black),
                  ),
                ),
              ],
            ),
          ),
        if (showAvatar)
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                child: Text(userMap["full_name"][0].toString().toUpperCase()),
              ),
              const SizedBox(width: 8.0),
              Text(
                userMap["full_name"],
                style: AppStyle.bold16(color: Colors.black),
              ),
            ],
          ),
        if (showTextMessage)
          Row(
            children: [
              if (!isMe) const SizedBox(width: 40.0),
              Expanded(
                child: Align(
                  alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 8.0),
                    padding: const EdgeInsets.all(12.0),
                    decoration: BoxDecoration(
                      color: isMe ? Colors.deepPurple : Colors.grey[300],
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                    child: Text(
                      message.message.toString(),
                      style: AppStyle.regular16(
                        color: isMe ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        if (showVoteMessage) _buildMessageVote(message, memberList),
        // Show timestamp if it's the first message of a user or the last message in the group
        if (showTimestamp)
          Align(
            alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
            child: Padding(
              padding: !isMe
                  ? const EdgeInsets.only(left: 48.0, top: 4.0)
                  : const EdgeInsets.only(
                      top: 4.0,
                      right: 8.0,
                    ),
              child: Text(
                Utils.formatDateTimeByHour(message.createdAt.toString()),
                style: AppStyle.regular12(color: Colors.grey[600]),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMessageVote(ThreadMessage message, List<Account> memberList) {
    final ThreadMessageRelatedData relatedData = message.relatedData!;
    void showVoting() {
      if (relatedData.uuid!.isNotEmpty) {
        VotingDetailUpsertBts.show(context, relatedData, memberList).then(
          (result) async {
            if (result == true) {
              await context.read<ThreadDetailCubit>().onFetchThreadDetail(relatedData.threadId);
            }
          },
        );
      }
    }

    final userMap = jsonDecode(message.userLog.toString());
    final bool isVoted = relatedData.hasUserVoted(accountService.account?.uuid);
    final ThreadMessageRelatedDataSummary relatedDataSummary = relatedData.getDecodedSummary();
    final relatedDataItems = relatedData.getItemsWithVoters(memberList);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        spacing: 8,
        children: [
          VoteHeading(
            fullName: userMap["full_name"],
            voteName: relatedData.name,
            isVoted: isVoted,
            showVoting: showVoting,
          ),
          VoteCard(
            relatedData: relatedData,
            relatedDataSummary: relatedDataSummary,
            relatedDataItems: relatedDataItems,
            isVoted: isVoted,
            showVoting: showVoting,
          ),
        ],
      ),
    );
  }

  void _sendMessage(ThreadDetailCubit cubit, ThreadDetailState state) {
    if (_controller.text.isNotEmpty) {
      final message = _controller.text;
      cubit.onSendMessage(message);
      _controller.clear();
      _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final state = context.read<ThreadDetailState>();
      if (state.messages.isNotEmpty) {
        if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
          _scrollToBottom();
        }
      }
    });
  }

  Widget _showAvatar(ThreadFamily message) {
    var members = message.members?.length ?? 0;
    return members > 1
        ? Container(
            width: 50,
            height: 65,
            child: Stack(
              children: [
                for (var i = 0; i < members; i++) ...[
                  if (i == 0) ...[
                    Positioned(
                      left: 0,
                      child: CircleAvatar(radius: 12, child: Text(message.members![i].fullName![0].toUpperCase())),
                    ),
                  ] else if (i == 1) ...[
                    Positioned(
                      top: 17,
                      child: CircleAvatar(radius: 12, child: Text(message.members![i].fullName![0].toUpperCase())),
                    ),
                  ]
                ],
                if (members > 2) ...[
                  Positioned(
                    left: 14,
                    top: 5,
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: Colors.purple,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: Text(
                        "+${members - 2}",
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ))
        : CircleAvatar(radius: 20, child: Text(message.members![0].fullName![0]));
  }

  Widget _showMemberInThread(ThreadDetailCubit cubit, ThreadDetailState state) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: padding(horizontal: 0),
      separatorBuilder: (context, index) => Divider(color: appTheme.borderColor),
      itemCount: state.memberListChat.length,
      itemBuilder: (context, index) => _buildMember(index, state.memberListChat),
    );
  }

  Widget _buildMember(int index, List<Account> membersList) {
    final member = membersList[index];
    print(member.toJson());
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        cubit.onChatWithMember(member);
      },
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: padding(top: 5, bottom: 5),
        child: Row(
          children: [
            AvatarCircleView(account: member, index: index, size: 25),
            const SizedBox(width: 12),
            Expanded(child: Text(member.fullName ?? '', style: AppStyle.regular14())),
            const SizedBox(width: 12),
          ],
        ),
      ),
    );
  }

  Widget _showNameMember(ThreadFamily message) {
    var name = "";
    for (var i = 0; i < message.members!.length; i++) {
      name = name + message.members![i].fullName! + ", ";
    }
    return Text(
      name.replaceAll(RegExp(r',\s*$'), ''),
      style: AppStyle.medium14(color: appTheme.blackText),
      overflow: TextOverflow.ellipsis,
    );
  }
}
