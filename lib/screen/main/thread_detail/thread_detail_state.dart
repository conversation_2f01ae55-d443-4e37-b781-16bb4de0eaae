import '../../../base/widget/cubit/base_state.dart';
import '../../../data/model/account.dart';
import '../../../data/model/thread_family.dart';
import '../../../data/model/thread_message.dart';

enum ThreadDetailStatus { none, loading, success, error }
enum ThreadInputCommand { voting }

class ThreadDetailState extends BaseState {

  final ThreadDetailStatus status;
  final ThreadFamily? threadDetail;
  List<ThreadMessage> messages = const [];
  final bool isWaitingForResponse;
  final String? errorMessage;
  final List<Account> memberListChat;
  final List<Account> memberList;

  ThreadDetailState({
    this.status = ThreadDetailStatus.none,
    this.threadDetail,
    this.messages = const [],
    this.isWaitingForResponse = false,
    this.errorMessage,
    this.memberListChat = const [],
    this.memberList = const [],
  });

  ThreadDetailState copyWith({
    ThreadDetailStatus? status,
    ThreadFamily? threadDetail,
    List<ThreadMessage>? messages,
    bool? isWaitingForResponse,
    String? errorMessage,
    List<Account>? memberListChat,
    List<Account>? memberList,

  }) {
    return ThreadDetailState(
      status: status ?? this.status,
      threadDetail: threadDetail ?? this.threadDetail,
      messages: messages ?? this.messages,
      isWaitingForResponse: isWaitingForResponse ?? this.isWaitingForResponse,
      errorMessage: errorMessage ?? this.errorMessage,
      memberListChat:  memberListChat ?? this.memberListChat,
      memberList: memberList ?? this.memberList,
    );
  }

  @override
  List<Object?> get props => [status, threadDetail, messages,  isWaitingForResponse, errorMessage, memberListChat, memberList];

}