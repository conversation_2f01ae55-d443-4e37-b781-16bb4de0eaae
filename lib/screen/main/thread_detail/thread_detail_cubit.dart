import 'dart:async';

import 'package:family_app/screen/main/thread_detail/thread_detail_state.dart';
import 'package:family_app/utils/log/app_logger.dart';

import '../../../base/widget/cubit/base_cubit.dart';
import '../../../config/service/account_service.dart';
import '../../../data/model/account.dart';
import '../../../data/model/thread_family.dart';
import '../../../data/repository/thread/ithread_repository.dart';
import '../../../data/repository/thread/model/message_parameter.dart';
import '../../../data/repository/thread/model/thread_parameter.dart';
import '../../../router/app_route.dart';

const int mesagePollingInterval = 5; //seconds

class ThreadDetailCubit extends BaseCubit<ThreadDetailState> {
  final IThreadRepository familyRepository;
  final AccountService accountService;
  ThreadFamily threadFamily;
  Timer? _timer;
  final StreamController<ThreadDetailRoute> _navigationController = StreamController();

  Stream<ThreadDetailRoute> get navigationStream => _navigationController.stream;


  ThreadDetailCubit({
    required this.familyRepository,
    required this.accountService,
    required this.threadFamily,
  }) : super(ThreadDetailState());

  @override
  void onInit() {
    super.onInit();
    print("threadFamily id: ${threadFamily.uuid}");
    onFetchThreadDetail(threadFamily.uuid.toString());
    showMemberInThread();
    showMemberChatInThread();
  }

  Future<void> onRefresh() async {
    // onFetchThreadDetail(threadFamily.uuid.toString());
    logd("Do nothing for now");
  }

  showMemberChatInThread() {
    var data = state.threadDetail ?? threadFamily;
    var result = accountService.memberInFamily.value.where((member) => data.members!.any((item) => member.familyMemberUuid == item.uuid) &&
        member.familyMemberUuid != accountService.account?.uuid.toString()).toList();
    emit(state.copyWith(memberListChat: result ?? []));
  }

  showMemberInThread() {
    var data = state.threadDetail ?? threadFamily;
    var result = accountService.memberInFamily.value.
    where((member) => data.members!.any((item) => member.familyMemberUuid == item.uuid)).toList();
    emit(state.copyWith(memberList: result ?? []));
  }

  void startAutoRefresh() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: mesagePollingInterval), (timer) {
      onFetchThreadMessage(state.threadDetail!.uuid.toString());
    });
  }

  onFetchThreadDetail(String threadId) async {
    try {
      emit(state.copyWith(status: ThreadDetailStatus.loading));
      final result = await familyRepository.getThreadDetail(threadId);
      print("result: ${result.uuid}");
      if (result.uuid != null) {
        // startAutoRefresh();
        onFetchThreadMessage(result.uuid.toString());
      }
      emit(state.copyWith(status: ThreadDetailStatus.success, threadDetail: result,));
    } catch (e) {
      emit(state.copyWith(status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  onFetchThreadMessage(String threadId) async {
    try {
      emit(state.copyWith(status: ThreadDetailStatus.loading));
      final result = await familyRepository.getThreadMessage(threadId);
      emit(state.copyWith(status: ThreadDetailStatus.success, messages: result.reversed.toList()));
    } catch (e) {
      emit(state.copyWith(status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  onSendMessage(String message) async {
    try {
      emit(state.copyWith(status: ThreadDetailStatus.loading));
      final result = await familyRepository.createThreadMessage(
          MessageParameter(message: message, messageType: "text", messageTypeId: ""), threadFamily.uuid.toString());
      onFetchThreadMessage(threadFamily.uuid.toString());
      print("onSendMessage: ${result.uuid}");
      emit(state.copyWith(status: ThreadDetailStatus.success));

    } catch (e) {
      emit(state.copyWith(status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  updateSelectedMember(List<Account> selectedMember) async {
    try {
      List<String?> uuids = selectedMember.map((member) => member.familyMemberUuid).toList();
      List<String> nonNullUuids = uuids.whereType<String>().toList();
      print("object ${nonNullUuids}");
      final result = await familyRepository.updateMemberThread(nonNullUuids , threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success, threadDetail: result,));
      showMemberInThread();
      showMemberChatInThread();
    } catch (e) {
      emit(state.copyWith(status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  onChatWithMember(Account member) {
    _createThreadWithMember(member);
    print("object ${member.familyMemberUuid}");
  }

  void stopAutoRefresh() {
    _timer?.cancel();
  }

  _createThreadWithMember(Account account) async {
    try {
      // emit(state.copyWith(status: ThreadStatus.loading));
      List<String> members = ["${account.familyMemberUuid}"];
      final result = await familyRepository.createThread(
        ThreadParameter(
          familyId: accountService.familyId,
          name: account.fullName,
          members: members,
          color: "",
          image: "",
          setting: [],
        ),
      );
      if (result.uuid != null) {
        _navigationController.add(ThreadDetailRoute(parameter: result));
        // emit(state.copyWith(threadDetail: result));
        // await onFetchAllThread(accountService.familyId);
        // emit(state.copyWith(selectedMembers: []));
      }

    } catch (e) {
      // emit(state.copyWith(
      //     status: ThreadStatus.error, errorMessage: e.toString()));
    }
  }

}
