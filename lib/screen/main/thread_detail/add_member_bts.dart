import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../base/stream/base_list_stream_controller.dart';
import '../../../base/stream/base_stream_builder.dart';
import '../../../config/lang/locale_keys.g.dart';
import '../../../config/service/account_service.dart';
import '../../../config/service/app_service.dart';
import '../../../config/theme/style/style_theme.dart';
import '../../../data/model/account.dart';
import '../../../data/repository/family/ifamily_repository.dart';
import '../../../extension.dart';
import '../../../main.dart';
import '../../../utils/bottom_sheet.dart';
import '../../../widget/appbar_custom.dart';
import '../../../widget/avatar_circle_view.dart';
import '../../../widget/circle_checkbox.dart';
import '../../../widget/primary_button.dart';

class AddMemberThreadBts extends StatefulWidget {
  const AddMemberThreadBts({this.onSelected, super.key, this.selectedMembers = const []});
  final Function(List<Account>)? onSelected;
  final List<String> selectedMembers;

  static show(BuildContext context, {required List<String> selectedMembers, Function(List<Account>)? onSelected}) {
    BottomSheetUtils.showHeight(
      context,
      child: AddMemberThreadBts(
        onSelected: onSelected,
        selectedMembers: selectedMembers,
      ),
    );
  }

  @override
  State<AddMemberThreadBts> createState() => _AddMemberThreadBtsState();
}

class _AddMemberThreadBtsState extends State<AddMemberThreadBts> {
  final IFamilyRepository familyRepository = locator.get();
  late final BaseListStreamController<Account> members = BaseListStreamController<Account>([]);
  late final BaseListStreamController<String> selectedMembers = BaseListStreamController<String>(widget.selectedMembers);

  final AccountService accountService = locator.get();

  @override
  void initState() {
    super.initState();
    onFetchFamilyMember();
  }
  //
  Future<void> onFetchFamilyMember() async {
    String currentActiveFamilyUuid = accountService.myActiveFamily.value?.familyUuid ?? '';

    if (currentActiveFamilyUuid.isNotEmpty) {
      final result = await familyRepository.getUserInFamily(currentActiveFamilyUuid);

      members.value = result;
    } else {
      final result = await familyRepository.getFamilyMember();
      members.value = result;
    }
  }

  @override
  void dispose() {
    members.dispose();
    selectedMembers.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBarCustom(
          title: LocaleKeys.select_member.tr(),
          viewPadding: padding(horizontal: 16, top: 17, bottom: 15),
          showBack: false,
          actions: [
            GestureDetector(onTap: context.maybePop, child: const Icon(Icons.close, size: 22)),
          ],
        ),
        Expanded(
          child: TwoBaseListStreamBuilder<Account, String>(
            firstController: members,
            secondController: selectedMembers,
            builder: (membersList, selectMembers) => ListView.separated(
              physics: const BouncingScrollPhysics(),
              padding: padding(horizontal: 16),
              itemBuilder: (context, index) => _buildMember(index, membersList, selectMembers),
              separatorBuilder: (context, index) => Divider(color: appTheme.borderColor),
              itemCount: membersList.length,
            ),
          ),
        ),
        Container(
          padding: padding(horizontal: 16, vertical: 7),
          decoration: BoxDecoration(
            color: appTheme.whiteText,
            boxShadow: [
              BoxShadow(color: Colors.black.withOpacity(.082), blurRadius: 10),
            ],
          ),
          child: SafeArea(
            top: false,
            child: TwoBaseListStreamBuilder(
              firstController: members,
              secondController: selectedMembers,
              builder: (membersList, selectMembers) {
                final isSelectAll = membersList.length == selectMembers.length;
                return Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: onUpdateSelectAllValue,
                        behavior: HitTestBehavior.opaque,
                        child: Row(
                          children: [
                            CircleCheckbox(isChecked: isSelectAll, onTap: onUpdateSelectAllValue),
                            const SizedBox(width: 9),
                            Expanded(child: Text(LocaleKeys.select_all.tr(), style: AppStyle.regular14())),
                          ],
                        ),
                      ),
                    ),
                    PrimaryButton(
                      isFullWidth: false,
                      onTap: () {
                        final allMember = members.value
                            .where((value) => selectedMembers.value.contains(value.familyMemberUuid ?? ''))
                            .toList();
                        widget.onSelected?.call(allMember);
                        context.maybePop();
                      },
                      text: LocaleKeys.add_new_member.tr(),
                      buttonPadding: padding(top: 7, bottom: 10, horizontal: 31),
                    )
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  void onUpdateSelectAllValue() {
    if (selectedMembers.length == members.length) {
      selectedMembers.value = [];
    } else {
      selectedMembers.value = members.value.map((e) => e.familyMemberUuid ?? '').toList();
    }
  }

  Widget _buildMember(int index, List<Account> membersList, List<String> selectMembers) {
    final hasContain = selectMembers.contains(membersList[index].familyMemberUuid);
    final member = membersList[index];
    print(member.toJson());
    void onChangeStatusContain() {
      if (hasContain) {
        selectedMembers.removeValue((value) => (member.familyMemberUuid ?? '') == value);
      } else {
        selectedMembers.addValue(member.familyMemberUuid ?? '');
      }
    }

    return GestureDetector(
      onTap: onChangeStatusContain,
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: padding(top: 15, bottom: 14),
        child: Row(
          children: [
            AvatarCircleView(account: member, index: index, size: 40),
            const SizedBox(width: 12),
            Expanded(child: Text(member.fullName ?? '', style: AppStyle.regular14())),
            const SizedBox(width: 12),
            CircleCheckbox(isChecked: hasContain, onTap: onChangeStatusContain),
          ],
        ),
      ),
    );
  }
}