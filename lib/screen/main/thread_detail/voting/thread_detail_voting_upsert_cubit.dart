import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/repository/thread/ithread_repository.dart';
import 'package:family_app/data/repository/thread/model/message_parameter.dart';
import 'package:family_app/data/repository/thread_poll/ithread_poll_repository.dart';
import 'package:family_app/data/repository/thread_poll/model/thread_poll_parameter.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'thread_detail_voting_upsert_parameter.dart';
import 'thread_detail_voting_upsert_state.dart';

class ThreadDetailVotingUpsertCubit extends BaseCubit<ThreadDetailVotingUpsertState> {
  final ThreadDetailVotingUpsertParameter parameter;
  final IThreadPollRepository threadPollRepository = locator<IThreadPollRepository>();
  final IThreadRepository threadRepository = locator<IThreadRepository>();

  ThreadDetailVotingUpsertCubit(this.parameter) : super(ThreadDetailVotingUpsertState(threadId: parameter.threadId));

  static const prefixOptionHandler = 'options-';
  late TextFieldHandler handlerTitle;
  late List<TextFieldHandler> handlerOptions = [];
  late int optionCounter = 0;
  late FormTextFieldHandler formHandler;

  @override
  void onInit() {
    super.onInit();
    _initData();
  }

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }

  void _initData() {
    handlerTitle = TextFieldHandler(field: 'title', hintText: 'Untitled vote', inputType: TextInputType.text, isRequired: true, onListenText: validateFormTitle);
    addOption();
  }

  void updateName(String name) {
    emit(state.copyWith(name: name));
  }

  void validateFormTitle() {
    if (handlerTitle.text.isEmpty) {
      emit(state.copyWith(isNameValid: false));
    } else {
      emit(state.copyWith(isNameValid: true));
    }
  }

  void updateOptions(List<Map<String, dynamic>>? options) {
    emit(state.copyWith(options: options));
  }

  void addOption({String value = ''}) {
    const int maxOptionLength = 10;
    if (state.options.length == maxOptionLength) return showSimpleToast(LocaleKeys.vote_option_added_maximum.tr());

    optionCounter++;
    final handler = TextFieldHandler(
      field: '$prefixOptionHandler$optionCounter',
      hintText: 'Option $optionCounter',
      inputType: TextInputType.text,
      isRequired: true,
      onListenText: validateFormOption,
    );
    final option = {
      'value': value,
      'handler': handler,
    };
    final options = [...state.options, option];
    updateOptions(options);
    updateFormHandler(options);
  }

  void removeOption(int index) {
    if (index < 0 || index >= state.options.length || state.options.length == 1) return;
    final options = [
      ...state.options.sublist(0, index),
      ...state.options.sublist(index + 1),
    ];
    updateOptions(options);
    updateFormHandler(options);
  }

  void onReorder(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final List<Map<String, dynamic>> updatedOptions = [...state.options];
    // Reorder options
    final Map<String, dynamic> movedOption = updatedOptions.removeAt(oldIndex);
    updatedOptions.insert(newIndex, movedOption);

    emit(state.copyWith(options: updatedOptions));
  }

  void updateFormHandler(List<Map<String, dynamic>> options) {
    handlerOptions = [
      ...options.map((option) => option['handler']),
    ];
    formHandler = FormTextFieldHandler(
      handlers: [handlerTitle, ...handlerOptions],
      validateForm: onSubmit,
    );
    validateFormOption();
  }

  void validateFormOption() {
    if (handlerOptions.length < 2 || handlerOptions.any((handler) => handler.text.isEmpty)) {
      emit(state.copyWith(isOptionsValid: false));
    } else {
      emit(state.copyWith(isOptionsValid: true));
    }
  }

  void updateFromTime(DateTime? dateTime) {
    emit(state.copyWith(fromTime: dateTime));
  }

  void updateToTime(DateTime? dateTime) {
    emit(state.copyWith(toTime: dateTime));
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    try {
      final String title = json['title'] ?? '';
      final List<String> options = json.entries
          .where((entry) => entry.key.startsWith(prefixOptionHandler)) // Filter keys that start with prefix option handler
          .map((entry) => entry.value as String) // Map values to a list of strings
          .toList();
      final params = ThreadPollParameter(threadId: state.threadId, name: title, items: options, fromDate: state.fromTime?.toIso8601String(), toDate: state.toTime?.toIso8601String());

      emit(state.copyWith(status: ThreadDetailVotingUpsertStatus.loading));
      final resultPoll = await threadPollRepository.threadPollCreate(params);
      await threadRepository.createThreadMessage(
        MessageParameter(message: '', messageType: ThreadMessageType.POLL, messageTypeId: resultPoll.uuid),
        state.threadId,
      );
      optionCounter = 0;
      emit(state.copyWith(status: ThreadDetailVotingUpsertStatus.success));
    } catch (e) {
      AppLogger.e('Error saving polling: $e');
      emit(state.copyWith(status: ThreadDetailVotingUpsertStatus.error));
    } finally {
      emit(state.copyWith(status: ThreadDetailVotingUpsertStatus.done));
    }
  }
}
