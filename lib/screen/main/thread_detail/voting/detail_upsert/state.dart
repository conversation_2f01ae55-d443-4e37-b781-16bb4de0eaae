import 'package:family_app/base/widget/cubit/base_state.dart';

enum VotingDetailUpsertStatus { initial, loading, success, error, done }

class VotingDetailUpsertState extends BaseState {
  final VotingDetailUpsertStatus status;
  final String? selectedItemUuid;
  final bool isActive;

  VotingDetailUpsertState({
    this.status = VotingDetailUpsertStatus.initial,
    this.selectedItemUuid,
    this.isActive = false,
  });

  @override
  List<Object?> get props => [status, selectedItemUuid, isActive];

  VotingDetailUpsertState copyWith({
    VotingDetailUpsertStatus? status,
    String? selectedItemUuid,
    bool? isActive,
  }) {
    return VotingDetailUpsertState(
      status: status ?? this.status,
      selectedItemUuid: selectedItemUuid ?? this.selectedItemUuid,
      isActive: isActive ?? this.isActive,
    );
  }
}
