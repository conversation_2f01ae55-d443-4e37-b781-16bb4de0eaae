import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data_summary.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/thread_detail/widget/vote_card_voter_list.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:flutter/material.dart';

import '../../widget/vote_title_info.dart';
import 'cubit.dart';
import 'state.dart';

class VotingDetailUpsertBts extends BaseBlocProvider<VotingDetailUpsertState, VotingDetailUpsertCubit> {
  static late List<Account> memberList;
  static late ThreadMessageRelatedData relatedData;

  const VotingDetailUpsertBts({super.key});

  static Future<bool?> show(BuildContext context, ThreadMessageRelatedData paramRelatedData, List<Account>? paramMemberList) async {
    relatedData = paramRelatedData;
    memberList = paramMemberList ?? [];
    return BottomSheetUtils.showHeightReturnBool(
      context,
      height: 0.5,
      child: const VotingDetailUpsertBts(),
    );
  }

  @override
  Widget buildPage() => _VotingDetailUpsertScreen(relatedData: relatedData, memberList: memberList);

  @override
  VotingDetailUpsertCubit createCubit() => VotingDetailUpsertCubit();
}

class _VotingDetailUpsertScreen extends StatefulWidget {
  final ThreadMessageRelatedData relatedData;
  final List<Account> memberList;

  const _VotingDetailUpsertScreen({required this.relatedData, required this.memberList});

  @override
  State<_VotingDetailUpsertScreen> createState() => _VotingDetailUpsertScreenState();
}

class _VotingDetailUpsertScreenState extends BaseBlocPageState<_VotingDetailUpsertScreen, VotingDetailUpsertState, VotingDetailUpsertCubit> {
  @override
  bool listenWhen(VotingDetailUpsertState previous, VotingDetailUpsertState current) {
    switch (current.status) {
      case VotingDetailUpsertStatus.loading:
        showLoading();
        break;
      case VotingDetailUpsertStatus.success:
        dismissLoading();
        Navigator.of(context).pop(true);
        break;
      default:
        dismissLoading();
        break;
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildView(
    BuildContext context,
    VotingDetailUpsertCubit cubit,
    VotingDetailUpsertState state,
  ) {
    return _buildDialog(context, cubit, state);
  }

  Widget _buildDialog(
    BuildContext context,
    VotingDetailUpsertCubit cubit,
    VotingDetailUpsertState state,
  ) {
    return Column(
      children: [
        _buildHeader(context, cubit, state),
        Container(height: 1, color: Theme.of(context).dividerColor),
        _buildBody(context, cubit, state),
      ],
    );
  }

  Widget _buildHeader(BuildContext context, VotingDetailUpsertCubit cubit, VotingDetailUpsertState state) {
    final ThreadMessageRelatedData relatedData = VotingDetailUpsertBts.relatedData;
    final bool isVoted = relatedData.hasUserVoted(accountService.account?.uuid);
    return Container(
      padding: const EdgeInsets.all(16),
      width: double.infinity,
      child: Stack(
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(
              isVoted ? LocaleKeys.close.tr() : LocaleKeys.cancel.tr(),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            child: Center(
              child: Text(isVoted ? 'Voting result' : 'Voting', style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context, VotingDetailUpsertCubit cubit, VotingDetailUpsertState state) {
    final ThreadMessageRelatedData relatedData = VotingDetailUpsertBts.relatedData;
    final bool isVoted = relatedData.hasUserVoted(accountService.account?.uuid);
    final List<Account> memberList = VotingDetailUpsertBts.memberList;
    final relatedDataItems = relatedData.getItemsWithVoters(memberList);

    final bestTotalCountVote = relatedData.getItemWithBestTotalCountVote();
    const int totalVoterDisplayConfig = 3;
    const double voterSize = 24.0;
    final int totalVoterDisplayed = bestTotalCountVote!.value > totalVoterDisplayConfig ? totalVoterDisplayConfig + 1 : bestTotalCountVote.value;
    final double votersWidth = bestTotalCountVote.value > 0 ? (totalVoterDisplayed + 1) * voterSize : 0;

    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    spacing: 20.0,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: VoteTitleInfo(
                          voteName: relatedData.name,
                          isVoted: isVoted,
                          isEnded: relatedData.isEnded(),
                          isEnableInfo: false,
                          fromDate: relatedData.fromDate,
                          toDate: relatedData.toDate,
                        ),
                      ),
                      SizedBox(
                        width: double.infinity,
                        child: Column(mainAxisSize: MainAxisSize.min, mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.start, spacing: 8, children: [
                          for (final option in relatedDataItems)
                            Stack(
                              children: [
                                if (!isVoted)
                                  LabeledRadio(
                                    label: option.name,
                                    voters: option.voters,
                                    votersWidth: votersWidth,
                                    padding: const EdgeInsets.symmetric(horizontal: 5.0),
                                    value: option.uuid!,
                                    groupValue: state.selectedItemUuid,
                                    onChanged: (String newValue) {
                                      cubit.updateSelectedtemUuid(newValue);
                                    },
                                  ),
                                if (isVoted)
                                  _buildVotedOption(
                                    context,
                                    cubit,
                                    state,
                                    option,
                                  ),
                              ],
                            ),
                        ]),
                      ),
                    ],
                  )),
            ),
          ),
          if (!isVoted)
            Padding(
              padding: const EdgeInsets.only(left: 20.0, right: 20.0, top: 16, bottom: 32.0),
              child: PrimaryButtonV2(
                text: LocaleKeys.send.tr(),
                isLoading: state.status == VotingDetailUpsertStatus.loading,
                isActive: state.isActive,
                onTap: state.isActive ? () => cubit.onSubmit(relatedData.uuid!, state.selectedItemUuid!) : null,
              ),
            ),
          if (isVoted)
            SizedBox(
              width: double.infinity,
              child: Padding(
                padding: const EdgeInsets.only(left: 20.0, right: 20.0, top: 16, bottom: 32.0),
                child: InkWell(
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      LocaleKeys.create_event_for_this_vote.tr(),
                      textAlign: TextAlign.center,
                      style: AppStyle.textSmS.copyWith(
                        color: appTheme.primaryColorV2,
                      ),
                    ),
                  ),
                  onTap: () => cubit.createEvent(context, relatedData, memberList),
                ),
              ),
            )
        ],
      ),
    );
  }

  Widget _buildVotedOption(BuildContext context, VotingDetailUpsertCubit cubit, VotingDetailUpsertState state, ItemWithVoters option) {
    final ThreadMessageRelatedData relatedData = VotingDetailUpsertBts.relatedData;
    final List<Account> memberList = VotingDetailUpsertBts.memberList;

    // Fetch related data and calculate vote counts
    relatedData.getItemsWithVoters(memberList);
    final ThreadMessageRelatedDataSummary relatedDataSummary = relatedData.getDecodedSummary();
    final int voteCount = relatedData.getVoteCountByItemUuid(option.uuid!);
    final int totalVoteCount = relatedDataSummary.total;
    final bestTotalCountVote = relatedData.getItemWithBestTotalCountVote();

    // Constants for layout configuration
    const int totalVoterDisplayConfig = 3;
    const double votersIndent = 8.0;
    const double votersHorizontalPadding = 4.0;
    const double voterSize = 24.0;
    const double voterOverlapWidth = 12.0;
    const double optionsContainerHorizontalPadding = 20.0;
    const double optionVerticalPadding = 8.0;

    // Determine the number of voters to display
    final int totalVoterDisplayed = bestTotalCountVote!.value > totalVoterDisplayConfig ? totalVoterDisplayConfig + 1 : bestTotalCountVote.value;

    // Helper method to calculate the width occupied by voters
    double calculateWidthOfVoters(int totalVoterDisplayed, double voterSize, double voterOverlapWidth) {
      if (totalVoterDisplayed <= 0) return 0;
      final totalVotersWidth = totalVoterDisplayed * voterSize;
      const totalPadding = (votersHorizontalPadding * 2) + (optionVerticalPadding * 2);
      final totalOverlapWidth = (totalVoterDisplayed - 1) * voterOverlapWidth;
      return totalVotersWidth + totalPadding - totalOverlapWidth;
    }

    // Helper method to calculate the width available for the voting option
    double calculateWidthOfOption(BuildContext context, double optionContainerPadding, double votersWidth) {
      return MediaQuery.of(context).size.width - (optionContainerPadding * 2) - votersWidth;
    }

    // Calculate widths for voters and the voting option
    final double votersWidth = calculateWidthOfVoters(totalVoterDisplayed, voterSize, voterOverlapWidth);
    final double optionsWidth = calculateWidthOfOption(context, optionsContainerHorizontalPadding, votersWidth);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: optionsContainerHorizontalPadding),
      child: Row(
        children: [
          SizedBox(
            width: optionsWidth,
            child: Stack(
              children: [
                Positioned(
                  left: 0,
                  top: 0,
                  bottom: 0,
                  right: 0,
                  child: Container(
                    decoration: ShapeDecoration(
                      color: appTheme.borderColorV2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(99),
                      ),
                    ),
                  ),
                ),
                Positioned(
                    left: 0,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      width: ((voteCount / totalVoteCount) * optionsWidth).roundToDouble(),
                      decoration: BoxDecoration(
                        color: appTheme.orangeColorV2,
                        borderRadius: voteCount == totalVoteCount
                            ? BorderRadius.circular(99)
                            : const BorderRadius.only(
                                topLeft: Radius.circular(99),
                                bottomLeft: Radius.circular(99),
                              ),
                      ),
                    )),
                Container(
                  padding: const EdgeInsets.all(12),
                  child: Text(
                    option.name,
                    style: AppStyle.textXsR,
                  ),
                )
              ],
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: votersWidth,
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: votersHorizontalPadding),
                child: VoteCardVoterList(
                  totalVoterDisplayConfig: totalVoterDisplayConfig,
                  voters: option.voters ?? [],
                  votersIndent: votersIndent,
                  optionVerticalPadding: optionVerticalPadding,
                  voterSize: voterSize,
                  voterOverlapWidth: voterOverlapWidth,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class LabeledRadio extends StatelessWidget {
  const LabeledRadio({
    super.key,
    required this.label,
    required this.padding,
    required this.groupValue,
    required this.value,
    required this.onChanged,
    this.voters,
    this.votersWidth,
  });

  final String label;
  final EdgeInsets padding;
  final String? groupValue;
  final String value;
  final ValueChanged<String> onChanged;
  final List<Account>? voters;
  final double? votersWidth;

  void setValue() {
    if (value != groupValue) {
      onChanged(value);
    }
  }

  @override
  Widget build(BuildContext context) {
    const int totalVoterDisplayConfig = 3;
    const double voterSize = 24.0;
    const double optionVerticalPadding = 8.0;
    const double voterOverlapWidth = 12.0;

    return InkWell(
      splashFactory: NoSplash.splashFactory,
      highlightColor: Colors.transparent,
      onTap: () => setValue(),
      child: Padding(
        padding: const EdgeInsets.only(left: 10, right: 20),
        child: Row(
          children: [
            SizedBox(
              width: 40,
              child: Radio(
                groupValue: groupValue,
                value: value,
                onChanged: (String? newValue) {
                  onChanged(newValue!);
                },
              ),
            ),
            Expanded(
              child: Container(
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  color: appTheme.backgroundV2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(99),
                  ),
                ),
                padding: const EdgeInsets.all(12),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: AppStyle.textXsR,
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: votersWidth,
              child: VoteCardVoterList(
                totalVoterDisplayConfig: totalVoterDisplayConfig,
                voters: voters ?? [],
                optionVerticalPadding: optionVerticalPadding,
                voterSize: voterSize,
                voterOverlapWidth: voterOverlapWidth,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
