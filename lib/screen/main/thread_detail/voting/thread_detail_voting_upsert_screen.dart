import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/widget/bottom_sheet/date_picker_bottom_sheet.dart';
import 'package:family_app/widget/dotted_border/dotted_border.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/textfield/title_text_field_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'thread_detail_voting_upsert_cubit.dart';
import 'thread_detail_voting_upsert_parameter.dart';
import 'thread_detail_voting_upsert_state.dart';

class ThreadDetailVotingUpsertBts extends BaseBlocProvider<ThreadDetailVotingUpsertState, ThreadDetailVotingUpsertCubit> {
  final ThreadDetailVotingUpsertParameter parameter;

  const ThreadDetailVotingUpsertBts({required this.parameter, super.key});

  static Future<bool?> show(BuildContext context, String threadId) async {
    return BottomSheetUtils.showHeightReturnBool(
      context,
      height: 0.9,
      child: ThreadDetailVotingUpsertBts(
        parameter: ThreadDetailVotingUpsertParameter(threadId),
      ),
    );
  }

  @override
  Widget buildPage() => const _ThreadDetailVotingUpsertScreen();

  @override
  ThreadDetailVotingUpsertCubit createCubit() => ThreadDetailVotingUpsertCubit(parameter);
}

class _ThreadDetailVotingUpsertScreen extends StatefulWidget {
  const _ThreadDetailVotingUpsertScreen();

  @override
  State<_ThreadDetailVotingUpsertScreen> createState() => _ThreadDetailVotingUpsertScreenState();
}

class _ThreadDetailVotingUpsertScreenState extends BaseBlocPageState<_ThreadDetailVotingUpsertScreen, ThreadDetailVotingUpsertState, ThreadDetailVotingUpsertCubit> {
  @override
  bool listenWhen(ThreadDetailVotingUpsertState previous, ThreadDetailVotingUpsertState current) {
    switch (current.status) {
      case ThreadDetailVotingUpsertStatus.loading:
        showLoading();
        break;
      case ThreadDetailVotingUpsertStatus.success:
        dismissLoading();
        Navigator.of(context).pop(true);
        break;
      default:
        dismissLoading();
        break;
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildView(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return _buildDialog(context, cubit, state);
  }

  Widget _buildDialog(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        _buildHeader(context, cubit, state),
        Container(height: 1, color: Theme.of(context).dividerColor),
        _buildBody(context, cubit, state),
      ],
    );
  }

  Widget _buildHeader(BuildContext context, ThreadDetailVotingUpsertCubit cubit, ThreadDetailVotingUpsertState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text(
              'Cancel',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const Expanded(
            child: Center(
              child: Text('Voting', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
          ),
          _buildSubmitButton(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildSubmitButton(BuildContext context, ThreadDetailVotingUpsertCubit cubit, ThreadDetailVotingUpsertState state) {
    return TextButton(
        onPressed: () {
          if (state.isNameValid && state.isOptionsValid) cubit.formHandler.onSubmit();
        },
        child: Text(
          'Create',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: (state.isNameValid && state.isOptionsValid) ? appTheme.primaryColorV2 : appTheme.grayV2,
          ),
        ));
  }

  Widget _buildBody(BuildContext context, ThreadDetailVotingUpsertCubit cubit, ThreadDetailVotingUpsertState state) {
    return Expanded(
      child: SingleChildScrollView(
        child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 16.0,
              children: [
                _buildQuestion(context, cubit, state),
                _buildOptions(context, cubit, state),
                _buildDateline(context, cubit, state),
              ],
            )),
      ),
    );
  }

  Widget _buildTextBox(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return ValueListenableBuilder(
      valueListenable: cubit.formHandler.formValidated,
      builder: (context, isValidate, _) => TitleTextFieldV2(fieldNode: cubit.handlerTitle, validatedForm: isValidate),
    );
  }

  Widget _buildQuestion(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Vote title', style: AppStyle.textSmR.copyWith(color: appTheme.primaryColorV2)),
        SizedBox(height: 8.h2),
        _buildTextBox(context, cubit, state),
      ],
    );
  }

  Widget _buildOptions(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Options', style: AppStyle.textSmR.copyWith(color: appTheme.primaryColorV2)),
        SizedBox(height: 8.h2),
        _buildOptionList(context, cubit, state),
        Row(
          spacing: 4.0,
          children: [
            const SizedBox(
              width: 32,
              height: 32,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: InkWell(
                  child: DottedBorder(
                    radius: const Radius.circular(8),
                    borderType: BorderType.RRect,
                    color: appTheme.borderColorV2,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    child: Text(
                      'Add option',
                      style: AppStyle.textSmR.copyWith(color: appTheme.primaryColorV2),
                    ),
                  ),
                  onTap: () => cubit.addOption(),
                ),
              ),
            ),
            const SizedBox(
              width: 32,
              height: 32,
            ),
          ],
        )
      ],
    );
  }

  Widget _buildOptionList(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return ReorderableListView(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        for (final (index, option) in state.options.indexed)
          ListTile(
            key: ValueKey('option-$index'),
            leading: const SizedBox(
              width: 32,
              height: 32,
              child: Icon(Icons.menu, size: 24),
            ),
            title: ValueListenableBuilder(
              valueListenable: cubit.formHandler.formValidated,
              builder: (context, isValidate, _) => TitleTextFieldV2(
                fieldNode: option['handler'],
                validatedForm: isValidate,
              ),
            ),
            trailing: SizedBox(
              width: 32,
              height: 32,
              child: IconButton(
                padding: EdgeInsets.zero,
                icon: ImageAssetCustom(
                  imagePath: Assets.icons.iconDelete.path,
                ),
                onPressed: () => cubit.removeOption(index),
              ),
            ),
            contentPadding: const EdgeInsets.all(4),
          ),
      ],
      onReorder: (int oldIndex, int newIndex) {
        cubit.onReorder(oldIndex, newIndex);
      },
    );
  }

  Widget _buildDateline(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Set dateline', style: AppStyle.textSmR.copyWith(color: appTheme.primaryColorV2)),
        SizedBox(height: 8.h2),
        _buildFromDate(context, cubit, state),
        const SizedBox(height: 16),
        _buildToDate(context, cubit, state),
      ],
    );
  }

  Widget _buildFromDate(BuildContext context, ThreadDetailVotingUpsertCubit cubit, ThreadDetailVotingUpsertState state) {
    return Padding(
      padding: EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text('Start time', style: AppStyle.textMdR),
          InkWell(
            onTap: () => DatePickerBottomSheet.show(
              context,
              initialDate: state.fromTime,
              title: 'Start time',
              onSelected: (date) => {cubit.updateFromTime(date)},
            ),
            child: Container(
              padding: EdgeInsets.zero,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    state.fromTime != null ? DateFormat('dd/MM/yyyy hh:mm').format(state.fromTime!) : 'dd / mm / yyyy hh:mm',
                    style: TextStyle(
                      fontWeight: FontWeight.normal,
                      color: state.fromTime != null
                          ? state.isFromTimeValid
                              ? Colors.black
                              : Colors.red
                          : appTheme.grayV2,
                    ),
                  ),
                  const SizedBox(width: 8),
                  SvgPicture.asset(
                    Assets.icons.icCalendar.path,
                    width: 24.w2,
                    colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToDate(BuildContext context, ThreadDetailVotingUpsertCubit cubit, ThreadDetailVotingUpsertState state) {
    return Padding(
      padding: EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text('End time', style: AppStyle.textMdR),
          InkWell(
            onTap: () => DatePickerBottomSheet.show(
              context,
              initialDate: state.toTime,
              title: 'End time',
              onSelected: (date) => {cubit.updateToTime(date)},
            ),
            child: Container(
              padding: EdgeInsets.zero,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    state.toTime != null ? DateFormat('dd/MM/yyyy hh:mm').format(state.toTime!) : 'dd / mm / yyyy hh:mm',
                    style: TextStyle(
                      fontWeight: FontWeight.normal,
                      color: state.toTime != null
                          ? state.isToTimeValid
                              ? Colors.black
                              : Colors.red
                          : appTheme.grayV2,
                    ),
                  ),
                  const SizedBox(width: 8),
                  SvgPicture.asset(
                    Assets.icons.icCalendar.path,
                    width: 24.w2,
                    colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
