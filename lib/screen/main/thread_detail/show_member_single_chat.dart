import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../base/stream/base_list_stream_controller.dart';
import '../../../base/stream/base_stream_builder.dart';
import '../../../config/lang/locale_keys.g.dart';
import '../../../config/service/account_service.dart';
import '../../../config/service/app_service.dart';
import '../../../config/theme/style/style_theme.dart';
import '../../../data/model/account.dart';
import '../../../data/repository/family/ifamily_repository.dart';
import '../../../extension.dart';
import '../../../main.dart';
import '../../../utils/bottom_sheet.dart';
import '../../../widget/appbar_custom.dart';
import '../../../widget/avatar_circle_view.dart';
import '../../../widget/circle_checkbox.dart';
import '../../../widget/primary_button.dart';

class ShowMemberThreadBts extends StatefulWidget {
  const ShowMemberThreadBts({this.onSelected, required this.members, super.key});
  final List<Account> members;
  final Function(Account)? onSelected;


  static show(BuildContext context, {List<Account> members = const [], Function(Account)? onSelected}) {
    BottomSheetUtils.showHeight(
      context,
      child: ShowMemberThreadBts(
        members: members,
        onSelected: onSelected,
      ),
    );
  }

  @override
  State<ShowMemberThreadBts> createState() => _ShowMemberThreadBtsState();
}

class _ShowMemberThreadBtsState extends State<ShowMemberThreadBts> {
  final IFamilyRepository familyRepository = locator.get();
  late final BaseListStreamController<Account> members = BaseListStreamController<Account>(widget.members);
  final AccountService accountService = locator.get();

  @override
  void initState() {
    super.initState();
    // onFetchFamilyMember();
  }
  //

  @override
  void dispose() {
    members.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBarCustom(
          title: LocaleKeys.send_message_title.tr(),
          viewPadding: padding(horizontal: 16, top: 17, bottom: 15),
          showBack: false,
          actions: [
            GestureDetector(onTap: context.maybePop, child: const Icon(Icons.close, size: 22)),
          ],
        ),
        Expanded(
          child: BaseStreamBuilder(
            controller: members,
            builder: (members) => ListView.separated(
              physics: const BouncingScrollPhysics(),
              padding: padding(horizontal: 16),
              separatorBuilder: (context, index) => Divider(color: appTheme.borderColor),
              itemCount: members.length,
              itemBuilder: (context, index) => _buildMember(index, members),
            ),
          ),
        ),

      ],
    );
  }


  Widget _buildMember(int index, List<Account> membersList) {
    final member = membersList[index];
    print(member.toJson());
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: padding(top: 15, bottom: 14),
        child: Row(
          children: [
            AvatarCircleView(account: member, index: index, size: 40),
            const SizedBox(width: 12),
            Expanded(child: Text(member.fullName ?? '', style: AppStyle.regular14())),
            const SizedBox(width: 12),
            PrimaryButton(
              isFullWidth: false,
              onTap: () {
                widget.onSelected?.call(member);
                context.maybePop();
              },
              text: LocaleKeys.message.tr(),
              buttonPadding: padding(top: 7, bottom: 10, horizontal: 31),
            )
            // CircleCheckbox(isChecked: hasContain, onTap: onChangeStatusContain),
          ],
        ),
      ),
    );
  }
}