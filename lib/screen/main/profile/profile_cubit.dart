import 'dart:io';
import 'dart:ui' show Locale;

import 'package:archive/archive_io.dart';
import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/language_service.dart';
import 'package:family_app/config/service/log_service.dart';
import 'package:family_app/config/constant/timezone.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/usecase/sign_out_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/profile/profile_state.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

class ProfileCubit extends BaseCubit<ProfileState> {
  final IFamilyRepository familyRepository;
  final AccountService accountService;
  final SignOutUsecase signOutUsecase;
  final LanguageService languageService;
  late final LogService _logService;

  ProfileCubit({
    required this.familyRepository,
    required this.accountService,
    required this.signOutUsecase,
    required this.languageService,
  }) : super(ProfileState()) {
    _initLogService();
    detectAndSetTimezone();
  }

  Future<void> _initLogService() async {
    final appDir = await getApplicationDocumentsDirectory();
    final logDir = Directory('${appDir.path}/logs');
    if (!await logDir.exists()) {
      await logDir.create(recursive: true);
    }
    _logService = LogService(logDir);
  }

  void onLogout() async {
    try {
      showLoading();
      await signOutUsecase.call();
      emit(state.copyWith(status: ProfileStatus.logoutSuccess));
    } catch (e) {
      print(e);
    } finally {
      dismissLoading();
    }
  }

  Future<void> shareAppLog() async {
    try {
      showLoading();
      // Write a test log entry before exporting
      // await _logService.writeLog('Test log entry from shareAppLog at: '
      //     + DateTime.now().toIso8601String());
      await _logService.exportAndShareLogs();
    } catch (e) {
      print('Error sharing logs: $e');
    } finally {
      dismissLoading();
    }
  }

  void handleNotification(bool v) {
    state.notification.value = v;
  }

  void handleLocale(Locale v) {
    languageService.onSaveLanguage(v.languageCode);
  }

  void handleTimezone(Timezone v) {
    emit(state.copyWith(timezone: v));
  }

  void detectAndSetTimezone() {
    final userTimezoneName = DateTime.now().timeZoneName;
    final userTimezoneOffset = _formatTimezoneOffset(DateTime.now().timeZoneOffset);

    final defaultTimezone = listTimezone.firstWhere(
      (tz) => tz.name == userTimezoneName || tz.abbr == userTimezoneName || tz.offset == userTimezoneOffset,
      orElse: () {
        return listTimezone.first;
      },
    );

    emit(state.copyWith(timezone: defaultTimezone));
  }

  String _formatTimezoneOffset(Duration offset) {
    final hours = offset.inHours;
    final minutes = offset.inMinutes.remainder(60).abs();
    final sign = offset.isNegative ? '-' : '+';
    final hoursFormatted = hours.abs().toString();
    final minutesFormatted = minutes > 0 ? ':${minutes.toString().padLeft(2, '0')}' : '';
    return 'UTC$sign$hoursFormatted$minutesFormatted';
  }

  onTapEditProfile(BuildContext context) async {
    logd("onTapEditProfile");
    await context.pushRoute(const AccountEditRoute());
    await accountService.initMyProfile();
    emit(state.copyWith(updateCount: state.updateCount + 1));
  }
}
