import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/timezone.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/in_app_purchase_service.dart';
import 'package:family_app/config/service/language_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/profile/model/profile_item_model.dart';
import 'package:family_app/screen/main/profile/profile_cubit.dart';
import 'package:family_app/screen/main/profile/profile_state.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/button.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/header.dart';
import 'package:family_app/widget/image/avatar.dart';
import 'package:family_app/widget/line_widget.dart';
import 'package:family_app/widget/popup/popup.dart';
import 'package:family_app/widget/svg.dart';
import 'package:family_app/widget/switch.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

@RoutePage()
class ProfilePage extends BaseBlocProvider<ProfileState, ProfileCubit> {
  const ProfilePage({super.key});

  @override
  Widget buildPage() => const ProfileView();

  @override
  ProfileCubit createCubit() => ProfileCubit(
        familyRepository: locator.get(),
        accountService: locator.get(),
        signOutUsecase: locator.get(),
        languageService: locator.get(),
      );
}

class ProfileView extends StatefulWidget {
  const ProfileView({super.key});

  @override
  State<ProfileView> createState() => _ProfileViewState();
}

class _ProfileViewState
    extends BaseBlocPageStateV2<ProfileView, ProfileState, ProfileCubit> {
  final InAppPurchaseService _iapService = locator.get();
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    _loadAppVersion();
  }

  Future<void> _loadAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final versionPrefix = LocaleKeys.version.tr();
      if (mounted) {
        setState(() {
          _appVersion = '$versionPrefix ${packageInfo.version} (${packageInfo.buildNumber})';
        });
      }
    } catch (e) {
      print('Failed to get app version: $e');
      if (mounted) {
        setState(() {
          _appVersion = 'N/A';
        });
      }
    }
  }

  @override
  bool listenWhen(ProfileState previous, ProfileState current) {
    if (previous.status != current.status) {
      if (current.status == ProfileStatus.logoutSuccess) {
        context.replaceRoute(const SignInRoute());
        showSimpleToast(LocaleKeys.logout_successful_text.tr());
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(
      BuildContext context, ProfileCubit cubit, ProfileState state) {
    final email = accountService.account?.email;
    final name = accountService.account?.fullName;
    var photoUrl = accountService.account?.photoUrl;

    return Padding(
      padding: paddingV2(top: 16, horizontal: 8)
          .add(EdgeInsets.only(top: context.top)),
      child: Column(children: [
        Header(LocaleKeys.setting.tr(),
            action: ButtonIcon(Assets.icons.icLogout.path,
                () => DialogUtils.showSignOutDialog(context),
                bg: appTheme.errorV2.withValues(alpha: 0.12))),
        SizedBox(height: 8.h2),
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.only(bottom: context.bottom),
            child: Column(children: [
              _boxBuilder([
                Padding(
                  padding: paddingV2(horizontal: 16),
                  child: Row(children: [
                    Avatar(photoUrl, name: name, size: 48.w2),
                    SizedBox(width: 16.w2),
                    Expanded(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(children: [
                              Flexible(
                                  child: Text(name ?? '',
                                      style: AppStyle.bold16V2(),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis)),
                              SizedBox(width: 8.w2),
                              _iapService.premiumBuilder(
                                (context, isPremium, _) => Container(
                                  padding: paddingV2(horizontal: 8),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8.w2),
                                    color: isPremium ? null : appTheme.grayV2,
                                    gradient: isPremium
                                        ? LinearGradient(
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                            colors: [
                                              appTheme.blueColorV2,
                                              appTheme.errorV2
                                            ],
                                          )
                                        : null,
                                  ),
                                  child: Text(
                                      (isPremium
                                              ? LocaleKeys.premium
                                              : LocaleKeys.free)
                                          .tr()
                                          .toUpperCase(),
                                      style: AppStyle.bold9V2(
                                          color: Colors.white)),
                                ),
                              ),
                            ]),
                            if (email?.isNotEmpty ?? false)
                              Text(email!,
                                  style: AppStyle.regular12V2(
                                      color: appTheme.grayV2),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis),
                          ]),
                    ),
                    SizedBox(width: 16.w2),
                    ButtonIcon(Assets.icons.icEdit.path,
                        () => cubit.onTapEditProfile(context),
                        bg: appTheme.backgroundV2),
                  ]),
                ),
              ]),
              _iapService.premiumBuilder(
                (context, isPremium, child) =>
                    isPremium ? const SizedBox.shrink() : child!,
                Button(
                  margin: paddingV2(top: 8),
                  borderRadius: BorderRadius.circular(56.h2),
                  onTap: () => context.pushRoute(const PremiumRoute()),
                  clipBehavior: Clip.hardEdge,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [appTheme.blueColorV2, appTheme.errorV2],
                  ),
                  child: Stack(alignment: Alignment.center, children: [
                    Padding(
                      padding: paddingV2(all: 16, right: 24),
                      child: Row(children: [
                        Svg(Assets.icons.icLightningBolt.path, height: 20.w2),
                        SizedBox(width: 4.w2),
                        Expanded(
                          child: Text(LocaleKeys.upgrade_premium_now.tr(),
                              style: AppStyle.bold16V2(color: Colors.white)),
                        ),
                        SizedBox(width: 4.w2),
                        Svg(Assets.icons.icArrowRight.path, size: 16.w2),
                      ]),
                    ),
                    Positioned(
                      right: 44.w2,
                      child: Svg(Assets.images.logo.path,
                          size: 86.w2,
                          color: const Color.fromRGBO(225, 225, 225, 0.3)),
                    ),
                  ]),
                ),
              ),
              SizedBox(height: 8.h2),
              _boxBuilder([
                _btnBuilder(Assets.icons.icUsers.path, LocaleKeys.manage_family,
                    onTap: () => context.pushRoute(const FamilyListRoute())),
                _btnBuilder(
                    Assets.icons.icLock.path, LocaleKeys.change_password,
                    onTap: () => context.pushRoute(const ChangePasswordRoute())),
                _btnBuilder(Assets.icons.iconNotificaiton.path,
                    LocaleKeys.notifications,
                    child: ValueListenableBuilder(
                        valueListenable: state.notification,
                        builder: (context, enabled, child) =>
                            SwitchCustom(enabled, cubit.handleNotification))),
              ]),
              SizedBox(height: 8.h2),
              _boxBuilder([
                _btnBuilder(Assets.icons.icGlobal.path, LocaleKeys.language,
                    child: _dropdownBuilder(
                        context.supportedLocales,
                        context.locale,
                        cubit.handleLocale,
                        (e) => e.translate)),
                _btnBuilder(Assets.icons.icClock.path, LocaleKeys.timezone,
                    child: _dropdownBuilder(listTimezone, state.timezone,
                        cubit.handleTimezone, (e) => e.offset)),
              ]),
              SizedBox(height: 8.h2),
              _boxBuilder([
                _btnBuilder(Assets.icons.icHelp.path, LocaleKeys.help,
                    onTap: () {}),
                _btnBuilder(
                    Assets.icons.icDoubleBubbles.path, LocaleKeys.send_feedback,
                    onTap: () => cubit.shareAppLog()),
                _btnBuilder(Assets.icons.icInfo.path, LocaleKeys.about,
                    onTap: () {}),
              ]),
              SizedBox(height: 24.h2),
              Text(
                _appVersion,
                style: AppStyle.regular12V2(color: appTheme.grayV2),
              ),
              SizedBox(height: 16.h2),
            ]),
          ),
        ),
      ]),
    );

    // return TwoBaseStreamBuilder(
    //   firstController: cubit.accountService.accountStream,
    //   secondController: cubit.accountService.myActiveFamily,
    //   builder: (account, activeFamily) {
    //     // final activity = ProfileGroupItem(items: [
    //     //   ProfileItemModel(
    //     //     title: LocaleKeys.my_activity_text.tr(),
    //     //     imagePath: Assets.images.activity.path,
    //     //     pageRouteInfo: const ListActivityRoute(),
    //     //   )
    //     // ]);
    //     final family = ProfileGroupItem(items: [
    //       ProfileItemModel(
    //         title: LocaleKeys.my_family_text.tr(),
    //         content: activeFamily?.familyName ?? account?.familyName ?? '',
    //         pageRouteInfo: const FamilyListRoute(),
    //         imagePath: Assets.images.myFamily.path,
    //       )
    //     ]);
    //     // final recycle = ProfileGroupItem(items: [
    //     //   ProfileItemModel(
    //     //     title: LocaleKeys.recycle_text.tr(),
    //     //     imagePath: Assets.images.recycle.path,
    //     //   )
    //     // ]);
    //     final about = ProfileGroupItem(title: LocaleKeys.about_family_link_text.tr(), items: [
    //       ProfileItemModel(
    //           title: LocaleKeys.send_feedback_text.tr(),
    //           imagePath: Assets.images.feedback.path,
    //           onTap: () => {
    //                 if (Platform.isIOS) {cubit.shareAppLog2()} else if (Platform.isAndroid) {cubit.shareAppLog()}
    //               }),
    //       ProfileItemModel(
    //         title: LocaleKeys.help_center_text.tr(),
    //         imagePath: Assets.images.help.path,
    //       ),
    //       ProfileItemModel(
    //         title: LocaleKeys.privacy_policy.tr(),
    //         imagePath: Assets.images.privacyPolicy.path,
    //         onTap: () => context.pushRoute(
    //           PolicyRoute(parameter: PolicyParameter(type: PolicyType.privacy)),
    //         ),
    //       ),
    //       ProfileItemModel(
    //         title: LocaleKeys.sign_out_title.tr(),
    //         imagePath: Assets.images.exit.path,
    //         onTap: () => DialogUtils.showSignOutDialog(context),
    //       ),
    //     ]);
    //
    //     return Stack(
    //       children: [
    //         Image.asset(Assets.images.bgProfile.path),
    //         SafeArea(
    //           child: Padding(
    //             padding: padding(top: 32 + 24, horizontal: 16),
    //             child: Column(
    //               children: [
    //                 Row(
    //                   children: [
    //                     CircleAvatarCustom(borderWidth: 3.w, size: 80, imageUrl: ''),
    //                     // "https://vrelay-vn1.5gencare.com/v1/family/photo/fb092da0-a37b-49da-b478-092276c30708/8b810c9f-80ff-458b-b600-8f97f45a7efa/profile.jpg"), //activeFamily?.photoUrl ?? ''),
    //                     SizedBox(width: 16.w),
    //                     Column(
    //                       crossAxisAlignment: CrossAxisAlignment.start,
    //                       children: [
    //                         RichText(
    //                           text: TextSpan(
    //                             children: [
    //                               TextSpan(text: account?.fullName ?? '', style: AppStyle.medium22()),
    //                               const WidgetSpan(child: SizedBox(width: 8)),
    //                               TextSpan(
    //                                 text: activeFamily?.relationship ?? '',
    //                                 style: AppStyle.medium14(color: appTheme.fadeTextColor),
    //                               ),
    //                             ],
    //                           ),
    //                         ),
    //                         SizedBox(height: 12.h),
    //                         Text(account?.email ?? '', style: AppStyle.regular14()),
    //                       ],
    //                     ),
    //                   ],
    //                 ),
    //                 SizedBox(height: 16.h),
    //                 //Phung : disable "My Activity "
    //                 // _buildBackgroundView(activity),
    //                 _buildBackgroundView(family),
    //                 // disable "Recycle" _buildBackgroundView(recycle),
    //                 _buildBackgroundView(about),
    //               ],
    //             ),
    //           ),
    //         ),
    //         Positioned(
    //           top: 24,
    //           right: 0,
    //           child: SafeArea(
    //             child: GestureDetector(
    //               onTap: () => context.pushRoute(const AccountEditRoute()),
    //               child: Image.asset(Assets.images.edit.path, width: 56, height: 56),
    //             ),
    //           ),
    //         ),
    //         Positioned(
    //           top: 0,
    //           left: 0,
    //           child: SafeArea(
    //               child: IconButton(
    //                   onPressed: () {
    //                     context.maybePop();
    //                   },
    //                   icon: Svg(Assets.icons.back.path))),
    //         ),
    //       ],
    //     );
    //   },
    // );
  }

  Widget _btnBuilder(String ic, String label,
      {VoidCallback? onTap, Widget? child}) {
    return Button(
      onTap: onTap,
      padding: paddingV2(all: 8, horizontal: 24),
      child: Row(children: [
        Svg(ic, size: 24.w2),
        SizedBox(width: 8.w2),
        Expanded(
            child: Text(label.tr(),
                style: AppStyle.regular14V2(),
                overflow: TextOverflow.ellipsis,
                maxLines: 1)),
        if (child != null) child,
      ]),
    );
  }

  Widget _boxBuilder(Iterable<Widget> children) {
    return Container(
      padding: paddingV2(vertical: 16),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(24.w2)),
      child: children.length == 1
          ? children.elementAt(0)
          : Column(children: children.toList()),
    );
  }

  Widget _buildBackgroundView(ProfileGroupItem group) {
    final isOnlyOne = group.items.length == 1;
    return GestureDetector(
      onTap: isOnlyOne
          ? () {
              final items = group.items.first;
              if (items.onTap != null) {
                items.onTap!();
              } else if (items.pageRouteInfo != null) {
                context.pushRoute(items.pageRouteInfo!);
              }
            }
          : null,
      behavior: HitTestBehavior.opaque,
      child: Container(
        margin: padding(bottom: 12),
        padding: padding(left: 12, vertical: 15, right: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: appTheme.whiteText,
          boxShadow: [
            const BoxShadow(
                color: Color(0x185F657C), offset: Offset(0, 0), blurRadius: 4),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (group.title.isNotEmpty) ...[
              Text(LocaleKeys.about_family_link_text.tr(),
                  style: AppStyle.regular12(color: appTheme.fadeTextColor)),
              SizedBox(height: 19.h),
            ],
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (context, index) => Padding(
                padding: padding(vertical: 16),
                child: const LineWidget(),
              ),
              itemCount: group.items.length,
              itemBuilder: (context, index) =>
                  itemRowWidget(group.items[index], isOnlyOne),
            ),
          ],
        ),
      ),
    );
  }

  Widget itemRowWidget(ProfileItemModel item, bool isOnlyOne) {
    return GestureDetector(
      onTap: isOnlyOne
          ? null
          : () {
              if (item.onTap != null) {
                item.onTap!();
              } else if (item.pageRouteInfo != null) {
                context.pushRoute(item.pageRouteInfo!);
              }
            },
      behavior: HitTestBehavior.opaque,
      child: Row(
        children: [
          Image.asset(item.imagePath, width: 20.w),
          SizedBox(width: 8.w),
          Expanded(child: Text(item.title, style: AppStyle.regular14())),
          if (item.content != null) ...[
            Text(item.content ?? '', style: AppStyle.regular14()),
            SizedBox(width: 16.w),
          ],
          item.action ?? Assets.images.arrowRight.image(width: 14, height: 14),
        ],
      ),
    );
  }

  Widget _dropdownBuilder<T>(Iterable<T> data, T value,
      ValueChanged<T> onChanged, String Function(T) textBuilder) {
    return CustomPopup(
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: data
              .map((e) => GestureDetector(
                  onTap: () {
                    context.maybePop();
                    onChanged(e);
                  },
                  child: Padding(
                      padding: paddingV2(all: 8), child: Text(textBuilder(e)))))
              .toList(),
        ),
      ),
      child: Container(
        padding: paddingV2(all: 8, vertical: 4),
        decoration: BoxDecoration(
          border: Border.all(width: 1.w2, color: appTheme.borderColorV2),
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.w2),
        ),
        child: Row(children: [
          SizedBox(
              width: 72.w2,
              child: Text(textBuilder(value),
                  maxLines: 1, overflow: TextOverflow.ellipsis)),
          SizedBox(width: 8.w2),
          Svg(Assets.icons.icArrowDown.path, size: 16.w2),
        ]),
      ),
    );
  }
}
