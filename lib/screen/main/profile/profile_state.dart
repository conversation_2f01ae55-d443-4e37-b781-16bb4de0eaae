import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/config/constant/timezone.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:flutter/material.dart' show ValueNotifier;

enum ProfileStatus { none, loading, success, error, logoutSuccess }

class ProfileState extends BaseState {
  final ProfileStatus status;

  final ValueNotifier<bool> notification;
  final int updateCount;
  final Timezone timezone;

  ProfileState({
    Timezone? timezone,
    bool notification = false,
    this.status = ProfileStatus.none,
    this.updateCount = 0,
  })  : notification = ValueNotifier(notification),
        timezone = timezone ?? listTimezone.first;

  ProfileState copyWith({
    ProfileStatus? status,
    bool? notification,
    Timezone? timezone,
    int? updateCount,
  }) =>
      ProfileState(
        status: status ?? this.status,
        timezone: timezone ?? this.timezone,
        notification: notification ?? this.notification.value,
        updateCount: updateCount ?? this.updateCount,
      );

  @override
  List<Object?> get props => [status, timezone, updateCount];
}
