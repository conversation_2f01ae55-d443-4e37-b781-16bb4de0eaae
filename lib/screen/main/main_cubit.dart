import 'dart:isolate';
import 'dart:ui';

import 'package:auto_route/auto_route.dart';
import 'package:dartx/dartx.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/notification/notification_service.dart';
import 'package:family_app/config/service/socket/base_socket_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/category.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/category/icategory_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/screen/main/main_state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/mixin/list_item_mixin.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class MainCubit extends BaseCubit<MainState> with ListItemMixin {
  final ICategoryRepository categoryRepository;
  final IListRepository listRepository;
  final AccountService accountService;
  final IActivityRepository activityRepository;
  final BaseSocketService socketService;
  final NotificationService notificationService;
  final IFamilyRepository familyRepository;

  late TabsRouter tabRouter;

  MainCubit({
    required this.categoryRepository,
    required this.listRepository,
    required this.accountService,
    required this.activityRepository,
    required this.socketService,
    required this.familyRepository,
    required this.notificationService,
  }) : super(MainState(currentMonth: DateTime.now()));

  void onMoveTab(int tab) {
    tabRouter.setActiveIndex(tab);
  }

  final ReceivePort _receivePort = ReceivePort();

  @override
  void onInit() async {
    locator.registerSingleton(this);
    // socketService.onConnect();
    super.onInit();
    onRefresh();
    updateFcmToken();
    notificationService.onHandleInitialMessage();
    _setupIsolated();
  }

  void updateFcmToken() async {
    try {
      await notificationService.onRequestPermission();
      // if (result == true) {
      final fcmToken = await notificationService.getFcmToken();
      await familyRepository.updateNotificationSetting(fcmToken ?? '');
      // }
    } catch (e) {
      print(e);
    }
  }

  Future<void> onRefresh() async {
    final categories = await categoryRepository.getAllCategory();
    final lists = await listRepository.getListByFamilyId(accountService.familyId);
    final newList = await getItemCategoryForListItem(categories, lists);
    final result = await activityRepository.getAllActivities(accountService.familyId);
    final upcoming = await familyRepository.getUpcoming(
      accountService.familyId,
      from: state.currentMonth.startDayOfWeek,
      to: state.currentMonth.endDayOfWeek,
    );

    List<ActivityModel> activeActivities = [];

    for (var e in result) {
      final toDate = e.toDate?.toLocalDT;
      if (toDate == null) {
        continue;
      }
      if (toDate.isAfterToday || toDate.isToday) {
        activeActivities.add(e);
      }
    }

    emit(state.copyWith(listItems: newList, activityList: activeActivities, upcoming: upcoming));
  }

  Future<List<ListItem>> getItemCategoryForListItem(List<Category> categories, List<ListItem> items) async {
    final newListItems = await Future.wait(items.map((item) => getDetailListItem(categories, item)));
    return newListItems;
  }

  @override
  Future<void> close() {
    locator.unregister(instance: this);
    _receivePort.close();
    return super.close();
  }

  void updateListItem(ListItem item) {
    final newLists = [...state.listItems];
    final index = state.listItems.indexWhere((e) => e.uuid == item.uuid);
    if (index != -1) {
      newLists[index] = item;
    } else {
      newLists.add(item);
    }
    emit(state.copyWith(listItems: newLists));
  }

  void updateListActivity(ActivityModel item) {
    final newActivity = [...state.activityList];
    final index = state.activityList.indexWhere((e) => e.uuid == item.uuid);
    if (index != -1) {
      newActivity[index] = item;
    } else {
      newActivity.add(item);
    }
    emit(state.copyWith(activityList: newActivity));
  }

  Future<void> updateItemStatusInList(ListItem listItem, int listIndex, int itemIndex) async {
    final items = <Item>[...(listItem.items ?? <Item>[])];
    final originalStatus = items[itemIndex].status;
    final newStatus = items[itemIndex].status == 0 ? 1 : 0;
    items[itemIndex].status = newStatus;
    listItem.items = items;
    updateListItemByType(listItem, listIndex: listIndex);
    try {
      final result = await listRepository.changeStatusItemInList(items[itemIndex].uuid ?? '', newStatus);
      if (result != null) {
        items[itemIndex] = result;
      } else {
        items[itemIndex].status = originalStatus;
      }
      updateListItemByType(listItem, listIndex: listIndex);
    } catch (e) {
      items[itemIndex].status = originalStatus;
      updateListItemByType(listItem, listIndex: listIndex);
    }
  }

  void updateListItemByType(ListItem listItem, {int? listIndex}) {
    final index = listIndex ?? state.listItems.indexWhere((e) => e.uuid == listItem.uuid);
    if (index != -1) {
      final list = <ListItem>[...(state.listItems)];
      list[index] = listItem;
      emit(state.copyWith(listItems: list, count: state.count + 1));
    }
  }

  // void fetchActivity() async {
  //   try {
  //     final result = await activityRepository.getAllActivities(accountService.familyId);

  //     List<ActivityModels> activeActivities = [];

  //     for (var e in result) {
  //       if (e.fromDate?.toLocalDT.isBeforeNow ?? false) {
  //         activeActivities.add(e);
  //       }
  //     }
  //     emit(state.copyWith(activityList: activeActivities));
  //   } catch (e) {
  //     print(e);
  //   }
  // }

  void upsertActivity(ActivityModel? activity) {
    final acts = [...state.activityList];
    final index = state.activityList.indexWhere((e) => e.uuid == activity?.uuid);
    if (index != -1) {
      acts[index] = activity!;
    } else {
      acts.add(activity!);
    }
    emit(state.copyWith(activityList: acts));
  }

  void deleteActivity(String id) {
    final acts = [...state.activityList];
    final index = state.activityList.indexWhere((e) => e.uuid == id);
    if (index != -1) {
      acts.removeAt(index);
    }
    emit(state.copyWith(activityList: acts));
  }

  void deleteListItem(String id) {
    final items = [...state.listItems];
    final activities = [...state.activityList];
    final index = state.listItems.indexWhere((e) => e.uuid == id);
    if (index != -1) {
      items.removeAt(index);
    }

    for (var i = 0; i < activities.length; i++) {
      activities[i].includedLists?.removeWhere((e) => e.uuid == id);
    }
    emit(state.copyWith(listItems: items, activityList: activities));
  }

  void onRemoveEvent(String eventId) {
    final activities = [...state.activityList];

    for (var i = 0; i < activities.length; i++) {
      activities[i].includedEvents?.removeWhere((e) => e.uuid == eventId);
    }
    emit(state.copyWith(activityList: activities));
  }

  void updateActivityInListItem(List<String> listItemIds, ActivityModel activity) {
    final list = [...state.listItems];
    for (final id in listItemIds) {
      final index = list.indexWhere((e) => e.uuid == id);
      if (index != -1) {
        list[index].setActivity(activity);
      }
    }
    emit(state.copyWith(listItems: list));
  }

  void onRemoveActivityIdInList(List<String> listItemIds) {
    final list = [...state.listItems];
    for (final id in listItemIds) {
      final index = list.indexWhere((e) => e.uuid == id);
      if (index != -1) {
        list[index].setActivity(null);
      }
    }
    emit(state.copyWith(listItems: list));
  }

  void _setupIsolated() async {
    IsolateNameServer.removePortNameMapping(AppConfig.notificationRoleReceivePort);
    IsolateNameServer.registerPortWithName(_receivePort.sendPort, AppConfig.notificationRoleReceivePort);

    _receivePort.listen((valueData) async {
      if (valueData is! Map<String, dynamic>) return;
      try {
        final message = RemoteMessage.fromMap(valueData);
        if (message.data['type'] == 'role') {
          await accountService.initMyProfile();
        }
      } catch (e) {
        // No-op
      }
    });
  }
}
