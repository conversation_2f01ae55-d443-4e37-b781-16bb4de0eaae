import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/main_cubit.dart';
import 'package:family_app/screen/main/main_state.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

@RoutePage()
class MainPage extends BaseBlocProvider<MainState, MainCubit> {
  const MainPage({super.key});

  @override
  Widget buildPage() => const MainView();

  @override
  MainCubit createCubit() => MainCubit(
        categoryRepository: locator.get(),
        listRepository: locator.get(),
        accountService: locator.get(),
        activityRepository: locator.get(),
        socketService: locator.get(),
        notificationService: locator.get(),
        familyRepository: locator.get(),
      );
}

class MainView extends StatefulWidget {
  const MainView({super.key});

  @override
  State<MainView> createState() => _MainViewState();
}

class _MainViewState extends BaseBlocNoAppBarPageState<MainView, MainState, MainCubit> {
  @override
  bool get isSafeArea => false;

  @override
  Widget buildBody(BuildContext context, MainCubit cubit, MainState state) {
    final bottomTab = [
      BottomNavItem(
        imagePath: Assets.icons.homeOff.path,
        text: LocaleKeys.home.tr(),
        activeImage: Assets.icons.homeOn.path,
      ),
      BottomNavItem(
        imagePath: Assets.icons.calendarOff.path,
        text: LocaleKeys.calendar.tr(),
        activeImage: Assets.icons.calendarOn.path,
      ),
      BottomNavItem(
        imagePath: Assets.images.addBorder.path,
        activeImage: Assets.images.addBorder.path,
        size: 40,
      ),
      BottomNavItem(
        imagePath: Assets.icons.checklistOff.path,
        text: LocaleKeys.check_list.tr(),
        activeImage: Assets.icons.checklistOn.path,
      ),
      //Profile
      BottomNavItem(
        imagePath: Assets.icons.profileOff.path,
        text: LocaleKeys.profile.tr(),
        activeImage: Assets.icons.profileOn.path,
      ),

      ///Memories
      BottomNavItem(
        imagePath: Assets.icons.checklistOff.path,
        text: LocaleKeys.memories.tr(),
        activeImage: Assets.icons.checklistOn.path,
      ),
    ];

    return AutoTabsScaffold(
      routes: [
        const HomeRoute(),
        const CalendarRoute(),
        const HomeRoute(),
        const CheckListRoute(),
        const ProfileRoute(),
        const MemoriesRoute(),
      ],
      bottomNavigationBuilder: (_, tabRouter) {
        cubit.tabRouter = tabRouter;
        return BottomNavigationBar(
          currentIndex: tabRouter.activeIndex,
          onTap: (index) {
            if (index == 2) {
              //context.pushRoute(const AiButlerRoute());
              context.pushRoute(const ActivityHomeRoute());
              return;
            }
            tabRouter.setActiveIndex(index);
            if (index != 0) {
              SystemChrome.setSystemUIOverlayStyle(
                const SystemUiOverlayStyle(statusBarIconBrightness: Brightness.dark),
              );
            }
          },
          unselectedLabelStyle: AppStyle.regular10(color: appTheme.fadeTextColor),
          selectedLabelStyle: AppStyle.medium10(color: appTheme.primaryColor),
          selectedItemColor: appTheme.primaryColor,
          unselectedItemColor: appTheme.fadeTextColor,
          backgroundColor: appTheme.whiteText,
          showUnselectedLabels: true,
          type: BottomNavigationBarType.fixed,
          items: bottomTab.asMap().entries.map((tab) {
            final item = tab.value;
            return BottomNavigationBarItem(
              label: item.text,
              activeIcon: item.isShow
                  ? ImageAssetCustom(
                      imagePath: item.activeImage,
                      width: item.size,
                      height: item.size,
                    )
                  : null,
              icon: item.isShow
                  ? ImageAssetCustom(imagePath: item.imagePath, width: item.size, height: item.size)
                  : const SizedBox(),
            );
          }).toList(),
        );
      },
    );
  }
}

class BottomNavItem {
  final String text;
  final String imagePath;
  final String activeImage;
  final Color? activeColor;
  final bool isShow;
  final double size;

  BottomNavItem({
    this.text = '',
    this.imagePath = '',
    this.activeImage = '',
    this.activeColor,
    this.isShow = true,
    this.size = 24,
  });
}
