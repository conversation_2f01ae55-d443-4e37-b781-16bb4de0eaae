import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/screen/main/search/search_state.dart';

class SearchCubit extends BaseCubit<SearchState> {
  final IEventRepository eventRepository;
  final AccountService accountService;

  SearchCubit({required this.eventRepository, required this.accountService}) : super(SearchState());

  void updateQuery(String query) {
    emit(state.copyWith(query: query, isSearching: query.isNotEmpty));
  }

  Future<void> search(String query) async {
    try {
      final result = await eventRepository.getEventInFamily(accountService.familyId, search: query.trim());
      groupEventsByDate(result);
      emit(state.copyWith(models: result));
    } catch (e) {
      print(e);
    }
  }

  void groupEventsByDate(List<EventModels> events) {
    final grouped = <String, List<EventModels>>{};
    for (final event in events) {
      final dateKey = DateFormat(FULL_DAY_FORMAT).format(DateTime.parse(event.createdAt ?? ''));
      grouped.putIfAbsent(dateKey, () => []).add(event);
    }
    emit(state.copyWith(groupedEvents: grouped));
  }
}
