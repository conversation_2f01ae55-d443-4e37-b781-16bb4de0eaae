
import 'dart:async';

import 'package:family_app/screen/main/thread/thread_state.dart';

import '../../../base/widget/cubit/base_cubit.dart';
import '../../../config/service/account_service.dart';
import '../../../config/service/app_service.dart';
import '../../../data/repository/thread/ithread_repository.dart';
import '../../../data/repository/thread/model/thread_parameter.dart';
import '../../../router/app_route.dart';
import 'member_model.dart';

class ThreadCubit extends BaseCubit<ThreadState> {
  final IThreadRepository familyRepository;
  final AccountService accountService;
  final StreamController<ThreadDetailRoute> _navigationController = StreamController();


  Stream<ThreadDetailRoute> get navigationStream => _navigationController.stream;

  ThreadCubit({
    required this.familyRepository,
    required this.accountService,
  }) : super(ThreadState());

  @override
  void onInit() {
    super.onInit();
    accountService.initMyProfile();
    // print("accountService.familyId: ${accountService.account?.activeFamily}");
    // print("accountService.familyId: ${accountService.familyId}");
    // emit(state.copyWith(listMember: accountService.memberInFamily.value));
    onFetchAllThread(accountService.familyId);
    showMember();
  }

  showMember() {
    var listMember = accountService.memberInFamily.value;
    var listMemberModel = listMember.map((e) => MemberModel(e, true)).toList();
    listMemberModel.removeWhere((element) => element.account.familyMemberUuid?.isEmpty ?? true);
    emit(state.copyWith(listMember: listMemberModel, selectedMembers: listMemberModel));
  }

  void toggleSelection(MemberModel member) {
    final updatedSelectedMembers =
        List<MemberModel>.from(state.selectedMembers);
    if (updatedSelectedMembers.contains(member)) {
      updatedSelectedMembers.remove(member);
    } else {
      updatedSelectedMembers.add(member);
    }
    emit(state.copyWith(selectedMembers: updatedSelectedMembers));
  }

  onFetchAllThread(String familyId) async {
    try {
      emit(state.copyWith(status: ThreadStatus.loading));
      final result = await familyRepository.getAllThread(familyId);
      var allThreads = result.where((element) => element.members != null).toList();
      //Sort all thread by the latest message time
      allThreads.sort((a, b) {
        if (a.latestMessageTime == null) {
          return 1;
        }
        if (b.latestMessageTime == null) {
          return -1;
        }
        return b.latestMessageTime!.compareTo(a.latestMessageTime!);
      });

      emit(state.copyWith(
          status: ThreadStatus.success, listMessageFamily: allThreads));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadStatus.error, errorMessage: e.toString()));
    }
  }

  createThread(String topic) async {
    try {
      emit(state.copyWith(status: ThreadStatus.loading));
      final result = await familyRepository.createThread(
          ThreadParameter(
            familyId: accountService.familyId,
            name: topic,
            members: state.selectedMembers.map((e) => e.account.familyMemberUuid.toString()).toList(),
            color: "",
            image: "",
            setting: [],
          ),
      );
      if (result.uuid != null) {
        _navigationController.add(ThreadDetailRoute(parameter: result));
        emit(state.copyWith(threadDetail: result));
        // await onFetchAllThread(accountService.familyId);
        emit(state.copyWith(selectedMembers: []));
      }

    } catch (e) {
      emit(state.copyWith(
          status: ThreadStatus.error, errorMessage: e.toString()));
    }
  }

  @override
  Future<void> close() {
    _navigationController.close();
    return super.close();
  }

  cancelCreateThread() {
    emit(state.copyWith(selectedMembers: []));
  }

  // pushToThreadDetail() {
  //   context.pushRoute(
  //     ThreadDetailRoute(parameter: message),
  //   ).then((value) {
  //     cubit.onFetchAllThread(accountService.familyId);
  //   });
  // }

}
