import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/utils/util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

import '../../../base/widget/cubit/base_bloc_page.dart';
import '../../../base/widget/cubit/base_bloc_provider.dart';
import '../../../config/lang/locale_keys.g.dart';
import '../../../config/service/app_service.dart';
import '../../../config/theme/style/style_theme.dart';
import '../../../gen/assets.gen.dart';
import '../../../main.dart';
import '../../../utils/assets/shadow_util.dart';
import '../../../widget/appbar_custom.dart';
import '../../../widget/image_asset_custom.dart';
import 'member_selection_bottom_sheet.dart';
import 'thread_cubit.dart';
import 'thread_state.dart';

@RoutePage()
class ThreadPage extends BaseBlocProvider<ThreadState, ThreadCubit> {
  const ThreadPage({super.key});

  @override
  Widget buildPage() => const ThreadView();

  @override
  ThreadCubit createCubit() => ThreadCubit(
        familyRepository: locator.get(),
        accountService: locator.get(),
      );
}

class ThreadView extends StatefulWidget {
  const ThreadView({super.key});

  @override
  State<ThreadView> createState() => _ThreadViewState();
}

class _ThreadViewState
    extends BaseBlocPageState<ThreadView, ThreadState, ThreadCubit>
    with SingleTickerProviderStateMixin {
  late ThreadCubit cubit;

  @override
  void onTapScreen(BuildContext context) {
    super.onTapScreen(context);
  }

  @override
  void initState() {
    super.initState();
    cubit = context.read<ThreadCubit>();
    cubit.navigationStream.listen((route) {
      context.pushRoute(route).then((value) {
        cubit.onFetchAllThread(accountService.familyId);
      });
    });
  }

  Widget buildAppBar(
      BuildContext context, ThreadCubit cubit, ThreadState state) {
    return CustomAppBar2(
      title: LocaleKeys.message.tr(),
      showBack: true,
      actions: [],
    );
  }

  @override
  Widget buildBody(BuildContext context, ThreadCubit cubit, ThreadState state) {
    if (state.status == ThreadStatus.loading) {
      return const Center(child: CircularProgressIndicator());
    } else if (state.status == ThreadStatus.error) {
      return Center(
        child: Text(state.errorMessage ?? "Error"),
      );
    } else if (state.status == ThreadStatus.success) {
      if (state.listMessageFamily.isEmpty) {
        return _buildEmptyView(cubit);
      } else {
        return Stack(
          children: [
            Container(
              height: double.infinity,
              width: double.infinity,
              margin: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: appTheme.whiteText,
                boxShadow: ShadowUtil.backgroundShadow,
              ),
              child: ListView.builder(
                itemCount: state.listMessageFamily.length,
                itemBuilder: (context, index) {
                  final message = state.listMessageFamily[index];
                  return _dataView(cubit, message);
                },
              ),
            ),
            Positioned(
              bottom: 10,
              right: 0,
              child: SafeArea(
                child: IconButton(
                  onPressed: () {
                    cubit.showMember();
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      builder: (context) =>
                          MemberSelectionBottomSheet(cubit: cubit),
                    );
                  },
                  icon: SvgPicture.asset(Assets.icons.iconAdd.path),
                ),
              ),
            ),
          ],
        );
      }
    }
    return Container();
  }

  Widget _buildEmptyView(ThreadCubit cubit) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: backgroundColor ?? appTheme.whiteText,
              boxShadow: ShadowUtil.backgroundShadow,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ImageAssetCustom(
                  imagePath: Assets.icons.messageEmpty.path,
                  width: 140,
                ),
                const SizedBox(height: 16),
                Text("You don’t have any messages yet",
                    style: AppStyle.medium16(
                      color: appTheme.blackColor,
                    )),
                const SizedBox(height: 16),
                Text(
                    "Start the conversation and keep everyone connected. Send a message now!",
                    textAlign: TextAlign.center,
                    style: AppStyle.regular14(
                      color: appTheme.grayColor,
                    )),
                const SizedBox(height: 16),
                InkWell(
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      builder: (context) =>
                          MemberSelectionBottomSheet(cubit: cubit),
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: appTheme.lightBotColor,
                      boxShadow: ShadowUtil.backgroundShadow,
                    ),
                    child: Text("New message",
                        style: AppStyle.medium16(
                          color: appTheme.whiteText,
                        )),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _dataView(ThreadCubit cubit, ThreadFamily message) {
    var name = "";
    Map<String, dynamic>? userMap;
    bool isMe = false;
    if (message.members!.length > 2) {
      for (var i = 0; i < message.members!.length; i++) {
        if (i == 0) {
          name = message.members![i].fullName!;
        } else {
          name = name + ", " + message.members![i].fullName!;
        }
      }
    } else {
      name = message.members![0].fullName.toString();
    }

    if (message.latestMessageUserName != null &&
        message.latestMessageUserName!.isNotEmpty) {
      userMap = jsonDecode(message.latestMessageUserName.toString());
      isMe = userMap?["uuid"] == accountService.account?.uuid;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        InkWell(
          onTap: () {
            context
                .pushRoute(
              ThreadDetailRoute(parameter: message),
            )
                .then((value) {
              cubit.onFetchAllThread(accountService.familyId);
            });
          },
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _showAvatar(cubit, message),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _showNameMember(message),
                      const SizedBox(height: 4),
                      if (message.latestMessage != null &&
                          message.latestMessage!.isNotEmpty) ...[
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                "${isMe ? "You" : userMap?["full_name"]}: ${message.latestMessageText}",
                                style: AppStyle.regular14(
                                    color: appTheme.blackText),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Text(
                              "${Utils.formatDateTime(message.latestMessageTime.toString())}",
                              style:
                                  AppStyle.regular12(color: appTheme.grayColor),
                            ),
                          ],
                        )
                      ] else ...[
                        Text(
                          "",
                          style: AppStyle.regular14(color: appTheme.blackText),
                        ),
                      ],
                      SizedBox(
                        height: 10,
                      ),
                      Divider(
                        height: 3,
                        color: appTheme.grayA8Color,
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _showAvatar(ThreadCubit cubit, ThreadFamily message) {
    var members = message.members?.length ?? 0;
    return members > 1
        ? Container(
        width: 50,
        height: 65,
        child: Stack(
          children: [
            for (var i = 0; i < members; i++) ...[
              if (i == 0)...[
                Positioned(
                  left: 0,
                  child: CircleAvatar(
                      radius: 20,
                      child: Text(message.members![i].fullName![0].toUpperCase())),
                ),
              ] else if (i == 1)...[
                Positioned(
                  top: 25,
                  child: CircleAvatar(
                      radius: 20,
                      child: Text(message.members![i].fullName![0].toUpperCase())),
                ),
              ]
            ],

            if (members > 2)...[
              Positioned(
                left: 16,
                bottom: 10,
                child: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.purple,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                  child: Text(
                    "+${members - 2}",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],

          ],
        ))
        : CircleAvatar(
            radius: 20, child: Text(message.members![0].fullName![0]));
  }

  Widget _showNameMember(ThreadFamily message) {
    return Text(
      message.name.toString(),
      style: AppStyle.medium14(color: appTheme.blackText),
      overflow: TextOverflow.ellipsis,
    );
  }


  }
