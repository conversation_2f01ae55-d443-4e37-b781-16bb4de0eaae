import 'package:family_app/data/model/thread_family.dart';

import '../../../base/widget/cubit/base_state.dart';
import '../../../data/model/account.dart';
import 'member_model.dart';

enum ThreadStatus { none, loading, success, error }

class ThreadState extends BaseState {

  final ThreadStatus status;
  final List<ThreadFamily> listMessageFamily;
  final List<MemberModel> listMember;
  final String? errorMessage;
  final List<MemberModel> selectedMembers;
  ThreadFamily? threadDetail;

  ThreadState({
    this.status = ThreadStatus.none,
    this.listMessageFamily = const [],
    this.listMember = const [],
    this.selectedMembers = const [],
    this.threadDetail,
    this.errorMessage,
  });

  ThreadState copyWith({
    ThreadStatus? status,
    List<ThreadFamily>? listMessageFamily,
    List<MemberModel>? listMember,
    List<MemberModel>? selectedMembers,
    ThreadFamily? threadDetail,
    String? errorMessage,
  }) {
    return ThreadState(
      status: status ?? this.status,
      listMessageFamily: listMessageFamily ?? this.listMessageFamily,
      listMember: listMember ?? this.listMember,
      selectedMembers: selectedMembers ?? this.selectedMembers,
      threadDetail: threadDetail ?? this.threadDetail,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, listMessageFamily, listMember, selectedMembers, threadDetail, errorMessage];


}
