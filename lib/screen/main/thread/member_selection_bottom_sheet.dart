import 'package:family_app/screen/main/thread/thread_cubit.dart';
import 'package:family_app/screen/main/thread/thread_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../config/theme/style/style_theme.dart';
import '../../../main.dart';

class MemberSelectionBottomSheet extends StatefulWidget {
  final ThreadCubit cubit;

  const MemberSelectionBottomSheet({Key? key, required this.cubit}) : super(key: key);

  @override
  _MemberSelectionBottomSheetState createState() => _MemberSelectionBottomSheetState();
}

class _MemberSelectionBottomSheetState extends State<MemberSelectionBottomSheet> {
  final TextEditingController _topicController = TextEditingController();
  final FocusNode _topicFocusNode = FocusNode();
  bool _isTopicEmpty = false;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(milliseconds: 300), () {
      _topicFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _topicController.dispose();
    _topicFocusNode.dispose();
    super.dispose();
  }

  void _validateAndCreateThread() {
    setState(() {
      _isTopicEmpty = _topicController.text.isEmpty;
    });

    if (!_isTopicEmpty && widget.cubit.state.selectedMembers.isNotEmpty) {
      widget.cubit.createThread(_topicController.text.trim());
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: widget.cubit,
      child: BlocBuilder<ThreadCubit, ThreadState>(
        builder: (context, state) {
          bool isCreateEnabled = state.selectedMembers.isNotEmpty && _topicController.text.isNotEmpty;

          return Padding(
            padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
            child: SingleChildScrollView(
              child: Wrap(
                children: [
                  Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.0),
                        topRight: Radius.circular(16.0),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Header Row
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextButton(
                                onPressed: () {
                                  widget.cubit.cancelCreateThread();
                                  Navigator.pop(context);
                                },
                                child: Text(
                                  "Cancel",
                                  style: AppStyle.regular16(color: appTheme.purpleColor),
                                ),
                              ),
                              Text(
                                "New message",
                                style: AppStyle.medium18(color: appTheme.blackText),
                              ),
                              TextButton(
                                onPressed: isCreateEnabled ? _validateAndCreateThread : null,
                                child: Text(
                                  "Create",
                                  style: AppStyle.regular16(
                                    color: isCreateEnabled ? appTheme.purpleColor : Colors.grey,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 16),

                          // TextField for Topic (Tự động focus)
                          TextField(
                            controller: _topicController,
                            focusNode: _topicFocusNode,
                            onChanged: (value) {
                              setState(() {
                                _isTopicEmpty = value.isEmpty;
                              });
                            },
                            decoration: InputDecoration(
                              hintText: "Enter a message topic",
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                                borderSide: BorderSide(
                                  color: _isTopicEmpty ? Colors.red : Colors.grey,
                                ),
                              ),
                              errorText: _isTopicEmpty ? "Please enter a topic" : null,
                            ),
                          ),
                          SizedBox(height: 16),

                          // Member List
                          SizedBox(
                            height: 250,
                            child: ListView.builder(
                              shrinkWrap: true,
                              itemCount: state.listMember.length,
                              itemBuilder: (context, index) {
                                final member = state.listMember[index];
                                return Column(
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        widget.cubit.toggleSelection(member);
                                        setState(() {});
                                      },
                                      child: Row(
                                        children: [
                                          Checkbox(
                                            value: state.selectedMembers.contains(member),
                                            onChanged: (bool? value) {
                                              widget.cubit.toggleSelection(member);
                                              setState(() {});
                                            },
                                          ),
                                          Expanded(
                                            child: ListTile(
                                              title: Text(member.account.fullName.toString()),
                                              subtitle: Text(member.account.role.toString()),
                                              leading: CircleAvatar(
                                                child: Text(member.account
                                                        .fullName!.isNotEmpty
                                                    ? member
                                                        .account.fullName![0]
                                                    : ''),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Divider(height: 1, color: appTheme.grayColor.withOpacity(0.1)),
                                  ],
                                );
                              },
                            ),
                          ),

                          SizedBox(height: 16),

                          // Selected Members Row
                          Row(
                            children: [
                              Expanded(
                                child: SizedBox(
                                  height: 50,
                                  child: ListView(
                                    scrollDirection: Axis.horizontal,
                                    shrinkWrap: true,
                                    children: state.selectedMembers
                                        .map(
                                          (member) => Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                                        child: Stack(
                                          children: [
                                            CircleAvatar(
                                                  radius: 24,
                                                  child: Text(member.account
                                                          .fullName!.isNotEmpty
                                                      ? member
                                                          .account.fullName![0]
                                                      : ''),
                                                ),
                                            Positioned(
                                              right: 0,
                                              top: 0,
                                              child: GestureDetector(
                                                onTap: () {
                                                  widget.cubit.toggleSelection(member);
                                                  setState(() {});
                                                },
                                                child: CircleAvatar(
                                                  radius: 10,
                                                  backgroundColor: Colors.black,
                                                  child: Icon(Icons.close, size: 12, color: Colors.white),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    )
                                        .toList(),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
