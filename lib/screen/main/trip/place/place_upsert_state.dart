import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:family_app/data/model/trip_model.dart';

/// State class for the place upsert screen
class PlaceUpsertState extends BaseState {
  final bool isSaving;
  final bool saveSuccess;
  final String? error;
  final Itinerary? place;
  final String description;
  final String venue;
  final String city;
  final String time;
  final String? activityImage;
  final LatLng? selectedLocation;
  final String? address;

  PlaceUpsertState({
    bool isLoading = false,
    this.isSaving = false,
    this.saveSuccess = false,
    this.error,
    this.place,
    this.description = '',
    this.venue = '',
    this.city = '',
    this.time = '',
    this.activityImage,
    this.selectedLocation,
    this.address,
  }) : super(isLoading: isLoading);

  PlaceUpsertState copyWith({
    bool? isLoading,
    bool? isSaving,
    bool? saveSuccess,
    String? error,
    Itinerary? place,
    String? description,
    String? venue,
    String? city,
    String? time,
    String? activityImage,
    LatLng? selectedLocation,
    String? address,
  }) {
    return PlaceUpsertState(
      isLoading: isLoading ?? this.isLoading,
      isSaving: isSaving ?? this.isSaving,
      saveSuccess: saveSuccess ?? this.saveSuccess,
      error: error,
      place: place ?? this.place,
      description: description ?? this.description,
      venue: venue ?? this.venue,
      city: city ?? this.city,
      time: time ?? this.time,
      activityImage: activityImage ?? this.activityImage,
      selectedLocation: selectedLocation ?? this.selectedLocation,
      address: address ?? this.address,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        isSaving,
        saveSuccess,
        error,
        place,
        description,
        venue,
        city,
        time,
        activityImage,
        selectedLocation,
        address,
      ];

  @override
  String toString() => 'PlaceUpsertState('
      'isLoading: $isLoading, '
      'isSaving: $isSaving, '
      'saveSuccess: $saveSuccess, '
      'error: $error, '
      'description: $description, '
      'venue: $venue, '
      'city: $city, '
      'time: $time, '
      'activityImage: $activityImage, '
      'selectedLocation: $selectedLocation, '
      'address: $address, '
      'place: $place)';
}
