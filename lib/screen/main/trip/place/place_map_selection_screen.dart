import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/trip/place/place_map_selection_cubit.dart';
import 'package:family_app/screen/main/trip/place/place_map_selection_state.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:family_app/data/model/trip_model.dart';

@RoutePage()
class PlaceMapSelectionPage extends BaseBlocProvider<PlaceMapSelectionState, PlaceMapSelectionCubit> {
  const PlaceMapSelectionPage({super.key, required this.parameter});

  final PlaceUpsertParameter parameter;

  @override
  Widget buildPage() => PlaceMapSelectionView(parameter: parameter);

  @override
  PlaceMapSelectionCubit createCubit() => PlaceMapSelectionCubit(parameter: parameter);
}

class PlaceMapSelectionView extends StatefulWidget {
  const PlaceMapSelectionView({super.key, required this.parameter});

  final PlaceUpsertParameter parameter;

  @override
  State<PlaceMapSelectionView> createState() => _PlaceMapSelectionViewState();
}

class _PoiCategory {
  final String label;
  final String type;
  final IconData icon;
  const _PoiCategory(this.label, this.type, this.icon);
}

class _PlaceMapSelectionViewState extends BaseBlocPageState<PlaceMapSelectionView, PlaceMapSelectionState, PlaceMapSelectionCubit> {
  final TextEditingController _searchController = TextEditingController();
  GoogleMapController? _mapController;
  String? _mapStyle;

  bool get viewMode => widget.parameter.viewMode;
  List<Activity> get allActivities => widget.parameter.allActivities;

  List<Activity> get _displayActivities {
    // If only one activity, show only that place (accessed from itinerary)
    // If more than one, show all (accessed from appbar)
    if (allActivities.length == 1) return allActivities;
    // Defensive: filter out activities without coordinates
    return allActivities.where((a) => a.latitude != null && a.longitude != null).toList();
  }

  final List<_PoiCategory> _categories = [
    _PoiCategory('Restaurant', 'restaurant', Icons.restaurant),
    _PoiCategory('Hotel', 'hotel', Icons.hotel),
    _PoiCategory('Coffee', 'cafe', Icons.local_cafe),
    _PoiCategory('Shopping', 'shopping_mall', Icons.shopping_bag),
    _PoiCategory('ATM', 'atm', Icons.atm),
    _PoiCategory('Gas', 'gas_station', Icons.local_gas_station),
    _PoiCategory('Pharmacy', 'pharmacy', Icons.local_pharmacy),
    _PoiCategory('Attraction', 'tourist_attraction', Icons.attractions),
  ];
  int _selectedCategoryIndex = -1; // No category selected by default
  bool _categoryMode = false; // Track if a category is selected and input is filled

  final Map<String, BitmapDescriptor> _customPoiIcons = {};
  bool _poiIconsLoaded = false;

  LatLng? _initialCameraTarget;
  bool _initialCameraReady = false;

  void _onPoiMarkerTapped(PoiMarker poi) async {
    // Update the cubit state to reflect the selected POI as the main selected location
    final cubit = context.read<PlaceMapSelectionCubit>();
    await cubit.onSelectSuggestion(
      PlaceSuggestion(
        placeId: poi.placeId,
        name: poi.name,
        address: poi.address ?? '',
        location: poi.location,
        photoReference: poi.photoReference,
      ),
    );
  }

  void _onMarkerTapped(Activity activity, PlaceMapSelectionCubit cubit) {
    cubit.onSelectActivity(activity);
  }

  void _onCategorySelected(int index, PlaceMapSelectionCubit cubit) {
    setState(() {
      _selectedCategoryIndex = index;
      _categoryMode = true;
      _searchController.text = _categories[index].label;
      FocusScope.of(context).unfocus();
    });
    final state = context.read<PlaceMapSelectionCubit>().state;
    final center = state.selectedLocation ?? const LatLng(10.762622, 106.660172);
    cubit.fetchNearbyPois(
      location: center,
      types: [_categories[index].type],
    );
  }

  @override
  void initState() {
    super.initState();
    _loadMapStyle();
    _loadCustomPoiIcons();
    _resolveInitialCameraTarget();
    if (viewMode && _displayActivities.length == 1) {
      // _selectedActivity = _displayActivities.first;
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (viewMode && allActivities.isNotEmpty) {
        _fitAllMarkers();
      }
    });
  }

  Future<void> _resolveInitialCameraTarget() async {
    final state = context.read<PlaceMapSelectionCubit>().state;
    if (state.selectedLocation != null) {
      setState(() {
        _initialCameraTarget = state.selectedLocation!;
        _initialCameraReady = true;
      });
      return;
    }
    final InitialLocation? initialLocation = widget.parameter.initialLocation;
    AppLogger.d('Resolving initial camera target latLng: ${initialLocation?.latLng}');
    AppLogger.d('Resolving initial camera target name: ${initialLocation?.name}');
    if (initialLocation != null) {
      if (initialLocation.latLng != null) {
        setState(() {
          _initialCameraTarget = initialLocation.latLng;
          _initialCameraReady = true;
        });
        return;
      } else if (initialLocation.name != null && initialLocation.name!.trim().isNotEmpty) {
        final latLng = await _geocodeLocationName(initialLocation.name!.trim());
        if (latLng != null) {
          setState(() {
            _initialCameraTarget = latLng;
            _initialCameraReady = true;
          });
          return;
        }
      }
    }
    // Defensive fallback: use default location if all else fails
    setState(() {
      _initialCameraTarget = const LatLng(10.762622, 106.660172);
      _initialCameraReady = true;
    });
  }

  Future<void> _loadMapStyle() async {
    _mapStyle = await rootBundle.loadString('assets/map_style.json');
  }

  Future<void> _loadCustomPoiIcons() async {
    final iconMap = <String, String>{
      'restaurant': 'assets/markers/marker_restaurant.png',
      'hotel': 'assets/markers/marker_hotel.png',
      'cafe': 'assets/markers/marker_cafe.png',
      'shopping_mall': 'assets/markers/marker_shopping.png',
      'atm': 'assets/markers/marker_atm.png',
      'gas_station': 'assets/markers/marker_gas.png',
      'pharmacy': 'assets/markers/marker_pharmacy.png',
      'tourist_attraction': 'assets/markers/marker_attraction.png',
    };
    for (final entry in iconMap.entries) {
      try {
        final bitmap = await BitmapDescriptor.fromAssetImage(
          const ImageConfiguration(size: Size(64, 64)),
          entry.value,
        );
        _customPoiIcons[entry.key] = bitmap;
      } catch (_) {
        // If asset not found, skip
      }
    }
    setState(() {
      _poiIconsLoaded = true;
    });
  }

  void _fitAllMarkers() async {
    if (_mapController == null) return;
    final validActivities = _displayActivities.where((a) => a.latitude != null && a.longitude != null).toList();
    if (validActivities.isEmpty) return;
    if (validActivities.length == 1) {
      final LatLng pos = LatLng(validActivities[0].latitude!, validActivities[0].longitude!);
      await _mapController!.animateCamera(CameraUpdate.newLatLngZoom(pos, 15));
      return;
    }
    LatLngBounds bounds = _createBounds(validActivities);
    await _mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 60));
  }

  LatLngBounds _createBounds(List<Activity> activities) {
    // Defensive: filter out activities without lat/lng
    final filtered = activities.where((a) => a.latitude != null && a.longitude != null).toList();
    if (filtered.isEmpty) {
      // Fallback to a default location if no valid activities
      return LatLngBounds(
        southwest: const LatLng(10.762622, 106.660172),
        northeast: const LatLng(10.762622, 106.660172),
      );
    }
    final lats = filtered.map((a) => a.latitude!).toList();
    final lngs = filtered.map((a) => a.longitude!).toList();
    final southwest = LatLng(lats.reduce((a, b) => a < b ? a : b), lngs.reduce((a, b) => a < b ? a : b));
    final northeast = LatLng(lats.reduce((a, b) => a > b ? a : b), lngs.reduce((a, b) => a > b ? a : b));
    return LatLngBounds(southwest: southwest, northeast: northeast);
  }

  BitmapDescriptor _getPoiMarkerIcon(String type) {
    if (_poiIconsLoaded && _customPoiIcons.containsKey(type)) {
      return _customPoiIcons[type]!;
    }
    return BitmapDescriptor.defaultMarker;
  }

  Future<void> _animateToLocation(LatLng location) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(location, 16),
      );
    }
  }

  /// Geocode a location name (city, country, etc) to LatLng
  Future<LatLng?> _geocodeLocationName(String locationName) async {
    try {
      final locations = await locationFromAddress(locationName);
      if (locations.isNotEmpty) {
        final loc = locations.first;
        return LatLng(loc.latitude, loc.longitude);
      }
    } catch (e) {
      print('Geocoding error for $locationName: $e');
    }
    return null;
  }

  @override
  Widget buildAppBar(BuildContext context, PlaceMapSelectionCubit cubit, PlaceMapSelectionState state) {
    return CustomAppBar2(
      title: viewMode ? 'View All Places' : 'Select Location',
      showBack: true,
      actions: viewMode ? [] : [
        if (state.selectedLocation != null)
          GestureDetector(
            onTap: () =>  cubit.onConfirm(context),
            behavior: HitTestBehavior.opaque,
            child: CircleItem(
              backgroundColor: appTheme.backgroundV2,
              padding: padding(all: 7),
              child: state.isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Assets.icons.icActionCheck.svg(),
            ),
          )
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, PlaceMapSelectionCubit cubit, PlaceMapSelectionState state) {
    if (!_initialCameraReady || _initialCameraTarget == null) {
      return const Center(child: CircularProgressIndicator());
    }
    Set<Marker> markers = {};
    if (viewMode) {
      final validActivities = _displayActivities.where((a) => a.latitude != null && a.longitude != null).toList();
      if (validActivities.isNotEmpty) {
        markers.addAll(validActivities
            .map((a) => Marker(
                  markerId: MarkerId(a.description),
                  position: LatLng(a.latitude!, a.longitude!),
                  onTap: () => _onMarkerTapped(a, cubit),
                )));
      } else if (_initialCameraTarget != null) {
        // No activities with lat/lng, but we have an initial camera target (from initialLocation)
        markers.add(Marker(
          markerId: const MarkerId('initial_location'),
          position: _initialCameraTarget!,
        ));
      }
    } else {
      if (state.selectedLocation != null) {
        markers.add(Marker(
          markerId: const MarkerId('selected'),
          position: state.selectedLocation!,
        ));
      }
      markers.addAll(state.poiMarkers.map((poi) => Marker(
            markerId: MarkerId('poi_${poi.placeId}'),
            position: poi.location,
            icon: _getPoiMarkerIcon(poi.type ?? ''),
            onTap: () => _onPoiMarkerTapped(poi),
          )));
      // If no POIs loaded yet, auto-fetch for all default categories around initial camera
      // if (state.poiMarkers.isEmpty && _initialCameraTarget != null && _poiIconsLoaded) {
      //   // Only fetch once per session
      //   WidgetsBinding.instance.addPostFrameCallback((_) {
      //     final cubit = context.read<PlaceMapSelectionCubit>();
      //     cubit.fetchNearbyPois(location: _initialCameraTarget!);
      //   });
      // }
    }
    return Stack(
      children: [
        GoogleMap(
          initialCameraPosition: CameraPosition(
            target: _initialCameraTarget!,
            zoom: 15,
          ),
          onMapCreated: (controller) {
            _mapController = controller;
            if (_mapStyle != null) {
              try {
                _mapController!.setMapStyle(_mapStyle!);
              } catch (e) {}
            }
            if (viewMode && _displayActivities.isNotEmpty) {
              _fitAllMarkers();
            }
          },
          markers: markers,
          myLocationEnabled: !viewMode,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
          compassEnabled: true,
          tiltGesturesEnabled: true,
          rotateGesturesEnabled: true,
          zoomGesturesEnabled: true,
          scrollGesturesEnabled: true,
          padding: const EdgeInsets.only(top: 120, bottom: 40),
          onTap: viewMode ? null : (LatLng latLng) async {
            await cubit.onMapTap(latLng);
            _animateToLocation(latLng);
          },
        ),
        if (!viewMode)
          Positioned(
            bottom: 24,
            right: 16,
            child: FloatingActionButton(
              heroTag: 'myLocation',
              backgroundColor: Colors.white,
              mini: true,
              elevation: 4,
              onPressed: () async {
                // Center map on user's current location
                // You may want to add permission checks here
                Position? position;
                try {
                  position = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
                } catch (_) {}
                if (position != null) {
                  final latLng = LatLng(position.latitude, position.longitude);
                  _animateToLocation(latLng);
                }
              },
              child: const Icon(Icons.my_location, color: Colors.black87),
            ),
          ),
          if (state.selectedPhotoUrl != null || state.selectedDescription != null || state.selectedCity != null || state.selectedTime != null || state.selectedAddress != null)
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Material(
              color: Colors.transparent,
              child: LocationQuickViewCard(
                photoUrl: state.selectedPhotoUrl,
                description: state.selectedDescription,
                city: state.selectedCity,
                time: state.selectedTime,
                address: state.selectedAddress,
              ),
            ),
          ),
        if (!viewMode)
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Material(
                  elevation: 4,
                  borderRadius: BorderRadius.circular(30.0),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search for a place...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30.0),
                        borderSide: BorderSide.none, // Remove default border
                      ),
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _searchController.clear();
                                  _selectedCategoryIndex = -1;
                                  _categoryMode = false;
                                });
                                context.read<PlaceMapSelectionCubit>().onSearchLocation('');
                              },
                            )
                          : null,
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                    ),
                    onChanged: (value) {
                      setState(() {
                        if (value.isEmpty) {
                          _selectedCategoryIndex = -1;
                          _categoryMode = false;
                        } else if (_selectedCategoryIndex != -1 && value == _categories[_selectedCategoryIndex].label) {
                          _categoryMode = true;
                        } else {
                          _categoryMode = false;
                        }
                      });
                      context.read<PlaceMapSelectionCubit>().onSearchLocation(value);
                    },
                    textInputAction: TextInputAction.search,
                  ),
                ),
                if (!_categoryMode && _searchController.text.isEmpty) ...[
                  const SizedBox(height: 10),
                  SizedBox(
                    height: 40,
                    child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      itemCount: _categories.length,
                      separatorBuilder: (_, __) => const SizedBox(width: 8),
                      itemBuilder: (context, index) {
                        final cat = _categories[index];
                        final selected = index == _selectedCategoryIndex;
                        return ChoiceChip(
                          label: Row(
                            children: [
                              Icon(cat.icon, size: 18, color: selected ? Colors.white : Colors.black54),
                              const SizedBox(width: 4),
                              Text(cat.label),
                            ],
                          ),
                          selected: selected,
                          onSelected: (_) => _onCategorySelected(index, cubit),
                          selectedColor: Theme.of(context).primaryColor,
                          backgroundColor: Colors.white,
                          labelStyle: TextStyle(color: selected ? Colors.white : Colors.black87),
                          elevation: selected ? 4 : 0,
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                        );
                      },
                    ),
                  ),
                ],
              ],
            ),
          ),
        if (!viewMode && state.suggestions.isNotEmpty)
          Positioned(
            top: 72,
            left: 16,
            right: 16,
            child: Card(
              elevation: 6,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListView.separated(
                shrinkWrap: true,
                padding: const EdgeInsets.all(8),
                itemCount: state.suggestions.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final suggestion = state.suggestions[index];
                  return ListTile(
                    leading: suggestion.photoReference != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              'https://maps.googleapis.com/maps/api/place/photo?maxwidth=100&photoreference=${suggestion.photoReference}&key=${cubit.apiKey}',
                              width: 48,
                              height: 48,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  const Icon(Icons.photo),
                            ),
                          )
                        : const Icon(Icons.place, color: Colors.blueAccent),
                    title: Text(
                      suggestion.name,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text(
                      suggestion.address,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    onTap: () async {
                      await cubit.onSelectSuggestion(suggestion);
                      setState(() {
                        _searchController.text = suggestion.name;
                        FocusScope.of(context).unfocus();
                      });
                      // Clear suggestions after selection
                      context.read<PlaceMapSelectionCubit>().onSearchLocation('');
                      _animateToLocation(suggestion.location);
                    },
                  );
                },
              ),
            ),
          ),
        if (state.isLoading)
          Container(
            color: Colors.black.withOpacity(0.3),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
      ],
    );
  }
}

class LocationQuickViewCard extends StatelessWidget {
  final String? photoUrl;
  final String? description;
  final String? city;
  final String? time;
  final String? address;
  const LocationQuickViewCard({
    super.key,
    this.photoUrl,
    this.description,
    this.city,
    this.time,
    this.address,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (photoUrl != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Image.network(
                  photoUrl!,
                  height: 180,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    height: 180,
                    width: double.infinity,
                    color: Colors.grey[200],
                    child: const Icon(Icons.broken_image, size: 48, color: Colors.grey),
                  ),
                ),
              ),
            if (photoUrl != null) const SizedBox(height: 14),
            Text(
              address ?? 'Selected Location',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            if (city != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.location_city,
                    size: 16,
                    color: appTheme.grayV2,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    city!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: appTheme.grayV2,),
                  ),
                ],
              ),
            ],
            if (time != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: appTheme.grayV2,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    time!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: appTheme.grayV2,
                        ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 12),
            Text(
              description ?? '',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
