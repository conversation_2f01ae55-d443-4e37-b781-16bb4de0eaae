import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_state.dart';
import 'package:family_app/screen/main/trip/trip_detail_cubit.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/material.dart';

/// Cubit for managing the place upsert screen
class PlaceUpsertCubit extends BaseCubit<PlaceUpsertState> {
  static const _tag = "PlaceUpsertCubit";

  /// The parameters passed to the screen
  final PlaceUpsertParameter parameter;
  final IActivityRepository activityRepository;
  StackRouter? router;

  PlaceUpsertCubit({
    required this.parameter,
    required this.activityRepository,
    this.router,
  }) : super(PlaceUpsertState(
          place: parameter.place,
          description: parameter.place?.activities?.firstOrNull?.description ?? '',
          venue: parameter.place?.activities?.firstOrNull?.venue ?? '',
          city: parameter.place?.activities?.firstOrNull?.city ?? '',
          time: _convertToTimeRange(parameter.place?.activities?.firstOrNull?.time),
          activityImage: parameter.place?.activities?.firstOrNull?.activityImage,
          selectedLocation: parameter.place?.activities?.firstOrNull?.latitude != null &&
                  parameter.place?.activities?.firstOrNull?.longitude != null
              ? LatLng(
                  parameter.place!.activities!.first.latitude!,
                  parameter.place!.activities!.first.longitude!,
                )
              : null,
        ));

  /// Convert old AM/PM format to time range format
  static String _convertToTimeRange(String? time) {
    if (time == null) return '10:00 - 12:00';
    if (time == 'AM') return '10:00 - 12:00';
    if (time == 'PM') return '16:00 - 18:00';
    return time; // If it's already in time range format, return as is
  }

  @override
  void onInit() {
    _init();
    super.onInit();
  }

  /// Initialize the cubit with existing place data if available
  Future<void> _init() async {
    try {
      emit(state.copyWith(isLoading: true));
      if (parameter.place?.activities?.isNotEmpty == true) {
        final activity = parameter.place!.activities!.first;
        emit(state.copyWith(
          description: activity.description,
          venue: activity.venue,
          city: activity.city,
          time: _convertToTimeRange(activity.time),
          activityImage: activity.activityImage,
          selectedLocation: activity.latitude != null && activity.longitude != null
              ? LatLng(activity.latitude!, activity.longitude!)
              : null,
          address: activity.venue,
          isLoading: false,
        ));
      }
      emit(state.copyWith(isLoading: false));
    } catch (e) {
      logd("_init $e", tag: _tag);
      emit(state.copyWith(isLoading: false));
    }
  }

  @override
  Future<void> close() {
    return super.close();
  }

  void onDescriptionChanged(String value) {
    emit(state.copyWith(description: value));
  }

  void onVenueChanged(String value) {
    emit(state.copyWith(venue: value));
  }

  void onCityChanged(String value) {
    emit(state.copyWith(city: value));
  }

  void onTimeChanged(String value) {
    emit(state.copyWith(time: value));
  }

  void onActivityImageChanged(String? value) {
    emit(state.copyWith(activityImage: value));
  }

  /// Handle form submission
  Future<void> onSubmit(BuildContext context) async {
    if (state.description.isEmpty || state.venue.isEmpty || state.city.isEmpty) {
      return;
    }

    final updatedActivity = Activity(
      time: state.time,
      description: state.description,
      venue: state.venue,
      city: state.city,
      latitude: state.selectedLocation?.latitude,
      longitude: state.selectedLocation?.longitude,
      activityImage: state.activityImage,
      category: 'Place', //TODO - hardcoded for now, need to change later
    );

    final updatedItinerary = Itinerary(
      activities: [updatedActivity],
    );

    final updatedParameter = PlaceUpsertParameter(
      tripId: parameter.tripId,
      dayIndex: parameter.dayIndex,
      activityIndex: parameter.activityIndex,
      place: updatedItinerary,
    );

    if (context.mounted) {
      Navigator.of(context).pop(updatedParameter);
    }
  }

  Future<void> savePlace(Activity place) async {
    if (state.isSaving) return;

    emit(state.copyWith(isSaving: true));
    try {
      final trip = await activityRepository.getActivityById(parameter.tripId);

      int dayIdx = parameter.dayIndex;
      if (trip.itinerary == null || dayIdx >= trip.itinerary!.length) {
        emit(state.copyWith(isSaving: false));
        return;
      }

      final dayItinerary = trip.itinerary![dayIdx];
      final activities = dayItinerary.activities ??= [];

      final updatedActivity = Activity(
        time: state.time,
        description: place.description,
        venue: place.venue,
        city: place.city,
        latitude: place.latitude,
        longitude: place.longitude,
        activityImage: state.activityImage,
        category: 'Place', //TODO - hardcoded for now, need to change later
      );

      if (parameter.activityIndex != null && parameter.activityIndex! < activities.length) {
        activities[parameter.activityIndex!] = updatedActivity;
      } else {
        activities.add(updatedActivity);
      }

      await activityRepository.updateActivity(trip.uuid, trip.toCreateActivityParameter());
      emit(state.copyWith(
        isSaving: false,
        saveSuccess: true,
      ));

      // Refresh TripDetailCubit if registered (after save success)
      try {
        final tripDetailCubit = locator.isRegistered<TripDetailCubit>() ? locator.get<TripDetailCubit>() : null;
        await tripDetailCubit?.fetchImagesAndLocations();
      } catch (e) {
        AppLogger.e('Error refreshing TripDetailCubit: $e');
      }
      if (router != null) {
        router!.maybePop();
      } else if (locator.isRegistered<StackRouter>()) {
        locator<StackRouter>().maybePop();
      }
    } catch (e) {
      AppLogger.e('Error saving place: $e');
      emit(state.copyWith(isSaving: false));
      if (router != null) {
        router!.maybePop(false);
      } else if (locator.isRegistered<StackRouter>()) {
        locator<StackRouter>().maybePop(false);
      }
    }
  }

  void updateFromPlaceDetails({
    required LatLng location,
    required String venue,
    required String city,
    String? activityImage,
    String? time,
  }) {
    emit(state.copyWith(
      selectedLocation: location,
      venue: venue,
      city: city,
      activityImage: activityImage,
      time: time ?? state.time,
    ));
  }
}
