import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/screen/main/trip/place/place_map_selection_state.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geocoding/geocoding.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

/// Cubit for managing the place map selection screen
class PlaceMapSelectionCubit extends BaseCubit<PlaceMapSelectionState> {
  static const _tag = "PlaceMapSelectionCubit";

  final PlaceUpsertParameter parameter;

  final String apiKey = AppConfig.GOOGLE_MAPS_API_KEY;

  PlaceMapSelectionCubit({
    required this.parameter,
  }) : super(PlaceMapSelectionState());

  @override
  void onInit() {
    _init();
    super.onInit();
  }

  /// Initialize the cubit with existing place data if available
  Future<void> _init() async {
    try {
      emit(state.copyWith(isLoading: true));
      if (parameter.place?.activities?.isNotEmpty == true) {
        final activity = parameter.place!.activities!.first;
        if (activity.latitude != null && activity.longitude != null) {
          final location = LatLng(
            activity.latitude!,
            activity.longitude!,
          );

          // Convert old AM/PM time format to 24-hour format if needed
          String? timeRange;
          if (activity.time == 'AM') {
            timeRange = '10:00 - 12:00';
          } else if (activity.time == 'PM') {
            timeRange = '16:00 - 18:00';
          } else {
            timeRange = activity.time;
          }

          emit(state.copyWith(
            selectedLocation: location,
            selectedTime: timeRange,
          ));
        }

        emit(state.copyWith(
          selectedDescription: activity.description,
          selectedAddress: activity.venue,
          selectedCity: activity.city,
          selectedPhotoUrl: activity.activityImage,
          isLoading: false,
        ));
      }
      emit(state.copyWith(isLoading: false));
    } catch (e) {
      logd("_init $e", tag: _tag);
      emit(state.copyWith(isLoading: false));
    }
  }

  /// Search for places using the Google Places API
  Future<void> onSearchLocation(String query) async {
    try {
      emit(state.copyWith(isLoading: true));

      // If query is empty, clear suggestions and return
      if (query.isEmpty) {
        emit(state.copyWith(
          suggestions: [],
          isLoading: false,
        ));
        return;
      }

      // Use initialLocation as location bias if available (migrated to InitialLocation class)
      String? locationBias;
      final initialLocation = parameter.initialLocation;
      LatLng? biasLatLng;
      if (initialLocation != null) {
        if (initialLocation.latLng != null) {
          biasLatLng = initialLocation.latLng;
        } else if (initialLocation.name != null && initialLocation.name!.trim().isNotEmpty) {
          // Try to geocode the name to get lat/lng
          try {
            final locations = await locationFromAddress(initialLocation.name!);
            if (locations.isNotEmpty) {
              biasLatLng = LatLng(locations.first.latitude, locations.first.longitude);
            }
          } catch (_) {}
        }
      }
      // Defensive fallback: use default location if all else fails
      biasLatLng ??= const LatLng(10.762622, 106.660172);
      locationBias = '&location=${biasLatLng.latitude},${biasLatLng.longitude}&radius=50000';

      final url = 'https://maps.googleapis.com/maps/api/place/textsearch/json?query=$query&key=$apiKey'
        + locationBias;

      final response = await http.get(
        Uri.parse(url),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK' && data['results'].isNotEmpty) {
          final results = data['results'];
          final suggestions = <PlaceSuggestion>[];

          for (var result in results.take(5)) {
            final location = result['geometry']['location'];
            final latLng = LatLng(location['lat'], location['lng']);

            String? photoReference;
            if (result['photos'] != null && result['photos'].isNotEmpty) {
              photoReference = result['photos'][0]['photo_reference'];
            }

            suggestions.add(PlaceSuggestion(
              placeId: result['place_id'],
              name: result['name'],
              address: result['formatted_address'],
              location: latLng,
              photoReference: photoReference,
            ));
          }

          emit(state.copyWith(
            suggestions: suggestions,
            isLoading: false,
          ));
        }
      }
    } catch (e) {
      logd("onSearchLocation $e", tag: _tag);
      emit(state.copyWith(isLoading: false));
    }
  }

  /// Handle selection of a place suggestion
  Future<void> onSelectSuggestion(PlaceSuggestion suggestion) async {
    try {
      emit(state.copyWith(isLoading: true));

      String? photoUrl;
      if (suggestion.photoReference != null) {
        try {
          photoUrl = 'https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&maxheight=400&photoreference=${suggestion.photoReference}&key=$apiKey';
          final response = await http.get(Uri.parse(photoUrl));
          if (response.statusCode != 200) {
            photoUrl = null;
          }
        } catch (e) {
          logd("Error verifying photo URL: $e", tag: _tag);
          photoUrl = null;
        }
      }

      String? city;
      String? openingHours;
      String? timeRange;
      String? shortAddress = suggestion.address;
      try {
        // Get place details including opening hours and address_components
        final detailsResponse = await http.get(
          Uri.parse(
            'https://maps.googleapis.com/maps/api/place/details/json?place_id=${suggestion.placeId}&fields=opening_hours,address_components&key=$apiKey',
          ),
        );

        if (detailsResponse.statusCode == 200) {
          final detailsData = json.decode(detailsResponse.body);
          if (detailsData['status'] == 'OK' && detailsData['result'] != null) {
            final result = detailsData['result'];
            // --- Opening hours logic (unchanged) ---
            if (result['opening_hours'] != null) {
              final hours = result['opening_hours'];
              if (hours['open_now'] != null) {
                openingHours = hours['open_now'] ? 'Open Now' : 'Closed';
              }
              if (hours['periods'] != null && hours['periods'].isNotEmpty) {
                final now = DateTime.now();
                final today = now.weekday % 7;
                final todayPeriod = hours['periods'].firstWhere(
                  (period) => period['open']['day'] == today,
                  orElse: () => null,
                );
                if (todayPeriod != null) {
                  final openTime = todayPeriod['open']['time'];
                  final closeTime = todayPeriod['close']['time'];
                  final formattedOpenTime = '${openTime.substring(0, 2)}:${openTime.substring(2)}';
                  final formattedCloseTime = '${closeTime.substring(0, 2)}:${closeTime.substring(2)}';
                  timeRange = '$formattedOpenTime - $formattedCloseTime';
                } else {
                  final firstPeriod = hours['periods'][0];
                  final openTime = firstPeriod['open']['time'];
                  final closeTime = firstPeriod['close']['time'];
                  final formattedOpenTime = '${openTime.substring(0, 2)}:${openTime.substring(2)}';
                  final formattedCloseTime = '${closeTime.substring(0, 2)}:${closeTime.substring(2)}';
                  timeRange = '$formattedOpenTime - $formattedCloseTime';
                }
              } else {
                timeRange = '10:00 - 12:00';
              }
            }
            // --- Short address logic ---
            if (result['address_components'] != null) {
              final comps = result['address_components'] as List;
              String? street, district, cityComp;
              for (var comp in comps) {
                if ((comp['types'] as List).contains('route')) street = comp['long_name'];
                if ((comp['types'] as List).contains('sublocality') || (comp['types'] as List).contains('administrative_area_level_2')) district = comp['long_name'];
                if ((comp['types'] as List).contains('locality')) cityComp = comp['long_name'];
              }
              // Add place name at the beginning
              final parts = [suggestion.name, street, district, cityComp].where((e) => e != null && e.isNotEmpty).toList();
              if (parts.isNotEmpty) shortAddress = parts.join(', ');
            }
          }
        }

        try {
          List<Placemark> placemarks = await placemarkFromCoordinates(
            suggestion.location.latitude,
            suggestion.location.longitude,
          );

          if (placemarks.isNotEmpty) {
            Placemark place = placemarks.first;
            city = place.locality;
          }
        } catch (e) {
          logd("Error getting city from geocoding: $e", tag: _tag);
        }
      } catch (e) {
        logd("Error getting place details: $e", tag: _tag);
      }

      final updatedState = state.copyWith(
        selectedLocation: suggestion.location,
        selectedAddress: shortAddress,
        // selectedDescription: suggestion.name,
        selectedPhotoUrl: photoUrl,
        selectedCity: city,
        selectedOpeningHours: openingHours,
        selectedTime: timeRange ?? '10:00 - 12:00',
        isLoading: false,
      );
      emit(updatedState);
    } catch (e) {
      logd("onSelectSuggestion $e", tag: _tag);
      emit(state.copyWith(isLoading: false));
    }
  }

  /// Handle map tap events
  Future<void> onMapTap(LatLng location) async {
    try {
      emit(state.copyWith(isLoading: true));

      String? address;
      String? city;
      try {
        List<Placemark> placemarks = await placemarkFromCoordinates(
          location.latitude,
          location.longitude,
        );

        if (placemarks.isNotEmpty) {
          Placemark place = placemarks.first;
          address = '${place.street}, ${place.locality}, ${place.country}';
          city = place.locality;
        }
      } catch (e) {
        logd("Error getting address from geocoding: $e", tag: _tag);
        address = 'Selected Location';
      }

      emit(state.copyWith(
        selectedLocation: location,
        selectedAddress: address,
        selectedCity: city,
        selectedTime: '10:00 - 12:00',
        isLoading: false,
      ));
    } catch (e) {
      logd("onMapTap $e", tag: _tag);
      emit(state.copyWith(isLoading: false));
    }
  }

  /// Handle confirmation of place selection
  Future<void> onConfirm(BuildContext context) async {
    try {
      if (state.selectedLocation == null || state.selectedDescription == null || state.selectedAddress == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please select a place from the search results'),
            ),
          );
        }
        return;
      }

      emit(state.copyWith(isLoading: true));

      String? activityImage = state.selectedPhotoUrl;
      if (activityImage != null) {
        try {
          final response = await http.get(Uri.parse(activityImage));
          if (response.statusCode != 200) {
            activityImage = parameter.place?.activities?.firstOrNull?.activityImage;
          }
        } catch (e) {
          logd("Error verifying photo URL: $e", tag: _tag);
          activityImage = parameter.place?.activities?.firstOrNull?.activityImage;
        }
      } else {
        activityImage = parameter.place?.activities?.firstOrNull?.activityImage;
      }

      // Convert old AM/PM format to 24-hour format if needed
      String timeRange = state.selectedTime ?? '10:00 - 12:00';
      if (timeRange == 'AM') {
        timeRange = '10:00 - 12:00';
      } else if (timeRange == 'PM') {
        timeRange = '16:00 - 18:00';
      }

      final updatedActivity = Activity(
        time: timeRange,
        description: state.selectedDescription ?? '',
        venue: state.selectedAddress ?? '',
        city: state.selectedCity ?? parameter.place?.activities?.firstOrNull?.city ?? '',
        latitude: state.selectedLocation!.latitude,
        longitude: state.selectedLocation!.longitude,
        activityImage: activityImage,
      );

      final updatedItinerary = Itinerary(
        activities: [updatedActivity],
      );

      final updatedParameter = PlaceUpsertParameter(
        tripId: parameter.tripId,
        dayIndex: parameter.dayIndex,
        activityIndex: parameter.activityIndex,
        place: updatedItinerary,
      );

      if (context.mounted) {
        Navigator.of(context).pop(updatedParameter);
      }
    } catch (e) {
      logd("Error in onConfirm: $e", tag: _tag);
      emit(state.copyWith(isLoading: false));
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error saving place data'),
          ),
        );
      }
    }
  }

  /// Fetch POIs near a location (e.g., restaurants, hotels)
  Future<void> fetchNearbyPois({
    required LatLng location,
    List<String> types = const ['restaurant', 'hotel', 'tourist_attraction'],
    int radiusMeters = 500,
  }) async {
    try {
      emit(state.copyWith(isLoading: true));
      final List<PoiMarker> pois = [];
      for (final type in types) {
        final url =
            'https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${location.latitude},${location.longitude}&radius=$radiusMeters&type=$type&key=$apiKey';
        final response = await http.get(Uri.parse(url));
        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['status'] == 'OK' && data['results'] != null) {
            for (final result in data['results']) {
              final loc = result['geometry']['location'];
              pois.add(PoiMarker(
                placeId: result['place_id'],
                name: result['name'],
                location: LatLng(loc['lat'], loc['lng']),
                type: type,
                address: result['vicinity'],
                photoReference: result['photos'] != null && result['photos'].isNotEmpty
                    ? result['photos'][0]['photo_reference']
                    : null,
              ));
            }
          }
        }
      }
      // Always pass a non-null list to copyWith
      emit(state.copyWith(poiMarkers: pois, isLoading: false));
    } catch (e) {
      logd('fetchNearbyPois error: $e', tag: _tag);
      emit(state.copyWith(isLoading: false));
    }
  }

  /// Update state when an activity marker is tapped in view mode
  void onSelectActivity(Activity activity) {
    emit(state.copyWith(
      selectedLocation: (activity.latitude != null && activity.longitude != null)
          ? LatLng(activity.latitude!, activity.longitude!)
          : null,
      selectedAddress: activity.venue,
      selectedDescription: activity.description,
      selectedPhotoUrl: activity.activityImage,
      selectedCity: activity.city,
      selectedTime: activity.time,
    ));
  }
}
