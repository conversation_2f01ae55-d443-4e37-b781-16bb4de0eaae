import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/booking_personal_info.dart';
import 'package:family_app/data/model/card_info.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/usecase/activity_usecase.dart';
import 'package:family_app/data/usecase/hotel_booking_usecase.dart';
import 'package:family_app/data/usecase/model/booking_hotel_param.dart';
import 'package:family_app/screen/main/trip/trip_detail_cubit.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'hotel_booking_info_parameter.dart';
import 'hotel_booking_info_state.dart';

class HotelBookingInfoCubit extends BaseCubit<HotelBookingInfoState> {
  final HotelBookingInfoParameter parameter;
  final HotelBookingUseCase usecase;
  final IActivityRepository activityRepository;
  final ActivityUsecase activityUsecase;
  final AccountService accountService;
  final LocalStorage localStorage;

  final kBookingPhoneCached = "kBookingPhoneCached";

  HotelBookingInfoCubit({
    required this.parameter,
    required this.activityRepository,
    required this.usecase,
    required this.accountService,
    required this.localStorage,
    required this.activityUsecase,
  }) : super(HotelBookingInfoState());

  final formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    _init();
  }

  _init() async {
    double totalPrice = parameter.offers.fold(
      0.0,
      (previousValue, element) => previousValue + (element.price) * element.quantity,
    );

    emit(state.copyWith(
        isLoading: false,
        totalPrice: totalPrice,
        personalInfo: BookingPersonalInfo(
          id: 1,
          firstName: accountService.account?.firstName ?? "",
          lastName: accountService.account?.lastName ?? "",
          email: accountService.account?.email ?? "",
        )));

    if (kDebugMode) {
      emit(state.copyWith(
        card: CardInfo(
          cardNumber: "****************",
          cardHolderName: "BOB SMITH",
          expiryDate: "2026-08",
          cvc: "123",
        ),
      ));
    }

    localStorage.getString(kBookingPhoneCached).then(
      (phone) {
        if (phone != null && phone.isNotEmpty) {
          emit(state.copyWith(
            personalInfo: state.personalInfo?.copyWith(phone: phone),
          ));
        }
      },
    );

    // String? phone = await localStorage.getString(kBookingPhoneCached);
  }

  void updateGuest(int index, BookingPersonalInfo newGuest) {
    logd("updateGuest: $index, $newGuest");
    emit(state.copyWith(personalInfo: newGuest, error: ""));
  }

  Future<void> onNext(BuildContext context) async {
    if (formKey.currentState?.validate() ?? false) {
      formKey.currentState?.save();
      showLoading();
      logd("onNext: ${state.personalInfo}, ${state.card}");
      if (state.personalInfo != null && state.card != null) {
        // var bookingResult = await amadeusRepository.bookHotel(params);
        var bookingResults = await Future.wait(parameter.offers.map((e) => usecase.bookHotel(
              BookingHotelParam(
                cardInfo: state.card!,
                personalInfo: state.personalInfo!,
                offer: e,
              ),
            )));
        for (var booking in bookingResults) {
          if (booking['success'] == false) {
            emit(state.copyWith(status: HotelBookingInfoStatus.error));
            dismissLoading();
            return;
          }
        }

        HotelBookingModel newBooking = HotelBookingModel(
          hotelId: parameter.hotel.id,
          hotelName: parameter.hotel.name,
          imageUrl: parameter.hotel.imageURL,
          location: parameter.location ?? "${parameter.hotel.city}, ${parameter.hotel.country}",
          provider: "Amadeus",
          checkInDate: parameter.checkInDate.yyyy_MM_dd,
          checkOutDate: parameter.checkOutDate.yyyy_MM_dd,
          bookingResults: bookingResults,
        );

        final List<HotelBookingModel> hotelBookings = List.from(parameter.activity.hotelBookings ?? []);
        // List<HotelBookingModel> hotelBookings = [];
        var updateBooking = false;
        for (int i = 0; i < hotelBookings.length; i++) {
          HotelBookingModel booking = hotelBookings[i];
          if (booking.hotelId == newBooking.hotelId &&
              (booking.bookingResults == null && booking.bookingResults!.isEmpty)) {
            hotelBookings[i] = newBooking;
            updateBooking = true;
            break;
          }
        }
        if(!updateBooking) {
          hotelBookings.add(newBooking);
        }


        final updatedActivity = parameter.activity.copyWith(hotelBookings: hotelBookings);
        final activityParameters = updatedActivity.toCreateActivityParameter();
        final result = await activityRepository.updateActivity(updatedActivity.uuid, activityParameters);
        if (result.uuid.isNotEmpty) {
          if (state.personalInfo?.phone?.isNotEmpty == true) {
            localStorage.setString(kBookingPhoneCached, state.personalInfo?.phone ?? "");
          }

          if (locator.isRegistered<TripDetailCubit>()) {
            locator.get<TripDetailCubit>().fetchImagesAndLocations();
          }
          emit(state.copyWith(status: HotelBookingInfoStatus.success));
        } else {
          emit(state.copyWith(status: HotelBookingInfoStatus.error, error: LocaleKeys.booking_failed.tr()));
        }
      } else {
        emit(state.copyWith(error: LocaleKeys.please_input_required_info.tr()));
      }
      dismissLoading();
    } else {
      emit(state.copyWith(error: LocaleKeys.please_input_required_info.tr()));
    }
  }

  onCardChanged(CardInfo p1) {
    logd("onCardChanged: $p1");
    emit(state.copyWith(card: p1));
  }
}
