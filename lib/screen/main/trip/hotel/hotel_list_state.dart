import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/hotel_model.dart';

class HotelListState extends BaseState {
  HotelListState({
    super.isLoading = false,
    this.isError = false,
    this.errorMessage,
    this.hotels,
    this.searchHotels,
    this.city,
    this.country,
  });

  final bool isError;
  final String? errorMessage;
  final List<HotelModel>? hotels;
  final List<HotelModel>? searchHotels;
  final String? city;
  final String? country;

  HotelListState copyWith({
    bool? isLoading,
    bool? isError,
    String? errorMessage,
    List<HotelModel>? hotels,
    List<HotelModel>? searchHotels,
    String? city,
    String? country,
  }) {
    return HotelListState(
      isLoading: isLoading ?? this.isLoading,
      isError: isError ?? this.isError,
      errorMessage: errorMessage ?? this.errorMessage,
      hotels: hotels ?? this.hotels,
      searchHotels: searchHotels ?? this.searchHotels,
      city: city ?? this.city,
      country: country ?? this.country,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    isError,
    errorMessage,
    hotels,
    searchHotels,
    city,
    country,
  ];
}