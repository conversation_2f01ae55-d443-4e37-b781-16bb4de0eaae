import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/hotel_model.dart';

class HotelDetailParameter {
  final String hotelId;
  final HotelModel? hotel;
  final String? location;
  final ActivityModel activity;
  final bool isSavedHotel;
  final int dayIndex;

  HotelDetailParameter({
    required this.hotelId,
    this.hotel,
    this.location,
    required this.activity,
    this.dayIndex = 0,
    this.isSavedHotel = false,
  });
}
