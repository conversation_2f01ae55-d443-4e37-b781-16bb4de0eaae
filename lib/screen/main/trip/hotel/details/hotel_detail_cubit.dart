import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/usecase/hotel_booking_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/booking/info/hotel_booking_info_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_parameter.dart';
import 'package:family_app/screen/main/trip/trip_detail_cubit.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/src/widgets/framework.dart';

import 'hotel_detail_state.dart';

class HotelDetailCubit extends BaseCubit<HotelDetailState> {
  final tag = "HotelDetailCubit";
  final HotelDetailParameter parameter;
  final HotelBookingUseCase usecase;
  final IActivityRepository activityRepository;

  HotelDetailCubit({required this.parameter, required this.activityRepository, required this.usecase})
      : super(HotelDetailState());

  @override
  void onInit() {
    super.onInit();
    var fromDate = parameter.activity.fromDate?.toLocalDT ?? DateTime.now();
    var toDate = parameter.activity.toDate?.toLocalDT ?? DateTime.now();

    print("fromDate: $fromDate");

    emit(state.copyWith(
      // budget: parameter.hotel.offers[0].price?.base,
      // cost: parameter.hotel.offers[0].price?.total,
      // currency: parameter.hotel.offers[0].price?.currency,
      checkInDate: fromDate.add(Duration(days: parameter.dayIndex)),
      checkOutDate: fromDate.add(Duration(days: parameter.dayIndex + 1)),
      tripStartDate: fromDate,
      tripEndDate: toDate,
    ));
    fetchOffers();
  }

  void fetchOffers() async {
    logd("fetchOffers", tag: tag);
    emit(state.copyWith(isLoading: true, offers: []));
    int guest = parameter.activity.hotelPreferences?.numberOfGuests ?? 1;

    // List<AmadeusHotelOfferModel> offers = [];
    try {
      if (parameter.hotel == null) {
        HotelModel? hotel = await usecase.getHotelById(parameter.hotelId);
        if (hotel != null) {
          emit(state.copyWith(hotel: hotel));
        } else {
          logd("Hotel not found for ID: ${parameter.hotelId}", tag: tag);
          emit(state.copyWith(isLoading: false, error: LocaleKeys.not_found.tr()));
          return;
        }
      }else{
        emit(state.copyWith(hotel: parameter.hotel));
      }

      for (var i = 1; i <= guest; i++) {
        // var resp = await amadeusRepository.getHotelOffers(
        //   "[${parameter.hotel.hotelId}]",
        //   i,
        //   checkInDate: state.checkInDate?.yyyy_MM_dd,
        //   checkOutDate: state.checkOutDate?.yyyy_MM_dd,
        // );
        try {
          var resp = await fetchHotelOffers(hotelIds: parameter.hotelId, adults: i, roomQuantity: 1);
          if (resp.isNotEmpty) {
            emit(state.copyWith(offers: [...state.offers, ...resp]));
          }
        } catch (e) {
          logd("Error fetching hotel offers for $i adults: $e", tag: tag);
          // showErrorToast(e.toString());
        }
      }

      double minPrice = 0;
      double maxPrice = 0;
      for (var i = 0; i < state.offers.length; i++) {
        var offer = state.offers[i];
        if (minPrice == 0 || offer.price < minPrice) {
          minPrice = offer.price;
        }
        if (maxPrice == 0 || offer.price > maxPrice) {
          maxPrice = offer.price;
        }
      }
      emit(state.copyWith(
        minPrice: minPrice,
        maxPrice: maxPrice,
        currency: state.offers[0].currency,
        offerCount: state.offers.length,
        isLoading: false,
      ));

      // emit(state.copyWith(offers: offers, offerCount: offers.length));

      // if (offers.isNotEmpty) {
      //   emit(state.copyWith(offers: offers[0].offers));
      // } else {
      //   emit(state.copyWith(isLoading: false));
      // }
    } catch (e) {
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<List<HotelOfferModel>> fetchHotelOffers({
    required String hotelIds,
    required int adults,
    required int roomQuantity,
  }) {
    try {
      return usecase.getHotelOffers(
        hotelIds,
        adults,
        checkInDate: state.checkInDate?.yyyy_MM_dd,
        checkOutDate: state.checkOutDate?.yyyy_MM_dd,
        roomQuantity: roomQuantity,
      );
    } catch (e) {
      rethrow;
    }
  }

  void onCheckInDateSelected(DateTime dateTime) {
    emit(state.copyWith(checkInDate: dateTime));
  }

  void onCheckOutDateSelected(DateTime dateTime) {
    emit(state.copyWith(checkOutDate: dateTime));
    fetchOffers();
  }

  Future<void> onBookHotel(BuildContext context) async {
    if (state.hotel == null) {
      showSimpleToast(LocaleKeys.not_found.tr());
      return;
    }
    var selectedOffers = state.offers.where((offer) => offer.quantity > 0).toList();
    if (selectedOffers.isEmpty) {
      showSimpleToast(LocaleKeys.please_select_one_offer.tr());
      return;
    }
    var parameterBooking = HotelBookingInfoParameter(
      activity: parameter.activity,
      offers: selectedOffers,
      currency: state.currency ?? "USD",
      hotel: state.hotel!,
      dayIndex: parameter.dayIndex,
      checkInDate: state.checkInDate!,
      checkOutDate: state.checkOutDate!,
      location: parameter.location
    );
    await context.pushRoute(HotelBookingInfoRoute(parameter: parameterBooking));
  }

  onOfferQuantityChanged(HotelOfferModel offer, int index, int privQty) async {
    logd("onOfferQuantityChanged: $index  ${offer.id} => ${offer.quantity} ");
    var offers = List<HotelOfferModel>.from(state.offers);
    if (index < offers.length) {
      showLoading();
      try {
        var resp = await fetchHotelOffers(
          hotelIds: parameter.hotelId,
          adults: offer.adults,
          roomQuantity: offer.quantity == 0 ? 1 : offer.quantity,
        );
        if (resp.isNotEmpty) {
          offers[index] = resp.first;
          offers[index].quantity = offer.quantity;
          emit(state.copyWith(
            offers: offers,
          ));
        } else {
          offers[index].quantity = privQty;
          emit(state.copyWith(offers: offers));
        }
      } catch (e) {
        offers[index].quantity = privQty;
        emit(state.copyWith(
          offers: offers,
        ));
        showSimpleToast(e.toString());
      }

      dismissLoading();
    } else {
      logd("Index out of range: $index, offers lengthsss: ${offers.length}");
    }
  }

  Future<void> onSaveToTrip(BuildContext context) async {
    if (state.hotel == null) {
      showSimpleToast(LocaleKeys.not_found.tr());
      return;
    }
    showLoading();

    HotelBookingModel bookings = HotelBookingModel(
      hotelId: state.hotel!.id,
      hotelName: state.hotel!.name,
      imageUrl: state.hotel!.imageURL,
      location: parameter.location ?? "${state.hotel!.city}, ${parameter.hotel!.country}",
      provider: "Amadeus",
      checkInDate: state.checkInDate?.yyyy_MM_dd,
      checkOutDate: state.checkOutDate?.yyyy_MM_dd,
      // bookingResults: bookingResults,
    );

    final List<HotelBookingModel> hotelBookings = List.from(parameter.activity.hotelBookings ?? []);
    hotelBookings.add(bookings);

    final updatedActivity = parameter.activity.copyWith(hotelBookings: hotelBookings);

    final activityParameters = updatedActivity.toCreateActivityParameter();
    final result = await activityRepository.updateActivity(updatedActivity.uuid, activityParameters);
    if (result.uuid.isNotEmpty) {
      if (locator.isRegistered<TripDetailCubit>()) {
        locator.get<TripDetailCubit>().fetchImagesAndLocations();
      }
      emit(state.copyWith(status: HotelDetailStatus.success));
    } else {
      emit(state.copyWith(status: HotelDetailStatus.error, error: LocaleKeys.save_trip_failed.tr()));
    }
    dismissLoading();
  }
}
