import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/amadeus/hotel_offer_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/widgets/hotel_room_widget.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/extension/int_ext.dart';
import 'package:family_app/utils/formatter/currency_formatter.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/bottom_sheet/date_picker_bottom_sheet.dart';
import 'package:family_app/widget/image/custom_network_image.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/primary_outline_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'hotel_detail_cubit.dart';
import 'hotel_detail_state.dart';

@RoutePage()
class HotelDetailPage extends BaseBlocProvider<HotelDetailState, HotelDetailCubit> {
  const HotelDetailPage({required this.parameter, super.key});

  final HotelDetailParameter parameter;

  @override
  Widget buildPage() => const HotelDetailView();

  @override
  HotelDetailCubit createCubit() => HotelDetailCubit(
        parameter: parameter,
        activityRepository: locator.get(),
        usecase: locator.get(),
      );
}

class HotelDetailView extends StatefulWidget {
  const HotelDetailView({super.key});

  @override
  State<StatefulWidget> createState() => _HotelDetailViewState();
}

class _HotelDetailViewState extends BaseBlocPageState<HotelDetailView, HotelDetailState, HotelDetailCubit> {
  @override
  Widget buildAppBar(BuildContext context, HotelDetailCubit cubit, HotelDetailState state) {
    return CustomAppBar2(
      title: LocaleKeys.add_detail_hotel.tr(),
      showBack: true,
      actions: [],
    );
  }

  @override
  bool listenWhen(HotelDetailState previous, HotelDetailState current) {
    if(current.status == HotelDetailStatus.success) {
      int count = 0;
      Navigator.of(context).popUntil((route) {
        return count++ == 3;
      });
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, HotelDetailCubit cubit, HotelDetailState state) {
    return Container(
      margin: const EdgeInsets.only(left: 10, right: 10, top: 16),
      child: state.hotel == null
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: appTheme.whiteText,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: _buildBasicInfo(cubit, state)),
                      const SizedBox(height: 8),
                      Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: appTheme.whiteText,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: _buildBookingInfo(cubit, state)),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    if(!cubit.parameter.isSavedHotel) ...[
                      Expanded(
                        child: PrimaryButtonV2(
                          text: LocaleKeys.save_to_trip.tr(),
                          color: appTheme.primaryColorV2,
                          bg: appTheme.whiteText,
                          border: Border.all(color: appTheme.primaryColorV2),
                          onTap: () {
                            cubit.onSaveToTrip(context);
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],

                    Expanded(
                      child: PrimaryButtonV2(
                        text: LocaleKeys.book_now.tr(),
                        border: Border.all(color: appTheme.primaryColorV2),
                        onTap: () {
                          cubit.onBookHotel(context);
                        },
                      ),
                    ),
                  ],
                )
              ],
            ),
    );
  }

  Widget _buildBasicInfo(HotelDetailCubit cubit, HotelDetailState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10.0),
          child: Container(
            width: double.infinity,
            color: appTheme.backgroundV2,
            child: AspectRatio(
              aspectRatio: 16 / 9,
              child: state.hotel!.imageURL != null
                  ? CachedNetworkImage(
                      imageUrl: state.hotel!.imageURL!,
                      fit: BoxFit.cover,
                    )
                  : FutureBuilder(
                      future: state.hotel!.fetchImage(),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.done) {
                          return CachedNetworkImage(
                            imageUrl: snapshot.data!,
                            fit: BoxFit.cover,
                          );
                        } else if (snapshot.connectionState == ConnectionState.done) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        } else {
                          return const SizedBox();
                        }
                      }),
            ),
          ),
        ),
        const SizedBox(height: 10),
        Text(
          state.hotel?.name ?? "",
          style: AppStyle.bold16V2(),
        ),
        if (state.minPrice != null && state.maxPrice != null)
          Text(
            "${state.minPrice ?? 0} - ${state.maxPrice ?? 0} ${state.currency}",
            style: AppStyle.bold16V2(color: appTheme.primaryColorV2),
          ),
        Row(
          children: [
            Assets.icons.icAddress.svg(
              width: 16,
              height: 16,
              colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcIn),
            ),
            const SizedBox(width: 4),
            Text(
              cubit.parameter.location ?? "${state.hotel?.city}, ${state.hotel?.country}",
              style: AppStyle.regular12V2(color: appTheme.grayV2),
            ),
          ],
        ),
        // Text(
        //   LocaleKeys.description.tr(),
        //   style: AppStyle.bold12V2(),
        // ),
        // const SizedBox(height: 4),
        // if (state.offers.isNotEmpty)
        //   Text(
        //     state.offers[0].description?["text"] as String? ?? "",
        //     style: AppStyle.regular12V2(color: appTheme.grayV2),
        //   ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildBookingInfo(HotelDetailCubit cubit, HotelDetailState state) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.booking_info.tr(),
          style: AppStyle.bold14V2(color: appTheme.grayV2),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
                child: _buildSelectionDate(
              title: LocaleKeys.check_in.tr(),
              dateTime: state.checkInDate,
              disable: true,
              firstDate: state.tripStartDate ?? DateTime.now(),
              lastDate: state.tripEndDate ?? DateTime.now(),
              onSelected: (dateTime) {
                cubit.onCheckInDateSelected(dateTime);
              },
            )),
            const SizedBox(width: 8),
            Expanded(
                child: _buildSelectionDate(
              title: LocaleKeys.check_out.tr(),
              dateTime: state.checkOutDate,
              firstDate: state.tripStartDate?.add(const Duration(days: 1)) ?? DateTime.now(),
              lastDate: state.tripEndDate ?? DateTime.now(),
              onSelected: (dateTime) {
                cubit.onCheckOutDateSelected(dateTime);
              },
            ))
          ],
        ),
        // const SizedBox(height: 8),
        // Row(
        //   children: [
        //     Expanded(
        //         child: _buildInputMoney(
        //       title: LocaleKeys.budget.tr(),
        //       money: state.budget,
        //       currency: state.currency,
        //       onChanged: (value) => cubit.onBudgetChanged(value),
        //     )),
        //     const SizedBox(width: 8),
        //     Expanded(
        //         child: _buildInputMoney(
        //       title: LocaleKeys.cost.tr(),
        //       money: state.cost,
        //       currency: state.currency,
        //       onChanged: (value) => cubit.onCostChanged(value),
        //     ))
        //   ],
        // ),
        const SizedBox(height: 8),
        if (state.offers.isNotEmpty)
          ...List.generate(state.offers.length, (index) {
            final offer = state.offers[index];
            if (offer is AmadeusHotelOfferModel) {
              return HotelRoomWidget(
                offer: offer,
                index: index,
                state: state,
                onChangedQuantity: (offer, index, privQty) => cubit.onOfferQuantityChanged(offer, index, privQty),
              );
            }
            return const SizedBox();
          })
        else if (state.isLoading)
          const Center(
            child: CircularProgressIndicator(),
          )
        else
          const Center(
            child: Text(
              "No offers available",
              style: TextStyle(color: Colors.red),
            ),
          )
      ],
    );
  }

  Widget _buildSelectionDate(
      {bool disable = false,
      required String title,
      DateTime? dateTime,
      required DateTime firstDate,
      required DateTime lastDate,
      required Function(DateTime) onSelected}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppStyle.regular12V2(),
        ),
        const SizedBox(height: 4),
        Container(
          padding: padding(horizontal: 8, vertical: 8),
          decoration: BoxDecoration(
            color: appTheme.whiteText,
            borderRadius: const BorderRadius.all(Radius.circular(4)),
            border: Border.all(
              color: appTheme.borderColorV2,
              width: 1,
            ),
          ),
          child: InkWell(
            onTap: disable
                ? null
                : () async {
                    final DateTime? datePicked = await showDatePicker(
                      context: context,
                      initialDate: dateTime ?? firstDate,
                      firstDate: firstDate,
                      lastDate: lastDate,
                    );
                    if (datePicked != null) {
                      onSelected(datePicked);
                    }
                  },
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    dateTime?.ddMMyy ?? 'dd / MM / yyyy',
                    style: AppStyle.regular14V2(),
                  ),
                ),
                SvgPicture.asset(
                  Assets.icons.icCalendar32.path,
                  width: 24.w,
                  height: 24.h,
                  colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
