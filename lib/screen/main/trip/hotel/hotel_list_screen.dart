import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_parameter.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/search_custom_filed.dart';
import 'package:flutter/material.dart';

import 'hotel_list_cubit.dart';
import 'hotel_list_parameter.dart';
import 'hotel_list_state.dart';
import 'widgets/hotel_item_widget.dart';

@RoutePage()
class HotelListPage extends BaseBlocProvider<HotelListState, HotelListCubit> {
  const HotelListPage({required this.parameter, super.key});

  final HotelListParameter parameter;

  @override
  Widget buildPage() => const HotelListView();

  @override
  HotelListCubit createCubit() => HotelListCubit(
        activityRepository: locator.get(),
        useCase: locator.get(),
        parameter: parameter,
      );
}

class HotelListView extends StatefulWidget {
  const HotelListView({super.key});

  @override
  State<HotelListView> createState() => _HotelListViewState();
}

class _HotelListViewState extends BaseBlocPageState<HotelListView, HotelListState, HotelListCubit> {
  @override
  Widget buildAppBar(BuildContext context, HotelListCubit cubit, HotelListState state) {
    return CustomAppBar2(
      title: LocaleKeys.select_new_hotel.tr(),
      showBack: true,
      actions: [],
    );
  }

  @override
  Widget buildBody(BuildContext context, HotelListCubit cubit, HotelListState state) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.only(left: 10, right: 10, top: 16),
          child: SearchCustomField(
            radius: 30,
            hintText: LocaleKeys.search_by_hotel.tr(),
            backgroundColor: appTheme.whiteText,
            onGetSearchValue: (text) => cubit.onGetSearchValue(text),
          ),
        ),
        Expanded(
          child: state.isLoading
              ? const Center(child: CircularProgressIndicator())
              : state.isError
                  ? Center(
                      child: Text(
                        state.errorMessage ?? "Error",
                        style: const TextStyle(color: Colors.red),
                      ),
                    )
                  : Container(
                      margin: const EdgeInsets.only(top: 16, left: 10, right: 10),
                      decoration: BoxDecoration(
                        color: appTheme.whiteText,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: ListView.builder(
                        itemCount: state.searchHotels?.isNotEmpty == true
                            ? state.searchHotels?.length
                            : state.hotels?.length ?? 0,
                        padding: const EdgeInsets.all(8),
                        itemBuilder: (context, index) {
                          var hotelItem = state.searchHotels?.isNotEmpty == true
                              ? state.searchHotels![index]
                              : state.hotels![index];
                          return HotelItemWidget(
                              hotel: hotelItem,
                              onTap: (hotel) {
                                cubit.onTap2Hotel(context, hotel);
                              });
                        },
                      ),
                    ),
        )
      ],
    );
  }
}
