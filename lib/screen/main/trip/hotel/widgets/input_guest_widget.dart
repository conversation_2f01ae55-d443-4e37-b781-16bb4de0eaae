
import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/booking_personal_info.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

import 'booking_info_textfield.dart';

class InputGuestWidget extends StatefulWidget {
  const InputGuestWidget({
    super.key,
    required this.guest,
    required this.onChanged,
  });

  final BookingPersonalInfo guest;
  final Function(BookingPersonalInfo) onChanged;

  @override
  State<InputGuestWidget> createState() => _InputGuestWidgetState();
}

class _InputGuestWidgetState extends State<InputGuestWidget> {
  String? _firstName;
  String? _lastName;
  String? _email;
  String? _phone;

  @override
  void initState() {
    _firstName = widget.guest.firstName;
    _lastName = widget.guest.lastName;
    _email = widget.guest.email;
    _phone = widget.guest.phone;
    super.initState();
  }

  @override
  void dispose() {
    if(_lastName != null) {
      widget.guest.lastName = _lastName;
    }
    if(_firstName != null) {
      widget.guest.firstName = _firstName;
    }
    if(_email != null) {
      widget.guest.email = _email;
    }
    if(_phone != null) {
      widget.guest.phone = _phone;
    }
    widget.onChanged(widget.guest);
    _debounce?.cancel();
    super.dispose();
  }

  Timer? _debounce;
  void _onChanged() {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 100), () {
      widget.guest.firstName = _firstName;
      widget.guest.lastName = _lastName;
      widget.guest.email = _email;
      widget.guest.phone = _phone;
      print("InputGuestWidget: onChanged: ${widget.guest.toAmadeusJson()}");
      widget.onChanged(widget.guest);
    });
  }

  @override
  void didUpdateWidget(covariant InputGuestWidget oldWidget) {
    if(oldWidget.guest != widget.guest) {
      _firstName = widget.guest.firstName;
      _lastName = widget.guest.lastName;
      _email = widget.guest.email;
      _phone = widget.guest.phone;
      setState(() {});
    }
    super.didUpdateWidget(oldWidget);
  }



  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: padding(horizontal: 8, vertical: 12),
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: appTheme.whiteText,
        borderRadius: const BorderRadius.all(Radius.circular(20)),
        border: Border.all(
          color: appTheme.borderColorV2,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(LocaleKeys.booking_info.tr(), style: AppStyle.bold14V2()),
          const SizedBox(height: 8),
          BookingInfoTextField(
            label: LocaleKeys.first_name.tr(),
            text: widget.guest.firstName ?? "",
            onChanged: (newValue) {
              _firstName = newValue;
              _onChanged();
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return LocaleKeys.please_input_first_name.tr();
              }
              return null;
            },
          ),
          BookingInfoTextField(
            label: LocaleKeys.last_name.tr(),
            text: widget.guest.lastName ?? "",
            onChanged: (newValue) {
              _lastName = newValue;
              _onChanged();
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return LocaleKeys.please_input_last_name.tr();
              }
              return null;
            },
          ),
          BookingInfoTextField(
            label: LocaleKeys.email.tr(),
            text: widget.guest.email ?? "",
            keyboardType: TextInputType.emailAddress,
            onChanged: (newValue) {
              _email = newValue;
              _onChanged();
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return LocaleKeys.please_input_email.tr();
              }
              return null;
            },
          ),
          BookingInfoTextField(
            label: LocaleKeys.phone.tr(),
            keyboardType: TextInputType.phone,
            text: widget.guest.phone ?? "",
            onChanged: (newValue) {
              _phone = newValue;
              _onChanged();
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return LocaleKeys.please_input_phone.tr();
              }
              if(value.length < 9) {
                return LocaleKeys.invalid_phone_number.tr();
              }
              return null;
            },
          )
        ],
      ),
    );
  }
}

