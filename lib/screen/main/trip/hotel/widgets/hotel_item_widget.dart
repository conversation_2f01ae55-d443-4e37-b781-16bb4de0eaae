import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/int_ext.dart';
import 'package:flutter/material.dart';

class HotelItemWidget extends StatelessWidget {
  final HotelModel hotel;
  final Function(HotelModel hotel) onTap;

  const HotelItemWidget({super.key, required this.hotel, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: appTheme.backgroundV2,
        borderRadius: BorderRadius.circular(10),
      ),
      child: InkWell(
        onTap: () => onTap(hotel),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.0),
              child: Container(
                width: 96,
                height: 74,
                color: appTheme.backgroundV2, // Use color here directly
                child: hotel.imageURL != null
                    ? CachedNetworkImage(
                        imageUrl: hotel.imageURL!,
                        fit: BoxFit.cover,
                      )
                    : FutureBuilder(
                        future: hotel.fetchImage(),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState == ConnectionState.done) {
                            return CachedNetworkImage(
                              imageUrl: snapshot.data!,
                              fit: BoxFit.cover,
                            );
                          } else if (snapshot.connectionState == ConnectionState.done) {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          } else {
                            return const SizedBox();
                          }
                        }),
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    hotel.name ?? "",
                    style: AppStyle.bold14V2(),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Assets.icons.icAddress.svg(
                        width: 16,
                        height: 16,
                        colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcIn),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        "${hotel.city}, ${hotel.country}",
                        style: AppStyle.regular12V2(color: appTheme.grayV2),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  // Text(
                  //   LocaleKeys.price.tr(),
                  //   style: AppStyle.regular10V2(color: appTheme.grayV2),
                  // ),
                  // Text(
                  //   "${hotel.offers[0].price?.base.currency} - ${hotel.offers[0].price?.total.currency} ${hotel.offers[0].price?.currency}",
                  //   style: AppStyle.bold10V2(),
                  // ),
                ],
              ),
            ),
            const SizedBox(width: 10),
            if (hotel.overallRating != null)
              _buildRating(hotel.overallRating!)
            else
              FutureBuilder<double>(
                future: hotel.fetchRating(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.done) {
                    return _buildRating(snapshot.data ?? 0.0);
                  } else {
                    return const SizedBox.shrink();
                  }
                },
              ),
          ],
        ),
      ),
    );
  }

  _buildRating(double rating) {
    if (rating <= 0) {
      return const SizedBox.shrink();
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: appTheme.grayV2.withOpacity(0.2),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          Assets.icons.icStarRating.svg(),
          const SizedBox(width: 2),
          Text(
            rating.toStringAsFixed(1),
            style: AppStyle.bold14V2(color: appTheme.blackText),
          ),
        ],
      ),
    );
  }
}
