import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/usecase/model/activity_parameter.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/screen/main/trip/trip_detail_cubit.dart';
import 'package:family_app/config/service/app_service.dart';
import 'trip_upsert_state.dart';

class TripUpsertCubit extends BaseCubit<TripUpsertState> {
  final IActivityRepository activityRepository;
  final IFamilyRepository familyRepository;
  ActivityModel? _cachedActivity;

  TripUpsertCubit({required this.activityRepository, required this.familyRepository, TripUpsertState? initialState})
      : super(initialState ?? TripUpsertState());

  Future<void> init({String? tripId, TripUpsertState? editState}) async {
    if (editState != null) {
      emit(editState.copyWith(status: TripUpsertStatus.done));
      return;
    }

    if (tripId != null) {
      if (_cachedActivity?.uuid == tripId) {
        _updateStateFromActivity(_cachedActivity!);
        return;
      }

      emit(state.copyWith(status: TripUpsertStatus.loading));
      try {
        final activity = await activityRepository.getActivityById(tripId);
        _cachedActivity = activity;
        _updateStateFromActivity(activity);
      } catch (e) {
        AppLogger.e('Error fetching trip: $e');
        emit(state.copyWith(status: TripUpsertStatus.error));
      }
    } else {
      emit(TripUpsertState(status: TripUpsertStatus.done));
    }
  }

  void _updateStateFromActivity(ActivityModel activity) {
    emit(TripUpsertState(
      status: TripUpsertStatus.done,
      tripName: activity.name,
      fromDate: (activity.fromDate != null && activity.fromDate!.isNotEmpty) ? DateTime.parse(activity.fromDate!) : null,
      toDate: (activity.toDate != null && activity.toDate!.isNotEmpty) ? DateTime.parse(activity.toDate!) : null,
      countryCode: (activity.countryCode != null && activity.countryCode!.isNotEmpty) ? activity.countryCode!.split(',').first : null,
      coverImageFilePath: activity.imagePath ?? activity.getImagePath(),
    ));
  }

  void updateCoverImageFile(String? path) {
    if (state.coverImageFilePath != path) {
      emit(state.copyWith(coverImageFilePath: path));
    }
  }

  void updateTripName(String? name) {
    if (state.tripName != name) {
      emit(state.copyWith(tripName: name));
    }
  }

  void updateFromDate(DateTime? date) {
    if (state.fromDate != date) {
      emit(state.copyWith(fromDate: date));
    }
  }

  void updateToDate(DateTime? date) {
    if (state.toDate != date) {
      emit(state.copyWith(toDate: date));
    }
  }

  void addCountry(String country) {
    emit(state.copyWith(countryCode: country));
  }

  void removeCountry(String country) {
    if (state.countryCode == country) {
      emit(state.copyWith(countryCode: null));
    }
  }

  Future<bool> saveTrip({bool isEdit = false, String? tripId}) async {
    if (!_validateTripData()) {
      emit(state.copyWith(status: TripUpsertStatus.error));
      return false;
    }

    emit(state.copyWith(status: TripUpsertStatus.loading));
    try {
      final param = _toCreateActivityParameter();
      if (isEdit && tripId != null) {
        await activityRepository.updateActivity(tripId, param);
      } else {
        await activityRepository.createActivity(param);
      }
      emit(state.copyWith(status: TripUpsertStatus.success));
      // Refresh TripDetailCubit if registered (after save success)
      try {
        final tripDetailCubit = locator.isRegistered<TripDetailCubit>() ? locator.get<TripDetailCubit>() : null;
        await tripDetailCubit?.fetchImagesAndLocations();
      } catch (e) {
        AppLogger.e('Error refreshing TripDetailCubit: $e');
      }
      return true;
    } catch (e) {
      AppLogger.e('Error saving trip: $e');
      emit(state.copyWith(status: TripUpsertStatus.error));
      return false;
    }
  }

  bool _validateTripData() {
    if (state.tripName?.isEmpty ?? true) {
      return false;
    }
    if (state.fromDate == null || state.toDate == null) {
      return false;
    }
    if (state.countryCode == null || state.countryCode!.isEmpty) {
      return false;
    }
    return true;
  }

  CreateActivityParameter _toCreateActivityParameter() {
    return CreateActivityParameter(
      name: state.tripName ?? '',
      caption: '',
      fromDate: state.fromDate?.toIso8601String() ?? '',
      toDate: state.toDate?.toIso8601String() ?? '',
      includedMembers: const [],
      itinerary: const [],
      activityType: 'trip',
      color: '',
      description: '',
      familyId: _cachedActivity?.familyId ?? '',
      countryCode: state.countryCode,
      // additionalCities: state.countries.length > 1 ? state.countries.sublist(1) : null,
    );
  }
}
