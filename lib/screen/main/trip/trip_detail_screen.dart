import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/transfer_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:timeline_tile/timeline_tile.dart';

import 'trip_detail_cubit.dart';
import 'trip_detail_state.dart';

@RoutePage()
class TripDetailPage extends BaseBlocProvider<TripDetailState, TripDetailCubit> {
  const TripDetailPage({required this.parameter, super.key});

  final TripDetailParameter parameter;

  @override
  Widget buildPage() => const TripDetailView();

  @override
  TripDetailCubit createCubit() => TripDetailCubit(activityRepository: locator.get(), parameter: parameter);
}

class TripDetailView extends StatefulWidget {
  const TripDetailView({super.key});

  @override
  State<TripDetailView> createState() => _TripDetailViewState();
}

class _TripDetailViewState extends BaseBlocPageState<TripDetailView, TripDetailState, TripDetailCubit> {
  @override
  void initState() {
    super.initState();
    isTopSafeArea = false;
  }

  @override
  bool listenWhen(TripDetailState previous, TripDetailState current) {
    if (current.isSaving && !previous.isSaving) {
      showLoading();
    } else if (previous.isSaving && !current.isSaving) {
      dismissLoading();
      if (current.saveTripSuccess) {
        AppLogger.d('Save trip success 11');
        // context.pushRoute(const ActivityHomeRoute());
      } else {
        AppLogger.d('Save trip failed');
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, TripDetailCubit cubit, TripDetailState state) {
    final isEditMode = state.editMode;
    final titleController = TextEditingController(text: state.activity?.name ?? '');
    if (isEditMode) {
      titleController.selection = TextSelection.fromPosition(
        TextPosition(offset: titleController.text.length),
      );
    }
    final fromDate = state.activity?.fromDate?.toDateTime();
    final toDate = state.activity?.toDate?.toDateTime();

    return Container(
      margin: const EdgeInsets.all(8),
      child: ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(MediaQuery.of(context).viewPadding.top > 0
              ? MediaQuery.of(context).viewPadding.top
              : 24),
          topRight: Radius.circular(MediaQuery.of(context).viewPadding.top > 0
              ? MediaQuery.of(context).viewPadding.top
              : 24),
          bottomLeft: const Radius.circular(24),
          bottomRight: const Radius.circular(24),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                AspectRatio(
                  aspectRatio: 4.0 / 3.0,
                  child: Stack(
                    children: [
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: (state.activity?.imagePath != null &&
                                    state.activity?.imagePath?.isNotEmpty == true)
                                ? CachedNetworkImageProvider(state.activity?.imagePath ?? '')
                                : Image.asset(Assets.images.birthdayRegistryCover.path).image,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withOpacity(0.0),
                              Colors.black.withOpacity(0.8),
                            ],
                            stops: [0.5, 1],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    isEditMode
                        ? Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.15),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.yellowAccent, width: 2),
                            ),
                            child: TextField(
                              controller: titleController,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 20.0,
                              ),
                              decoration: const InputDecoration(
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              ),
                              onChanged: (value) {
                                cubit.updateTripTitle(value);
                              },
                            ),
                          )
                        : Text(
                            state.activity?.name ?? '',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 20.0,
                            ),
                          ),
                    const SizedBox(height: 4),
                    isEditMode
                        ? GestureDetector(
                            onTap: () async {
                              final picked = await showDateRangePicker(
                                context: context,
                                firstDate: DateTime(2000),
                                lastDate: DateTime(2100),
                                initialDateRange: fromDate != null && toDate != null
                                    ? DateTimeRange(start: fromDate, end: toDate)
                                    : null,
                              );
                              if (picked != null) {
                                cubit.updateTripDates(picked.start, picked.end);
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.10),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.yellowAccent, width: 2),
                              ),
                              child: Text(
                                "${_formatDate(state.activity?.fromDate)} - ${_formatDate(state.activity?.toDate)}",
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.normal,
                                  fontSize: 16.0,
                                ),
                              ),
                            ),
                          )
                        : Text(
                            "${_formatDate(state.activity?.fromDate)} - ${_formatDate(state.activity?.toDate)}",
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.normal,
                              fontSize: 16.0,
                            ),
                          ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: 44,
              left: 4,
              right: 4,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CircleItem(
                    onTap: () => context.maybePop(),
                    padding: padding(all: 2),
                    backgroundColor: appTheme.blackColor.withValues(alpha: 0.2),
                    child: Transform.rotate(
                      angle: pi,
                      child: Assets.icons.arrowRight.svg(
                        width: 32.w,
                        height: 32.w,
                        colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                      ),
                    ),
                  ),
                  const Expanded(child: SizedBox()),
                  CircleItem(
                    onTap: () {
                      final activityModel = state.activity;
                      if (activityModel == null) return;
                      final List<Activity> allActivities = [];
                      if (activityModel.itinerary != null) {
                        for (final day in activityModel.itinerary!) {
                          if (day.activities != null) {
                            allActivities.addAll(day.activities!);
                          }
                        }
                      }
                      context.pushRoute(PlaceMapSelectionRoute(
                        parameter: PlaceUpsertParameter(
                          tripId: state.activityId,
                          dayIndex: 0,
                          activityIndex: null,
                          place: null,
                          viewMode: true,
                          initialLocation: InitialLocation(
                            name: activityModel.city,
                            latLng: activityModel.gpsCoord
                          ),
                          allActivities: allActivities,
                        ),
                      ));
                    },
                    padding: padding(all: 8),
                    backgroundColor: appTheme.blackColor.withValues(alpha: 0.2),
                    child: Assets.icons.icMap.svg(
                        width: 22,
                        height: 22,
                        colorFilter: const ColorFilter.mode(
                            Colors.white, BlendMode.srcIn)),
                  ),
                  const SizedBox(width: 4),
                    CircleItem(
                    onTap: () async {
                      if (state.editMode) {
                      // Save latest data before exiting edit mode
                      await cubit.updateTripNameAndDates(
                        state.activity?.name ?? '',
                        state.activity?.fromDate?.toDateTime(),
                        state.activity?.toDate?.toDateTime(),
                      );
                      }
                      cubit.toggleEditMode();
                    },
                    padding: padding(all: 4.w),
                    backgroundColor: appTheme.blackColor.withValues(alpha: 0.2),
                    child: state.editMode
                        ? Assets.icons.icActionCheck.svg(
                          width: 25.w,
                          height: 25.w,
                          colorFilter: const ColorFilter.mode(
                          Colors.white, BlendMode.srcIn),
                        )
                        : Assets.icons.icEdit.svg(
                          width: 25.w,
                          height: 25.w,
                          colorFilter: const ColorFilter.mode(
                          Colors.white, BlendMode.srcIn),
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget buildBody(BuildContext context, TripDetailCubit cubit, TripDetailState state) {
    if (state.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
        padding: paddingV2(left: 8),
        child: DefaultTabController(
          length: state.days.length,
          initialIndex: state.selectedIndex,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TabBar(
                onTap: (value) => cubit.setSelectedIndex(value),
                isScrollable: true,
                tabs: state.days.map((day) => Tab(text: day)).toList(),
                tabAlignment: TabAlignment.start,
                indicatorSize: TabBarIndicatorSize.tab,
              ),
              Expanded(
                child: TabBarView(
                  children:
                      state.activity?.itinerary?.asMap().entries.map((entry) {
                            int index = entry.key;
                            Itinerary itinerary = entry.value;
                            return _buildActivities(
                                context, cubit, state, itinerary, index);
                          }).toList() ??
                          [],
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildActivities(BuildContext context, TripDetailCubit cubit, TripDetailState state, Itinerary itinerary, int dayIndex) {
    final timelineList = cubit.getTimelineList(state.activity!, dayIndex);
    final count = timelineList.length;
    return ListView.builder(
      padding: paddingV2(vertical: 8),
      itemCount: count + 1, // Add 1 for the "Add new item"
      itemBuilder: (context, index) {
        if (index == count) {
          return _buildAddNewItemTile(context, cubit, state, itinerary, dayIndex);
        }

        final timelineItem = timelineList[index];
        if (timelineItem.data is TransferModel) {
          return _buildTransferTimelineTile(context, cubit, state, timelineItem.data, index, count);
        } else if (timelineItem.data is HotelBookingModel) {
          return _buildHotelTile(context, cubit, state, timelineItem.data as HotelBookingModel, index, count);
        } else {
          return _buildActivityTimelineTile(context, cubit, state, timelineItem.data as Activity, index, count);
        }
      },
    );
  }

  Widget _buildHotelTile(BuildContext context, TripDetailCubit cubit, TripDetailState state, HotelBookingModel hotel, int index, int length) {
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32.w,
        height: 32.w,
        color: Colors.white,
        indicator: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: Assets.icons.icHotel.svg(width: 32.w, height: 32.w),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.borderColorV2,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding: EdgeInsets.only(left: 16.w, right: 8.w, top: 8.w, bottom: 16.w),
        child: _buildHotelItemView(context, cubit, state, hotel),
      ),
    );
  }

  Widget _buildTransferTimelineTile(BuildContext context, TripDetailCubit cubit, TripDetailState state, TransferModel transfer, int index, int length) {
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32.w,
        height: 32.w,
        color: Colors.white,
        indicator: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: Assets.icons.icTransfer.svg(width: 32.w, height: 32.w),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.borderColorV2,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding: EdgeInsets.only(left: 16.w, right: 8.w, top: 8.w, bottom: 16.w),
        child: _buildTransferItemView(context, cubit, state, transfer),
      ),
    );
  }

  Widget _buildAddNewItemTile(BuildContext context, TripDetailCubit cubit, TripDetailState state, Itinerary itinerary, int dayIndex) {
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32.w,
        height: 32.w,
        color: Colors.white,
        indicator: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: Assets.icons.icTimelineAdd.svg(width: 32.w, height: 32.w),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.borderColorV2,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding: EdgeInsets.only(left: 16.w, right: 8.w, top: 8.w, bottom: 16.w),
        child: SizedBox(
          height: 40,
          child: Align(
            alignment: Alignment.centerLeft,
            child: TextButton(
              onPressed: () {
                cubit.onAddNewItemPressed(context, dayIndex);
              },
              child: const Text(
                'Add new item',
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4E46B4)),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransferItemView(BuildContext context, TripDetailCubit cubit, TripDetailState state, TransferModel transfer) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: appTheme.borderColor),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                transfer.getFromDate(),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              Text(
                transfer.ticketNo ?? '',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4E46B4),
                ),
              ),
              Text(
                transfer.getToDate(),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                transfer.getFromTime(),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.circle,
                        size: 12,
                        color: Color(0xFF595D62),
                      ),
                      const Expanded(
                        child: DottedLine(
                          direction: Axis.horizontal,
                          lineThickness: 2.0,
                          dashLength: 4.0,
                          dashColor: Color(0xFF595D62),
                          dashGapLength: 4.0,
                          dashRadius: 0.0,
                        ),
                      ),
                      const SizedBox(width: 4),
                      SvgPicture.asset(
                        Assets.icons.icFlight.path,
                        width: 20,
                        height: 20,
                        colorFilter: const ColorFilter.mode(Color(0xFF595D62), BlendMode.srcIn),
                      ),
                      const SizedBox(width: 4),
                      const Expanded(
                        child: DottedLine(
                          direction: Axis.horizontal,
                          lineThickness: 2.0,
                          dashLength: 4.0,
                          dashColor: Color(0xFF595D62),
                          dashGapLength: 4.0,
                          dashRadius: 0.0,
                        ),
                      ),
                      const Icon(
                        Icons.circle,
                        size: 12,
                        color: Color(0xFF595D62),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                transfer.getToTime(),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                flex: 40,
                child: Text(
                  transfer.fromLocation ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.visible,
                  textAlign: TextAlign.start,
                ),
              ),
              Flexible(
                flex: 20,
                child: Text(
                  transfer.getDuration(),
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
              Flexible(
                flex: 40,
                child: Text(
                  transfer.toLocation ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.visible,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActivityTimelineTile(BuildContext context, TripDetailCubit cubit, TripDetailState state, Activity activity, int index, int length) {
    final isHotel = activity.description.toLowerCase().contains('hotel') ||
        activity.description.toLowerCase().contains('hostel');
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32.w,
        height: 32.w,
        color: Colors.white,
        indicator: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: isHotel
              ? Assets.icons.icHotel.svg(width: 32.w, height: 32.w)
              : Assets.icons.icPosition.svg(width: 32.w, height: 32.w),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.borderColorV2,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding: EdgeInsets.only(left: 16.w, right: 8.w, top: 8.w, bottom: 16.w),
        child: SizedBox(
          height: 150.w,
          child: _buildActivityItemView2(context, cubit, state, activity, index, isHotel),
        ),
      ),
    );
  }

  Widget _buildHotelItemView(BuildContext context, TripDetailCubit cubit, TripDetailState state, HotelBookingModel hotel) {
    return InkWell(
      onTap: () {
        if(hotel.bookingResults == null || hotel.bookingResults!.isEmpty) {
          cubit.onBookHotel(context, hotel);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: appTheme.borderColor),
        ),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Container(
                width: 100,
                height: 90,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(hotel.imageUrl ?? ''),
                    fit: BoxFit.cover,
                  ),
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      hotel.hotelName ?? '',
                      style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                      maxLines: 2,
                      overflow: TextOverflow.visible,
                    ),
                    _buildLocationView(context, hotel.location ?? ''),
                    // Optionally add more hotel details here
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItemView2(BuildContext context, TripDetailCubit cubit, TripDetailState state, Activity activity, int activityIndex, bool isHotel) {
    final imageUrl = activity.activityImage;
    return GestureDetector(
      onTap: () {
        final itinerary = Itinerary(activities: [activity]);
        if (state.editMode) {
          // Edit mode: go to PlaceUpsertRoute
          // TODO: Support category hotel later
          if (!isHotel) {
            InitialLocation? initialLocation;
            if (activity.latitude != null && activity.longitude != null) {
              initialLocation = InitialLocation(
                name: activity.city,
                latLng: LatLng(activity.latitude!, activity.longitude!),
              );
            } else {
              initialLocation = InitialLocation(
                name: activity.city,
                latLng: state.activity?.gpsCoord,
              );
            }
            context.pushRoute(PlaceUpsertRoute(
              parameter: PlaceUpsertParameter(
                tripId: state.activityId,
                dayIndex: state.selectedIndex,
                activityIndex: activityIndex,
                place: itinerary,
                initialLocation: initialLocation,
              ),
            )).then((value) {
              if (value == true) {
                cubit.fetchTripDetail();
              }
            });
          }
        } else {
          // View mode: go to map selection
          // TODO: Support category hotel later
          if (!isHotel) {
            InitialLocation? initialLocation;
            if (activity.latitude != null && activity.longitude != null) {
              initialLocation = InitialLocation(
                name: activity.city,
                latLng: LatLng(activity.latitude!, activity.longitude!),
              );
            } else {
              initialLocation = InitialLocation(
                name: activity.city,
                latLng: state.activity?.gpsCoord,
              );
            }
            context.pushRoute(PlaceMapSelectionRoute(
              parameter: PlaceUpsertParameter(
                tripId: state.activityId,
                dayIndex: state.selectedIndex,
                activityIndex: activityIndex,
                place: itinerary,
                viewMode: true,
                initialLocation: initialLocation,
                allActivities: [activity],
              ),
            ));
          }
        }
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.w),
          border: Border.all(color: appTheme.borderColorV2, width: 1.0),
        ),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Container(
                width: 96.w,
                height: 74.w,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: imageUrl?.isEmpty == false
                        ? CachedNetworkImageProvider(imageUrl!)
                        : Image.asset(Assets.images.birthdayRegistryCover.path).image,
                    fit: BoxFit.cover,
                  ),
                  borderRadius: BorderRadius.circular(8.w),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      activity.description,
                      style: const TextStyle(
                          fontSize: 12, fontWeight: FontWeight.bold),
                      maxLines: 2,
                      overflow: TextOverflow.visible,
                    ),
                    _buildLocationView(context, activity.venue ?? ''),
                    _buildTimeView(context, cubit, state, activity),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLocationView(BuildContext context, String location) {
    return ListTile(
      onTap: () {
        // Open map view
        AppLogger.d('On location pressed');
      },
      contentPadding: EdgeInsets.zero,
      minTileHeight: 0,
      minVerticalPadding: 4,
      minLeadingWidth: 0,
      horizontalTitleGap: 8,
      leading: Assets.icons.icLocation.svg(width: 16, height: 16),
      title: Text(
        location,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  Widget _buildTimeView(BuildContext context, TripDetailCubit cubit, TripDetailState state, Activity activity) {
    final isHotel = activity.description.toLowerCase().contains('hotel') ||
        activity.description.toLowerCase().contains('hostel');

    // Parse time range from activity.time
    String startTime = '10:00';
    String endTime = '12:00';

    if (activity.time.isNotEmpty) {
      if (activity.time == 'AM') {
        startTime = '10:00';
        endTime = '12:00';
      } else if (activity.time == 'PM') {
        startTime = '16:00';
        endTime = '18:00';
      } else if (activity.time.contains(' - ')) {
        final times = activity.time.split(' - ');
        if (times.length == 2) {
          startTime = times[0].trim();
          endTime = times[1].trim();
        }
      }
    }

    return Container(
      child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isHotel ? 'Check in' : 'Start time',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              startTime,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(width: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isHotel ? 'Check out' : 'End time',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              endTime,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ]),
    );
  }

  @override
  Widget? buildFloatingActionButton(BuildContext context, TripDetailCubit cubit, TripDetailState state) {
    return ElevatedButton(
      onPressed: () {
        AppLogger.d("Ask AI button pressed!");
        _goToEditTrip(state);
        // context.pushRoute(TripUpsertRoute(/* pass trip details if editing */));
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF4E46B4),
        // Background color (purple)
        foregroundColor: Colors.white,
        // Text and icon color
        textStyle: const TextStyle(
          fontSize: 16, // Adjust the font size as needed
          fontWeight: FontWeight.w500, // Medium fontWeight
        ),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        // Adjust padding as needed
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20), // Rounded corners
        ),
        elevation: 3, // Add a subtle elevation (shadow)
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min, // Ensure the Row takes only the space it needs
        children: [
          Assets.icons.icLightningBolt.svg(
            height: 20, // Adjust icon size
            width: 20, // Ensure the icon is white
          ),
          const SizedBox(width: 8), // Spacing between icon and text
          const Text('Ask AI'),
        ],
      ),
    );
  }

  Future<void> _goToEditTrip(TripDetailState state) async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      context.pushRoute(ChatRoute(parameter: ChatParameter(chatContext: ChatContext.getEditTripContext(token, state.activityId))));
    }
  }

  void _showEditTripBottomSheet(BuildContext context, TripDetailCubit cubit, TripDetailState state) async {
    final nameController = TextEditingController(text: state.activity?.name ?? '');
    DateTime? fromDate = state.activity?.fromDate?.toDateTime();
    DateTime? toDate = state.activity?.toDate?.toDateTime();
    bool showSuccess = false;
    bool isSaving = false;
    await BottomSheetUtils.showHeightReturnBool(
      context,
      height: 0.35,
      child: Padding(
        padding: EdgeInsets.only(
          left: 16,
          right: 16,
          top: 24,
          bottom: MediaQuery.of(context).viewInsets.bottom + 24,
        ),
        child: StatefulBuilder(
          builder: (context, setState) {
            void resetSuccess() {
              if (showSuccess) setState(() => showSuccess = false);
            }
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Edit Trip', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Trip Name',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (_) => resetSuccess(),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () async {
                          final picked = await showDatePicker(
                            context: context,
                            initialDate: fromDate ?? DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: toDate ?? DateTime(2100),
                          );
                          if (picked != null) {
                            setState(() {
                              fromDate = picked;
                              if (toDate != null && toDate!.isBefore(fromDate!)) {
                                toDate = fromDate;
                              }
                              resetSuccess();
                            });
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today, size: 18),
                              const SizedBox(width: 8),
                              Text(fromDate != null ? "${fromDate!.year}-${fromDate!.month.toString().padLeft(2, '0')}-${fromDate!.day.toString().padLeft(2, '0')}" : 'From Date'),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: GestureDetector(
                        onTap: () async {
                          final picked = await showDatePicker(
                            context: context,
                            initialDate: toDate ?? (fromDate ?? DateTime.now()),
                            firstDate: fromDate ?? DateTime(2000),
                            lastDate: DateTime(2100),
                          );
                          if (picked != null) {
                            setState(() {
                              toDate = picked;
                              resetSuccess();
                            });
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today, size: 18),
                              const SizedBox(width: 8),
                              Text(toDate != null ? "${toDate!.year}-${toDate!.month.toString().padLeft(2, '0')}-${toDate!.day.toString().padLeft(2, '0')}" : 'To Date'),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: (isSaving || nameController.text.trim().isEmpty || fromDate == null || toDate == null)
                        ? null
                        : () async {
                            setState(() => isSaving = true);
                            final name = nameController.text.trim();
                            await cubit.updateTripNameAndDates(name, fromDate!, toDate!);
                            setState(() {
                              showSuccess = true;
                              isSaving = false;
                            });
                            Navigator.of(context).pop(true); // Return true to close sheet
                            cubit.fetchImagesAndLocations();
                            cubit.fetchTripDetail();
                          },
                    child: isSaving ? const SizedBox(width: 18, height: 18, child: CircularProgressIndicator(strokeWidth: 2)) : const Text('Save'),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  String _formatDate(String? date) {
    if (date == null || date.isEmpty) return '';
    try {
      return date.toDateTime().MMM_d_yyyy;
    } catch (_) {
      return date;
    }
  }
}
