import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_destination_focus_screen.dart';
import 'package:flutter/material.dart';

/// Connection state for the VTP Discovery WebSocket
enum VTPConnState { none, success, error, timeout, done }

/// State class for the VTP Discovery Cubit
class VTPDiscoveryState extends BaseState {
  /// List of destination options received from the AI
  final List<DestinationOption> destinationOptions;

  /// Connection state of the WebSocket
  final VTPConnState connState;

  /// Whether the cubit is loading data
  final bool loading;

  /// Whether the cubit is waiting for a response from the server
  final bool isWaitingForResponse;

  /// Error message if any
  final String? errorMessage;

  /// Search term used for the current search
  final String searchTerm;

  /// Selected vibes for the current search
  final List<String> selectedVibes;

  /// Whether a trip plan is being loaded
  final bool isTripPlanLoading;

  /// The error message for trip plan loading if any
  final String? tripPlanErrorMessage;

  /// The AI-generated trip plan
  final Trip? aiTrip;

  VTPDiscoveryState({
    this.destinationOptions = const [],
    this.connState = VTPConnState.none,
    this.loading = true,
    this.isWaitingForResponse = false,
    this.errorMessage,
    this.searchTerm = '',
    this.selectedVibes = const [],
    this.isTripPlanLoading = false,
    this.tripPlanErrorMessage,
    this.aiTrip,
    bool isLoading = false,
  }) : super(isLoading: isLoading);

  @override
  List<Object?> get props => [
        destinationOptions,
        connState,
        loading,
        isWaitingForResponse,
        errorMessage,
        searchTerm,
        selectedVibes,
        isTripPlanLoading,
        tripPlanErrorMessage,
        aiTrip,
        isLoading,
      ];

  /// Create a copy of the state with updated values
  VTPDiscoveryState copyWith({
    List<DestinationOption>? destinationOptions,
    VTPConnState? connState,
    bool? loading,
    bool? isWaitingForResponse,
    String? errorMessage,
    String? searchTerm,
    List<String>? selectedVibes,
    bool? isTripPlanLoading,
    String? tripPlanErrorMessage,
    Trip? aiTrip,
    bool? isLoading,
  }) {
    return VTPDiscoveryState(
      destinationOptions: destinationOptions ?? this.destinationOptions,
      connState: connState ?? this.connState,
      loading: loading ?? this.loading,
      isWaitingForResponse: isWaitingForResponse ?? this.isWaitingForResponse,
      errorMessage: errorMessage ?? this.errorMessage,
      searchTerm: searchTerm ?? this.searchTerm,
      selectedVibes: selectedVibes ?? this.selectedVibes,
      isTripPlanLoading: isTripPlanLoading ?? this.isTripPlanLoading,
      tripPlanErrorMessage: tripPlanErrorMessage ?? this.tripPlanErrorMessage,
      aiTrip: aiTrip ?? this.aiTrip,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
