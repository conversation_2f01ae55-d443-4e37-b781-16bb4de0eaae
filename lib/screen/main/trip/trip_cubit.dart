import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/screen/main/home/<USER>/activity_home_cubit.dart';
import 'package:family_app/screen/main/trip/trip_parameter.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:family_app/utils/log/app_logger.dart';

import '../../../data/model/trip_model.dart';
import 'trip_state.dart';

class TripCubit extends BaseCubit<TripState> {
  final IActivityRepository activityRepository;
  final TripParameter parameter;

  ActivityHomeCubit? activityCubit; 


  TripCubit({required this.activityRepository, required this.parameter}) : super(TripState(trip: parameter.trip));

  @override
  void onInit() {
    super.onInit();

    if (locator.isRegistered<ActivityHomeCubit>()) {
      activityCubit = locator.get<ActivityHomeCubit>();
    } 


    fetchImagesAndLocations();
  }

  Future<void> fetchImagesAndLocations() async {
    emit(state.copyWith(loading: true));

    if (parameter.trip.country != null) {
      state.heroImageUrl = await provider.fetchImageUrl(parameter.trip.country!);
    } else {
      state.heroImageUrl = await provider.fetchImageUrl(parameter.trip.name);
    }

    // //store the link here to re-use later.
    // parameter.trip.tripImageUrl = state.heroImageUrl;

    // await Trip.fetchImagesAndLocations(parameter.trip.itinerary, provider);

    // Map<String, String> foodImageUrls = Map.from(state.foodImageUrls);
    // for (var itinerary in parameter.trip.itinerary) {
    //   if (itinerary.food != null) {
    //     for (var food in itinerary?.food ?? []) {
    //       foodImageUrls[food] = await provider.fetchImageUrl(food);
    //       //store the link here to re-use later.
    //       itinerary.foodImageUrl = foodImageUrls[food];
    //     }
    //   }
    //   if (itinerary.activities != null) {
    //     for (var act1 in itinerary.activities!) {
    //       //search for an image with activity name

    //       Activity act = act1 as Activity;
    //       act.activityImage = await provider.fetchImageUrl(act.venue ?? act.description);
    //     }
    //   }
    // }

    // emit(state.copyWith(loading: false, heroImageUrl: state.heroImageUrl, foodImageUrls: foodImageUrls));

    //only load the hero image
    emit(state.copyWith(loading: false, heroImageUrl: state.heroImageUrl));

    //    for each day in the itinerary, fetch the image and location details
    for (int i = 0; i < parameter.trip.itinerary.length; i++) {
      fetchDayData(i);
    }
  }

  void fetchDayData(int dayIndex) async {
    if (state.dayLoadedStatus[dayIndex] == true) return;

    // Fetch data for the specific day
    var itinerary = parameter.trip.itinerary[dayIndex];
    Map<String, String> foodImageUrls = Map.from(state.foodImageUrls);

    logd("\n Day $dayIndex: ${itinerary.accommodation} - ${itinerary.activities} \n");
    if (itinerary.activities != null) {
      //if imageUrl is not null and empty fetch the accommodation image
      if (itinerary.imageUrl == null || !itinerary.imageUrl!.startsWith("https://")) {
        String searchString = itinerary.accommodation ??
            itinerary.activities![0].venue ??
            '' + ', ' + parameter.trip.city! + ', ' + parameter.trip.country!;
        // logd("searchString: $searchString");

        itinerary.imageUrl = await provider.fetchImageUrl(searchString);
      } else {
        // there's probaly a link in it.
        // logd("2.1 imageUrl is not empty: old link ${itinerary.imageUrl}");
      }
      // logd("3 Conclusion for: ${itinerary.accommodation} \n image url: ${itinerary.imageUrl}");

      //XXX: Think about this later.
      // itinerary.locationDetails =
      // await provider.fetchLocationDetails(itinerary.accommodation);
    } else {
      print("empty activity for: $itinerary");
    }

    // logd(" Food: ${itinerary.food} ");
    if (itinerary.food != null) {
      //food is a map {food: url} ,
      // for each food, search for an image with the food name , and save the url in the map
      for (var food in itinerary.food!.keys) {
        //if an url exists , skip this item
        if (itinerary.food![food] != null && itinerary.food![food]!.startsWith("https://")) {
          // logd("1.1 imageUrl is not empty: old link ${itinerary.food![food]}");
          continue;
        }

        String searchString = food + ', ' + parameter.trip.city! + ', ' + parameter.trip.country!;
        itinerary.food![food] = await provider.fetchImageUrl(searchString);
        // logd("found foodImage: $food -  ${itinerary.food![food]}");
      }
    }

    // logd("----- Activities: ${itinerary.activities} ----");
    if (itinerary.activities != null) {
      for (var act1 in itinerary.activities!) {
        Activity act = act1 as Activity;

        String searchString =
            act.venue ?? act.description + ', ' + parameter.trip.city! + ', ' + parameter.trip.country!;
        act.activityImage = await provider.fetchImageUrl(searchString);
        // logd("found activityImage: $act -  ${act.activityImage}");
      }
    }

    // Update the state to mark the day as loaded
    emit(state.copyWith(
      dayLoadedStatus: {...state.dayLoadedStatus, dayIndex: true},
      foodImageUrls: foodImageUrls,
    ));
  }

  Future<void> saveTrip() async {
    emit(state.copyWith(isSaving: true));
    bool saveTripSuccess = false;
    try {
      var result = null;
      if (parameter.trip.uuid != null && parameter.trip.uuid!.isNotEmpty) {
        result =
            await activityRepository.updateActivity(parameter.trip.uuid!, parameter.trip.toCreateActivityParameter());
        logd('Update trip result: ${result.itinerary}');
      } else {
        result = await activityRepository.createActivity(parameter.trip.toCreateActivityParameter());
        AppLogger.d('Create trip result: ${result}');
      }

      saveTripSuccess = result != null;

      if (saveTripSuccess) {
        // XXX: can be better if we can just update the activity in the list (locally)
        if (activityCubit != null) {
          // Update the activity list in the ActivityHomeCubit
          activityCubit!.fetchActivity();
       }
 
      }
    } catch (e) {
      print("Error saving trip: $e");
      // Handle save error
    } finally {
      emit(state.copyWith(isSaving: false, saveTripSuccess: saveTripSuccess));
    }
  }
}
