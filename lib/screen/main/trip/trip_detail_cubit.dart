import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_parameter.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/src/widgets/framework.dart';

import 'trip_detail_state.dart';
import 'trip_select_activity_type_parameter.dart';
import 'trip_select_activity_type_screen.dart';

class TripDetailCubit extends BaseCubit<TripDetailState> {
  final IActivityRepository activityRepository;
  final TripDetailParameter parameter;
  ActivityModel? _cachedActivity;

  TripDetailCubit({required this.activityRepository, required this.parameter})
      : super(TripDetailState(
            activityId: parameter.activityId, activity: parameter.activity));

  @override
  void onInit() {
    super.onInit();
    locator.registerSingleton(this);
    fetchImagesAndLocations();
  }

  @override
  Future<void> close() {
    locator.unregister<TripDetailCubit>();
    return super.close();
  }

  void setSelectedIndex(int index) {
    if (index != state.selectedIndex) {
      emit(state.copyWith(selectedIndex: index));
    }
  }

  Future<void> fetchImagesAndLocations() async {
    if (state.loading) return;

    emit(state.copyWith(loading: true));
    try {
      final activity = await activityRepository
          .getActivityById(parameter.activity?.uuid ?? parameter.activityId);

      _cachedActivity = activity;
      await _processActivityData(activity);
    } catch (e) {
      AppLogger.e('Error in fetchImagesAndLocations: $e');
      emit(state.copyWith(loading: false));
    }
  }

  Future<void> _processActivityData(ActivityModel activity) async {
    try {
      // Update days list
      final days = activity.itinerary?.asMap().entries.map((entry) {
            return 'Day ${entry.key + 1}';
          }).toList() ??
          [];

      // Fetch trip images if needed
      if (activity.imagePath == null) {
        await _fetchTripImages(activity);
      }

      logd("trip Hotel info: ${activity?.hotelBookings}");

      emit(state.copyWith(
        loading: false,
        activity: activity,
        heroImageUrl: activity.imagePath,
        days: days,
      ));
    } catch (e) {
      AppLogger.e('Error processing activity data: $e');
      emit(state.copyWith(loading: false));
    }
  }

  Future<void> _fetchTripImages(ActivityModel activity) async {
    try {
      if (activity.city != null) {
        activity.imagePath = await provider.fetchImageUrl(activity.city!);
      } else if (activity.country != null) {
        activity.imagePath = await provider.fetchImageUrl(activity.country!);
      }

      if (activity.fromDate != null && activity.toDate != null) {
        activity.trip_duration = activity.toDate!.toLocalDT
                .difference(activity.fromDate!.toLocalDT)
                .inDays +
            1;
      }
    } catch (e) {
      AppLogger.e('Error fetching trip images: $e');
    }
  }

  Future<void> fetchTripDetail() async {
    if (state.loading) return;

    emit(state.copyWith(loading: true));
    try {
      final result = await activityRepository.getActivityById(state.activityId);
      _cachedActivity = result;
      _updateDaysList();
      emit(state.copyWith(
        loading: false,
        activity: result,
        heroImageUrl: result.imagePath,
      ));
    } catch (e) {
      AppLogger.e("Error fetching trip: $e");
      emit(state.copyWith(loading: false));
    }
  }

  void _updateDaysList() {
    if (_cachedActivity?.itinerary == null) return;

    final days = _cachedActivity!.itinerary!.asMap().entries.map((entry) {
      return 'Day ${entry.key + 1}';
    }).toList();

    emit(state.copyWith(days: days));
  }

  List<ActivityTimelineItem> getTimelineList(
      ActivityModel activityModel, int dayIndex) {
    if (dayIndex < 0 || dayIndex >= (activityModel.itinerary?.length ?? 0)) {
      return [];
    }

    final List<ActivityTimelineItem> timelineItems = [];
    final itinerary = activityModel.itinerary![dayIndex];
    final dateTime =
        activityModel.fromDate?.toLocalDT.add(Duration(days: dayIndex));

    if (dateTime == null) return [];

    _addActivitiesToTimeline(timelineItems, itinerary, dateTime);
    _addTransfersToTimeline(timelineItems, itinerary);
    _addHotelBookingsToTimeline(timelineItems, activityModel, dateTime);

    return timelineItems;
  }

  /// Add activities to timeline
  void _addActivitiesToTimeline(List<ActivityTimelineItem> timelineItems,
      Itinerary itinerary, DateTime dateTime) {
    itinerary.activities?.forEach((activity) {
      DateTime timelineDateTime;

      if (activity.time.isEmpty) {
        timelineDateTime =
            DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
      } else if (activity.time == 'AM') {
        timelineDateTime =
            DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
      } else if (activity.time == 'PM') {
        timelineDateTime =
            DateTime(dateTime.year, dateTime.month, dateTime.day, 16, 0);
      } else if (activity.time.contains(' - ')) {
        final times = activity.time.split(' - ');
        if (times.length == 2) {
          final startTime = times[0].trim().split(':');
          if (startTime.length == 2) {
            final hour = int.tryParse(startTime[0]) ?? 10;
            final minute = int.tryParse(startTime[1]) ?? 0;
            timelineDateTime = DateTime(
                dateTime.year, dateTime.month, dateTime.day, hour, minute);
          } else {
            timelineDateTime =
                DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
          }
        } else {
          timelineDateTime =
              DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
        }
      } else {
        timelineDateTime =
            DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
      }

      timelineItems.add(
          ActivityTimelineItem(dateTime: timelineDateTime, data: activity));
    });
  }

  void _addTransfersToTimeline(
      List<ActivityTimelineItem> timelineItems, Itinerary itinerary) {
    itinerary.transfers?.forEach((transfer) {
      if (transfer.fromTime != null) {
        timelineItems.add(ActivityTimelineItem(
            dateTime: transfer.fromTime!.toLocalDT, data: transfer));
      }
    });
  }

  void _addHotelBookingsToTimeline(List<ActivityTimelineItem> timelineItems,
      ActivityModel activityModel, DateTime dateTime) {
    activityModel.hotelBookings?.forEach((booking) {
      if (booking.checkInDate != null && booking.checkOutDate != null) {
        var checkInMilis =
            booking.checkInDate!.toLocalDT.millisecondsSinceEpoch;
        var checkOutMilis =
            booking.checkOutDate!.toLocalDT.millisecondsSinceEpoch;
        var dayTripMilis = dateTime!.millisecondsSinceEpoch;

        if (dayTripMilis >= checkInMilis && dayTripMilis < checkOutMilis) {
          // If the trip day is between check-in and check-out dates, add the booking to the timeline
          timelineItems.add(ActivityTimelineItem(
              dateTime: booking.checkInDate!.toLocalDT, data: booking));
        }
      }
    });
  }

  void onAddNewItemPressed(BuildContext context, int dayIndex) {
    if (state.activity == null) return;

    BottomSheetUtils.showHeightReturnBool(context,
            height: 0.3,
            child: TripSelectActivityTypeBts(
                parameter:
                    TripSelectActivityTypeParameter(state.activity!, dayIndex)))
        .then((value) {
      AppLogger.d('Add new item pressed return value: $value');

      if (value == true) {
        fetchImagesAndLocations();
      }
    });
  }

  Future<void> onBookHotel(
      BuildContext context, HotelBookingModel hotel) async {
    if (parameter.activity == null) return;
    await context.pushRoute(HotelDetailRoute(
        parameter: HotelDetailParameter(
      hotelId: hotel.hotelId ?? '',
      activity: parameter.activity!,
      location: hotel.location,
      dayIndex: state.selectedIndex,
      isSavedHotel: true,
    )));
  }

  void toggleEditMode() {
    emit(state.copyWith(editMode: !state.editMode));
  }

  Future<void> updateTripNameAndDates(
      String name, DateTime? fromDate, DateTime? toDate) async {
    if (state.activity == null) return;
    emit(state.copyWith(isSaving: true));
    try {
      final updatedActivity = state.activity!.copyWith(
        name: name,
        fromDate: fromDate?.toIso8601String(),
        toDate: toDate?.toIso8601String(),
      );
      await activityRepository.updateActivity(
          updatedActivity.uuid, updatedActivity.toCreateActivityParameter());
      emit(state.copyWith(
        activity: updatedActivity,
        isSaving: false,
        saveTripSuccess: true,
      ));
    } catch (e) {
      AppLogger.e('Error updating trip name and dates: $e');
      emit(state.copyWith(isSaving: false, saveTripSuccess: false));
    }
  }

  // Update trip title only
  void updateTripTitle(String name) {
    if (state.activity == null) return;
    final updatedActivity = state.activity!.copyWith(name: name);
    emit(state.copyWith(activity: updatedActivity));
  }

  // Update trip dates only
  void updateTripDates(DateTime fromDate, DateTime toDate) {
    if (state.activity == null) return;
    final updatedActivity = state.activity!.copyWith(
      fromDate: fromDate.toIso8601String(),
      toDate: toDate.toIso8601String(),
    );
    emit(state.copyWith(activity: updatedActivity));
  }
}
