
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/upload/iupload_repository.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_parameter.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';

class TripSelectActivityTypeCubit extends BaseCubit<TripSelectActivityTypeState> {
  final TripSelectActivityTypeParameter parameter;
  final IFamilyRepository familyRepository;
  final IUploadRepository uploadRepository;
  final IActivityRepository activityRepository;
  
  TripSelectActivityTypeCubit(
      {required this.parameter,
      required this.familyRepository,
      required this.uploadRepository,
      required this.activityRepository})
      : super(TripSelectActivityTypeState(activity: parameter.activity, dayIndex: parameter.dayIndex));
  final AccountService accountService = locator.get();

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();
    initActivityTypes();
  }

  void initActivityTypes() {
    final dateTime = parameter.activity.fromDate?.toLocalDT.add(Duration(days: parameter.dayIndex));
    bool isDisabled = false;
    parameter.activity.hotelBookings?.forEach((booking) {
      if (booking.checkInDate != null && booking.checkOutDate != null) {
        var checkInMilis = booking.checkInDate!.toLocalDT.millisecondsSinceEpoch;
        var checkOutMilis = booking.checkOutDate!.toLocalDT.millisecondsSinceEpoch;
        var dayTripMilis = dateTime!.millisecondsSinceEpoch;

        if (dayTripMilis >= checkInMilis && dayTripMilis < checkOutMilis) {
          // If the trip day is between check-in and check-out dates, add the booking to the timeline
          isDisabled = true;
        }
      }
    });


    final List<ActivityType> activityTypes = [
      ActivityType(key: 'hotel', label: 'Hotel', iconPath: Assets.icons.icActivityTypeHotel.path, disabled: isDisabled),
      ActivityType(key: 'transfer', label: 'Transfer', iconPath: Assets.icons.icActivityTypeTransfer.path),
      ActivityType(key: 'place', label: 'Place', iconPath: Assets.icons.icActivityTypePlace.path),
    ];
    emit(state.copyWith(activityTypes: activityTypes));
  }

  @override
  Future<void> close() {
    locator.unregister<TripSelectActivityTypeCubit>();
    return super.close();
  }
}

class ActivityType {
  final String label;
  final String iconPath;
  final String key;
  final bool disabled;

  ActivityType({required this.label, required this.iconPath, required this.key, this.disabled = false});
}
