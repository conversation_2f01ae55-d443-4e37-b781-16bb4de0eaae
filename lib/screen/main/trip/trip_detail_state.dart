import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';

class TripDetailState extends BaseState {
  final String activityId;
  final ActivityModel? activity;
  final bool loading;
  final bool isSaving;
  final bool saveTripSuccess;
  final int selectedIndex;
  final String? heroImageUrl;
  final Map<String, String> foodImageUrls;
  final List<String> days;
  final bool editMode;

  TripDetailState({
    required this.activityId,
    this.activity,
    this.loading = false,
    this.isSaving = false,
    this.saveTripSuccess = false,
    this.selectedIndex = 0,
    this.heroImageUrl,
    this.foodImageUrls = const {},
    this.days = const [],
    this.editMode = false,
  });

  @override
  List<Object?> get props => [
        activityId,
        activity,
        loading,
        isSaving,
        saveTripSuccess,
        selectedIndex,
        heroImageUrl,
        foodImageUrls,
        days,
        editMode,
      ];


  TripDetailState copyWith({
    String? activityId,
    ActivityModel? activity,
    bool? loading,
    bool? isSaving,
    bool? saveTripSuccess,
    int? selectedIndex,
    String? heroImageUrl,
    Map<String, String>? foodImageUrls,
    List<String>? days,
    bool? editMode,
  }) {
    return TripDetailState(
      activityId: activityId ?? this.activityId,
      activity: activity ?? this.activity,
      loading: loading ?? this.loading,
      isSaving: isSaving ?? this.isSaving,
      saveTripSuccess: saveTripSuccess ?? this.saveTripSuccess,
      selectedIndex: selectedIndex ?? this.selectedIndex,
      heroImageUrl: heroImageUrl ?? this.heroImageUrl,
      foodImageUrls: foodImageUrls ?? this.foodImageUrls,
      days: days ?? this.days,
      editMode: editMode == null ? this.editMode : editMode,
    );
  }
}
