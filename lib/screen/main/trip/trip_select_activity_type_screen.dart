import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_bloc_bottomsheet_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/hotel_list_parameter.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_cubit.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_parameter.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_state.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_parameter.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class TripSelectActivityTypeBts extends BaseBlocProvider<TripSelectActivityTypeState, TripSelectActivityTypeCubit> {
  const TripSelectActivityTypeBts({required this.parameter, super.key});
  final TripSelectActivityTypeParameter parameter;

  @override
  Widget buildPage() => const TripSelectActivityTypeScreen();

  @override
  TripSelectActivityTypeCubit createCubit() => TripSelectActivityTypeCubit(
        parameter: parameter,
        familyRepository: locator.get(),
        uploadRepository: locator.get(),
        activityRepository: locator.get(),
      );
}

class TripSelectActivityTypeScreen extends StatefulWidget {
  const TripSelectActivityTypeScreen({super.key});

  @override
  State<TripSelectActivityTypeScreen> createState() => _TripSelectActivityTypeScreenState();
}

class _TripSelectActivityTypeScreenState
    extends BaseBlocBottomSheetPageState<TripSelectActivityTypeScreen, TripSelectActivityTypeState, TripSelectActivityTypeCubit> {
  @override
  String get title => 'Select item type';

  @override
  bool listenWhen(TripSelectActivityTypeState previous, TripSelectActivityTypeState current) {
    if (current.status == TripSelectActivityTypeStatus.loading) {
      showLoading();
    } else {
      dismissLoading();
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, TripSelectActivityTypeCubit cubit, TripSelectActivityTypeState state) {
    return Container(
      padding: padding(horizontal: 16),
      height: 44,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Text(
            title,
            textAlign: TextAlign.center,
            style: AppStyle.medium16(color: appTheme.blackColor),
          )
        ],
      ),
    );
  }

  @override
  Widget buildBody(BuildContext context, TripSelectActivityTypeCubit cubit, TripSelectActivityTypeState state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3, // Number of columns
          crossAxisSpacing: 25.0, // Spacing between columns
          mainAxisSpacing: 16.0, // Spacing between rows
          childAspectRatio: 1, // Aspect ratio of each grid item (adjust as needed)
        ),
        itemCount: state.activityTypes.length,
        itemBuilder: (context, index) {
          return ActivityTypeCard(cubit: cubit, state: state, activityType: state.activityTypes[index]);
        },
      ),
    );
  }
}

class ActivityTypeCard extends StatelessWidget {
  final TripSelectActivityTypeCubit cubit;
  final TripSelectActivityTypeState state;
  final ActivityType activityType;

  const ActivityTypeCard({super.key, required this.cubit, required this.state, required this.activityType});

  @override
  Widget build(BuildContext context) {
    final bool isDisabled = activityType.disabled;

    return Opacity(
      opacity: isDisabled ? 0.5 : 1.0, // Apply blur effect if disabled
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(10.0), // Apply rounded corners to pressed effect
          onTap: isDisabled
              ? null
              : () {
                  // Handle activity type tap
                  AppLogger.d('Tapped on ${activityType.label}');
                  if (activityType.key == 'transfer') {
                    context.pushRoute(TripTransferUpsertRoute(
                      parameter: TripTransferUpsertParameter(
                        activity: state.activity,
                        dayIndex: state.dayIndex,
                      ),
                    )).then((value) {
                      AppLogger.d('Trip transfer upsert returned value: $value');
                      Navigator.of(context).pop(value);
                    });
                  } else if (activityType.key == 'hotel') {
                    context.pushRoute(HotelListRoute(
                      parameter: HotelListParameter(state.activity, state.dayIndex),
                    ));
                  } else if (activityType.key == 'place') {
                    // Only use initialLocation for new place creation (no activityIndex)
                    // If editing an existing place, the map will use its lat/long
                    InitialLocation? initialLocation;
                    Activity? firstWithCoords;
                    if (state.activity.itinerary != null) {
                      for (final day in state.activity.itinerary!) {
                        if (day.activities != null) {
                          for (final a in day.activities!) {
                            if (a.latitude != null && a.longitude != null) {
                              firstWithCoords = a;
                              break;
                            }
                          }
                        }
                        if (firstWithCoords != null) break;
                      }
                    }
                    if (firstWithCoords != null && firstWithCoords.latitude != null && firstWithCoords.longitude != null) {
                      initialLocation = InitialLocation(
                        name: firstWithCoords.city ?? 'Ho Chi Minh City',
                        latLng: LatLng(firstWithCoords.latitude!, firstWithCoords.longitude!),
                      );
                    } else {
                      // Defensive fallback: use city name or default
                      initialLocation = InitialLocation(
                        name: state.activity.city ?? 'Ho Chi Minh City',
                      );
                    }
                    context.pushRoute(
                      PlaceUpsertRoute(
                        parameter: PlaceUpsertParameter(
                          tripId: state.activity.uuid,
                          dayIndex: state.dayIndex,
                          activityIndex: null, // Ensure this is null for new place creation
                          initialLocation: initialLocation, // Only used for new place
                        ),
                      ),
                    ).then((value) {
                      Navigator.of(context).pop(value);
                    });
                  }
                },
          child: Padding(
            padding: const EdgeInsets.all(4),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  activityType.iconPath,
                  height: 20, // Reduced icon size
                  width: 20,
                ),
                const SizedBox(height: 2), // Reduced spacing
                Text(
                  activityType.label,
                  style: const TextStyle(fontSize: 11), // Slightly smaller font
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
