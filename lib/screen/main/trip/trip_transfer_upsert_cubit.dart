import 'dart:convert';
import 'dart:io';

import 'package:dartx/dartx_io.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/model/transfer_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/usecase/model/activity_parameter.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/upload.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class TripTransferUpsertCubit extends BaseCubit<TripTransferUpsertState> {
  final IActivityRepository activityRepository;
  final TripTransferUpsertParameter parameter;

  TripTransferUpsertCubit({required this.activityRepository, required this.parameter})
      : super(TripTransferUpsertState(activity: parameter.activity));

  final AccountService accountService = locator.get();

  @override
  void onInit() {
    super.onInit();
    initData();
  }

  void initData() {
    final selectedTransport = state.transports.first;
    final startTime = parameter.activity.fromDate?.toDateTime();
    // Departure date and time = start time + dayIndex days
    final departureDate = startTime?.add(Duration(days: parameter.dayIndex));
    final arrivalDate = departureDate;
    emit(state.copyWith(
        selectedTransport: selectedTransport,
        departureDate: departureDate,
        arrivalDate: arrivalDate,
        dayIndex: parameter.dayIndex));
  }

  void updateSelectedCurrency(String currency) {
    emit(state.copyWith(selectedCurrency: currency));
  }

  void updateQuantity(int quantity) {
    emit(state.copyWith(quantity: quantity));
  }

  void updatePrice(double price) {
    emit(state.copyWith(price: price));
  }

  void updateNote(String note) {
    emit(state.copyWith(note: note));
  }

  void updateTicketNo(String ticketNo) {
    emit(state.copyWith(ticketNo: ticketNo, isTicketNoValid: ticketNo.isNotEmpty));
  }

  void updateDepartureLocation(String location) {
    emit(state.copyWith(departureLocation: location, isDepartureLocationValid: location.isNotEmpty));
  }

  void updateArrivalLocation(String location) {
    emit(state.copyWith(arrivalLocation: location, isArrivalLocationValid: location.isNotEmpty));
  }

  void updateSelectedTransport(Transport transport) {
    emit(state.copyWith(selectedTransport: transport));
  }

  Future<void> pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles();

      if (result != null) {
        File file = File(result.files.single.path!);
        AppLogger.d('Picked file: ${file.path}');
        final List<File> currentFiles = List.from(state.uploadedFiles);
        currentFiles.add(file);
        emit(state.copyWith(uploadedFiles: currentFiles));
      } else {
        // User canceled the picker
        AppLogger.d('User canceled the picker');
      }
    } catch (e) {
      AppLogger.e('Error picking file: $e');
    }
  }

  void removeFile(int index) {
    final List<File> currentFiles = List.from(state.uploadedFiles);
    final removedFile = currentFiles.removeAt(index);
    AppLogger.d('Removed file: ${removedFile.path}');
    emit(state.copyWith(uploadedFiles: currentFiles));
  }

  Future<void> selectDate(BuildContext context, bool isDeparture) async {
    final date = isDeparture ? state.departureDate : state.arrivalDate;
    final DateTime? datePicked = await showDatePicker(
      context: context,
      initialDate: date ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (datePicked != null) {
      if (isDeparture) {
        // Handle departure date selection
        emit(state.copyWith(departureDate: datePicked, isDepartureDateValid: true));
      } else {
        // Handle arrival date selection
        emit(state.copyWith(arrivalDate: datePicked, isArrivalDateValid: true));
      }
    } else {
      if (isDeparture) {
        emit(state.copyWith(isDepartureDateValid: false));
      } else {
        emit(state.copyWith(isArrivalDateValid: false));
      }
    }
  }

  Future<void> selectTime(BuildContext context, bool isDeparture) async {
    final time = isDeparture ? state.departureTime : state.arrivalTime;
    final TimeOfDay? timePicked = await showTimePicker(
      context: context,
      initialTime: time ?? TimeOfDay.now(),
    );
    if (timePicked != null) {
      if (isDeparture) {
        // Handle departure time selection
        emit(state.copyWith(departureTime: timePicked, isDepartureTimeValid: true));
      } else {
        // Handle arrival time selection
        emit(state.copyWith(arrivalTime: timePicked, isArrivalTimeValid: true));
      }
    } else {
      if (isDeparture) {
        emit(state.copyWith(isDepartureTimeValid: false));
      } else {
        emit(state.copyWith(isArrivalTimeValid: false));
      }
    }
  }

  bool _validateFields() {
    bool isTicketNoValid = false;
    bool isDepartureLocationValid = false;
    bool isDepartureDateValid = false;
    bool isDepartureTimeValid = false;
    bool isArrivalLocationValid = false;
    bool isArrivalDateValid = false;
    bool isArrivalTimeValid = false;

    if (state.ticketNo?.isEmpty ?? true) {
      AppLogger.d('Ticket no is empty');
      isTicketNoValid = false;
    } else {
      isTicketNoValid = true;
    }

    if (state.departureLocation?.isEmpty ?? true) {
      AppLogger.d('Departure location is empty');
      isDepartureLocationValid = false;
    } else {
      isDepartureLocationValid = true;
    }
    isDepartureDateValid = state.departureDate != null;
    if (!isDepartureDateValid) {
      AppLogger.d('Departure date is empty');
    }
    isDepartureTimeValid = state.departureTime != null;
    if (!isDepartureTimeValid) {
      AppLogger.d('Departure time is empty');
    }

    if (state.arrivalLocation?.isEmpty ?? true) {
      AppLogger.d('Arrival location is empty');
      isArrivalLocationValid = false;
    } else {
      isArrivalLocationValid = true;
    }
    isArrivalDateValid = state.arrivalDate != null;
    if (!isArrivalDateValid) {
      AppLogger.d('Arrival date is empty');
    }
    isArrivalTimeValid = state.arrivalTime != null;
    if (!isArrivalTimeValid) {
      AppLogger.d('Arrival time is empty');
    }
    emit(state.copyWith(
      isTicketNoValid: isTicketNoValid,
      isDepartureLocationValid: isDepartureLocationValid,
      isDepartureDateValid: isDepartureDateValid,
      isDepartureTimeValid: isDepartureTimeValid,
      isArrivalLocationValid: isArrivalLocationValid,
      isArrivalDateValid: isArrivalDateValid,
      isArrivalTimeValid: isArrivalTimeValid,
    ));
    return isTicketNoValid &&
        isDepartureLocationValid &&
        isDepartureDateValid &&
        isDepartureTimeValid &&
        isArrivalLocationValid &&
        isArrivalDateValid &&
        isArrivalTimeValid;
  }

  Future<void> saveActivity() async {
    if (!_validateFields()) {
      return;
    }
    emit(state.copyWith(status: TripTransferUpsertStatus.loading));
    try {
      // Upload files
      final uploadedFiles = state.uploadedFiles;
      final uploader = Upload(familyId: accountService.familyId);
      List<StorageModel> storageModels = [];
      if (uploadedFiles.isNotEmpty) {
        for (final file in uploadedFiles) {
          AppLogger.d('Uploading file: ${file.path}');
          final storageModel = await uploader.uploadFile(file, file.name);
          if (storageModel.uuid?.isNotEmpty == true) {
            AppLogger.d('Uploaded file succeeded: ${storageModel.fileName}');
            storageModels.add(storageModel);
          } else {
            AppLogger.d('Error uploading file: ${file.path}');
          }
        }
      }

      // Get list of uuids from storage models
      final attachmentUuids = storageModels.map((e) => e.uuid).toList();

      // Create transfer model
      final transfer = TransferModel(
        type: state.selectedTransport!.name.toLowerCase(),
        ticketNo: state.ticketNo,
        fromLocation: state.departureLocation,
        fromTime: getDateTimeString(state.departureDate, state.departureTime),
        toLocation: state.arrivalLocation,
        toTime: getDateTimeString(state.arrivalDate, state.arrivalTime),
        currency: state.selectedCurrency,
        quantity: state.quantity,
        price: state.price,
        note: state.note,
        attachments: attachmentUuids,
      );
      // Build Activity model
      final List<Itinerary> itineraries = List.from(state.activity.itinerary ?? []);
      final selectedItinerary = itineraries[state.dayIndex];
      final List<TransferModel> transfers = List.from(selectedItinerary.transfers ?? []);
      transfers.add(transfer);
      selectedItinerary.transfers = transfers;
      itineraries[state.dayIndex] = selectedItinerary;
      // AppLogger.d('All itineraries data: ${jsonEncode(itineraries)}');
      // AppLogger.d('Selected itineraries data: ${jsonEncode(selectedItinerary)}');
      final updatedActivity = state.activity.copyWith(itinerary: itineraries);
      // AppLogger.d('Updated activity data: ${jsonEncode(updatedActivity)}');
      // Save activity
      final activityParameters = updatedActivity.toCreateActivityParameter();
      AppLogger.d('Saving transfer, activity parameters: ${jsonEncode(activityParameters)}');
      final result = await activityRepository.updateActivity(updatedActivity.uuid!, activityParameters);
      if (result.uuid.isNotEmpty) {
        emit(state.copyWith(status: TripTransferUpsertStatus.success));
      } else {
        emit(state.copyWith(status: TripTransferUpsertStatus.error));
      }
      return;
    } catch (e) {
      AppLogger.e('Error saving transfer: $e');
    }
    emit(state.copyWith(status: TripTransferUpsertStatus.error));
  }

  String getDateTimeString(DateTime? date, TimeOfDay? time) {
    if (date != null && time != null) {
      final dateTime = DateTime(
        date.year,
        date.month,
        date.day,
        time.hour,
        time.minute,
      ).toUtc(); // Convert to UTC
      return dateTime.toIso8601String(); // DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    }
    return '';
  }


}
