import 'dart:io';

import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';

enum TransportType { flight, train, drive, car, bus, ferry }

class Transport {
  final TransportType type;
  final String name;
  final String title;
  final String ticketNoTitle;
  final String fromLocationTitle;
  final String fromDateTitle;
  final String toLocationTitle;
  final String toDateTitle;
  final String icon;

  Transport({
    required this.type,
    required this.name,
    required this.title,
    required this.ticketNoTitle,
    required this.fromLocationTitle,
    required this.fromDateTitle,
    required this.toLocationTitle,
    required this.toDateTitle,
    required this.icon,
  });

  @override
  String toString() {
    // Print type and name
    return 'Transport{type: $type, name: $name}';
  }
}

enum TripTransferUpsertStatus { initial, loading, success, error }

class TripTransferUpsertState extends BaseState {
  final ActivityModel activity;
  final int dayIndex;
  final TripTransferUpsertStatus status;
  final Transport? selectedTransport;
  final String? ticketNo;
  final String? departureLocation;
  final DateTime? departureDate;
  final TimeOfDay? departureTime;
  final DateTime? arrivalDate;
  final TimeOfDay? arrivalTime;
  final String? arrivalLocation;
  final String selectedCurrency;
  final int quantity;
  final double price;
  final String? note;
  final List<File> uploadedFiles;

  final bool isTicketNoValid;
  final bool isDepartureLocationValid;
  final bool isDepartureDateValid;
  final bool isDepartureTimeValid;
  final bool isArrivalLocationValid;
  final bool isArrivalDateValid;
  final bool isArrivalTimeValid;

  final List<String> currencies = const ['USD', 'CNY', 'EUR', 'GBP', 'JPY', 'VND'];
  final List<Transport> transports = [
    Transport(
        type: TransportType.flight,
        name: 'Flight',
        title: 'Flight Information',
        ticketNoTitle: 'Flight No.',
        fromLocationTitle: 'Departure airport',
        fromDateTitle: 'Departure date',
        toLocationTitle: 'Arrival airport',
        toDateTitle: 'Arrival date',
        icon: Assets.icons.icFlight.path),
    Transport(
        type: TransportType.train,
        name: 'Train',
        title: 'Train ticket information',
        ticketNoTitle: 'Train No.',
        fromLocationTitle: 'Departure station',
        fromDateTitle: 'Departure date',
        toLocationTitle: 'Arrival station',
        toDateTitle: 'Arrival date',
        icon: Assets.icons.icTrain.path),
    Transport(
        type: TransportType.drive,
        name: 'Drive',
        title: 'Drive ticket information',
        ticketNoTitle: 'Drive Info',
        fromLocationTitle: 'Starting at',
        fromDateTitle: 'Date',
        toLocationTitle: 'Destination',
        toDateTitle: 'Date',
        icon: Assets.icons.icDrive.path),
    Transport(
        type: TransportType.car,
        name: 'Car',
        title: 'Car ticket information',
        ticketNoTitle: 'Car Info',
        fromLocationTitle: 'Leave from',
        fromDateTitle: 'Date',
        toLocationTitle: 'Destination',
        toDateTitle: 'Date',
        icon: Assets.icons.icCar.path),
    Transport(
        type: TransportType.bus,
        name: 'Bus',
        title: 'Bus ticket information',
        ticketNoTitle: 'Bus Info',
        fromLocationTitle: 'Departure terminal',
        fromDateTitle: 'Departure date',
        toLocationTitle: 'Arrival terminal',
        toDateTitle: 'Arrival date',
        icon: Assets.icons.icBus.path),
    Transport(
        type: TransportType.ferry,
        name: 'Ferry',
        title: 'Ferry ticket information',
        ticketNoTitle: 'Ferry Info',
        fromLocationTitle: 'Departure port',
        fromDateTitle: 'Departure date',
        toLocationTitle: 'Arrival port',
        toDateTitle: 'Arrival date',
        icon: Assets.icons.icFerry.path),
  ];

  TripTransferUpsertState({
    required this.activity,
    this.dayIndex = 0,
    this.status = TripTransferUpsertStatus.initial,
    this.selectedTransport,
    this.ticketNo,
    this.departureDate,
    this.departureTime,
    this.departureLocation,
    this.arrivalDate,
    this.arrivalTime,
    this.arrivalLocation,
    this.selectedCurrency = 'USD',
    this.quantity = 1,
    this.price = 0.0,
    this.note,
    this.uploadedFiles = const [],
    this.isTicketNoValid = true,
    this.isDepartureLocationValid = true,
    this.isDepartureDateValid = true,
    this.isDepartureTimeValid = true,
    this.isArrivalLocationValid = true,
    this.isArrivalDateValid = true,
    this.isArrivalTimeValid = true,
  });

  @override
  List<Object?> get props => [
        activity,
        dayIndex,
        status,
        selectedTransport,
        ticketNo,
        departureDate,
        departureTime,
        departureLocation,
        arrivalDate,
        arrivalTime,
        arrivalLocation,
        selectedCurrency,
        quantity,
        price,
        note,
        uploadedFiles,
        isTicketNoValid,
        isDepartureLocationValid,
        isDepartureDateValid,
        isDepartureTimeValid,
        isArrivalLocationValid,
        isArrivalDateValid,
        isArrivalTimeValid,
      ];

  TripTransferUpsertState copyWith({
    ActivityModel? activity,
    int? dayIndex,
    TripTransferUpsertStatus? status,
    Transport? selectedTransport,
    String? ticketNo,
    DateTime? departureDate,
    TimeOfDay? departureTime,
    String? departureLocation,
    DateTime? arrivalDate,
    TimeOfDay? arrivalTime,
    String? arrivalLocation,
    String? selectedCurrency,
    int? quantity,
    double? price,
    String? note,
    List<File>? uploadedFiles,
    bool? isTicketNoValid,
    bool? isDepartureLocationValid,
    bool? isDepartureDateValid,
    bool? isDepartureTimeValid,
    bool? isArrivalLocationValid,
    bool? isArrivalDateValid,
    bool? isArrivalTimeValid,
  }) {
    return TripTransferUpsertState(
      activity: activity ?? this.activity,
      dayIndex: dayIndex ?? this.dayIndex,
      status: status ?? this.status,
      selectedTransport: selectedTransport ?? this.selectedTransport,
      ticketNo: ticketNo ?? this.ticketNo,
      departureDate: departureDate ?? this.departureDate,
      departureTime: departureTime ?? this.departureTime,
      departureLocation: departureLocation ?? this.departureLocation,
      arrivalDate: arrivalDate ?? this.arrivalDate,
      arrivalTime: arrivalTime ?? this.arrivalTime,
      arrivalLocation: arrivalLocation ?? this.arrivalLocation,
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      note: note ?? this.note,
      uploadedFiles: uploadedFiles ?? this.uploadedFiles,
      isTicketNoValid: isTicketNoValid ?? this.isTicketNoValid,
      isDepartureLocationValid: isDepartureLocationValid ?? this.isDepartureLocationValid,
      isDepartureDateValid: isDepartureDateValid ?? this.isDepartureDateValid,
      isDepartureTimeValid: isDepartureTimeValid ?? this.isDepartureTimeValid,
      isArrivalLocationValid: isArrivalLocationValid ?? this.isArrivalLocationValid,
      isArrivalDateValid: isArrivalDateValid ?? this.isArrivalDateValid,
      isArrivalTimeValid: isArrivalTimeValid ?? this.isArrivalTimeValid,
    );
  }
}
