import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/screen/family_create/family_create_state.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/image/image_picker_handler.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/utils/upload.dart';

class FamilyProfileHandler {
  final TextFieldHandler familyName;
  final TextFieldHandler description;
  String? photoUrl;

  FamilyProfileHandler({
    required this.familyName,
    required this.description,
    this.photoUrl,
  });

  Future<void> initialize({String? familyId, required IFamilyRepository familyRepository}) async {
    if (familyId != null) {
      final family = await familyRepository.getProfileById(familyId);
      familyName.text = family.familyName ?? '';
      description.text = family.description ?? '';
      photoUrl = family.photoUrl;
    }
  }
}

class FamilyCreateCubit extends BaseCubit<FamilyCreateState> {
  final IFamilyRepository familyRepository;
  final AccountService accountService = locator.get();

  late final FamilyProfileHandler profileHandler;
  late final FormTextFieldHandler formHandler;

  final String? familyId;

  FamilyCreateCubit({
    required this.familyRepository,
    this.familyId,
  }) : super(FamilyCreateState());

  @override
  void onInit() {
    super.onInit();
    _initializeHandlers();
  }

  Future<void> _initializeHandlers() async {
    profileHandler = FamilyProfileHandler(
      familyName: TextFieldHandler(
        field: 'family_name',
        title: LocaleKeys.family_name_text.tr(),
        inputType: TextInputType.name,
        errorText: (value) => LocaleKeys.family_name_invalid_text.tr(),
        isRequired: true,
      ),
      description: TextFieldHandler(
        field: 'description',
        title: LocaleKeys.description.tr(),
        inputType: TextInputType.text,
        errorText: (value) => LocaleKeys.description_invalid.tr(),
        isRequired: false,
      ),
    );

    formHandler = FormTextFieldHandler(
      handlers: [profileHandler.familyName, profileHandler.description],
      validateForm: onSubmit,
    );

    await profileHandler.initialize(familyId: familyId, familyRepository: familyRepository);
    emit(state.copyWith(photoUrl: profileHandler.photoUrl));
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    if (state.imageFile == null && familyId == null) {
      showSimpleToast(LocaleKeys.please_select_an_image_text.tr());
      return;
    }

    try {
      emit(state.copyWith(status: FamilyCreateStatus.loading));

      String? newFamilyId = familyId;

      if (familyId == null) {
        final account = await familyRepository.createProfile(
          familyName: profileHandler.familyName.text,
          description: profileHandler.description.text,
        );
        newFamilyId = account.uuid;
      } else {
        await familyRepository.updateProfileById(
          familyId!,
          familyName: profileHandler.familyName.text,
          description: profileHandler.description.text,
        );
      }

      if (state.imageFile != null && newFamilyId != null) {
        final file = state.imageFile!;
        final storageModel = await uploadImage(file, newFamilyId);
        if (storageModel.uuid?.isNotEmpty == true) {
          await familyRepository.updateAvatarS3(
            newFamilyId,
            fileUuid: storageModel.uuid!,
          );
        }
      }

      emit(state.copyWith(status: FamilyCreateStatus.success));
    } catch (e) {
      emit(state.copyWith(
        status: FamilyCreateStatus.error,
        errorMessage: LocaleKeys.family_creation_failed.tr(),
      ));
    }
  }

  Future<void> onUpdateAvatar() async {
    final pickedFile = await ImagePickerHandler.onGetImage();
    if (pickedFile != null) {
      emit(state.copyWith(imageFile: pickedFile));
    }
  }

  Future<StorageModel> uploadImage(File imageFile, String familyId) async {
    try {
      final upload = Upload(
        familyId: familyId,
      );
      final storageModel = await upload.uploadImage(imageFile, null);
      if (storageModel.uuid != null) {
        return storageModel;
      }
    } catch (e) {
      AppLogger.d('Image upload error: $e');
    }
    return const StorageModel();
  }

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }
}
