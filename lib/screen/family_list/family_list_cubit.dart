import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/screen/family_list/family_list_state.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';

class FamilyListCubit extends BaseCubit<FamilyListState> {
  final AccountService accountService;
  final HomeCubit mainCubit;
  final IFamilyRepository familyRepository;

  FamilyListCubit({
    required this.accountService,
    required this.familyRepository,
    required this.mainCubit,
  }) : super(FamilyListState());

  @override
  void onInit() {
    locator.registerSingleton(this);
    super.onInit();
    refresh();
    _checkFamilyList();
  }

  @override
  Future<void> close() {
    locator.unregister<FamilyListCubit>();
    return super.close();
  }


  refresh(){
    accountService.getMyFamilyBelong();
  }


  void _checkFamilyList() {
    final families = accountService.myFamilyBelong.value;
    if (families.isEmpty) {
      emit(state.copyWith(shouldForceCreateFamily: true));
    }
  }

  Future<void> deActiveCurrentFamily(String familyUuid) async {
    if (familyUuid.isEmpty) return;
    showLoading();
    try {
      await familyRepository.deactiveFamily(familyUuid);
      await accountService.initMyProfile();
      await mainCubit.onRefresh();
    } catch (e) {
      print(e);
    }
    dismissLoading();
  }

  Future<void> activeFamily(String familyUuid) async {
    logd("Activate family: $familyUuid");

    if (familyUuid.isEmpty) return;
    showLoading();
    try {
      await familyRepository.activeFamily(familyUuid);
      // await accountService.onChangeActiveFamily(familyUuid);
      await accountService.initMyProfile();
      await mainCubit.onRefresh();
    } catch (e) {
      print(e);
    }
    dismissLoading();
  }
}
