import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/account.dart';

class MemberListState extends BaseState {
  final List<Account> members;
  String url_invite = '';

  MemberListState({
    this.members = const [],
    this.url_invite = '',
  });

  MemberListState copyWith({
    List<Account>? members,
    String? url_invite,
  }) {
    return MemberListState(
      members: members ?? this.members,
      url_invite: url_invite ?? this.url_invite
    );
  }

  @override
  List<Object?> get props => [members, url_invite];
}
