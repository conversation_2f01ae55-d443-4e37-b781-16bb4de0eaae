import 'dart:async';
import 'dart:developer';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/family_profile.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/screen/family_list/family_list_cubit.dart';
import 'package:family_app/screen/family_list/member_list/member_list_parameter.dart';
import 'package:family_app/screen/family_list/member_list/member_list_state.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

import '../../../data/repository/family/model/invite_parameter.dart';

class MemberListCubit extends BaseCubit<MemberListState> {
  final AccountService accountService;
  final IFamilyRepository familyRepository;
  final MemberListParameter parameter;
  FamilyProfile? family;

  MemberListCubit({
    required this.accountService,
    required this.familyRepository,
    required this.parameter,
    required this.family,
  }) : super(MemberListState());

  late final StreamSubscription<List<Account>>? _memberSub;

  @override
  Future<void> close() {
    _memberSub?.cancel();
    locator.unregister<MemberListCubit>();
    return super.close();
  }

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();
    _memberSub = accountService.userRole == Role.owner
        ? accountService.memberInFamily.stream.listen((member) {
            if (isClose) return;
            emit(state.copyWith(members: member));
          })
        : null;
    refresh();
  }

  refresh() async {
    showLoading();
    final familyId = parameter.familyId;
    try {
      family = await familyRepository.getProfileById(familyId);
      final members = await familyRepository.getUserInFamily(familyId);

      emit(state.copyWith(members: members));
    } catch (e) {
      log(e.toString());
    }
    dismissLoading();
  }

  Future<void> onUpdateRoleOfMember(
      int index, Account member, String role) async {
    showLoading();
    try {
      final updatedMember =
          await familyRepository.updateMember(member.uuid ?? '', role: role);
      final updatedMembers = List<Account>.from(state.members)
        ..[index] = updatedMember;
      emit(state.copyWith(members: updatedMembers));
    } catch (e) {
      showSimpleToast(LocaleKeys.action_fail.tr());
    } finally {
      dismissLoading();
    }
  }

  void onInviteSuccess(Account member) {
    final members = [...state.members];
    members.add(member);
    emit(state.copyWith(members: members));
  }

  Future<void> getUrlInvite() async {
    try {
      final result = await familyRepository
          .invite(InviteParameter(familyId: parameter.familyId));
      if (result != null) {
        Share.share('${result.invite_url}');
        print("getUrlInvite ${result.invite_url}");
        // showSimpleToast(LocaleKeys.action_fail.tr());
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> onDeleteMember(BuildContext context, Account member) async {
    await DialogUtils.showDeleteDialogV2(
      context,
      content: LocaleKeys.confirm_delete_member
          .tr(namedArgs: {'field': '${member.fullName}'}),
      confirmText: LocaleKeys.remove.tr(),
      onConfirm: () async {
        final result = await familyRepository.kickMember(member.uuid ?? '');
        if (result) {
          refresh();
          FamilyListCubit familyListCubit = locator.get();
          familyListCubit.refresh();
          context.maybePop();
          showSimpleToast(LocaleKeys.remove_member_success.tr());
        } else {
          showSimpleToast(LocaleKeys.action_fail.tr());
        }
      },
    );
  }
}
