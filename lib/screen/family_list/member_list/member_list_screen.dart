import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/family_profile.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_list/member_detail/member_detail_parameter.dart';
import 'package:family_app/screen/family_list/member_list/member_list_cubit.dart';
import 'package:family_app/screen/family_list/member_list/member_list_parameter.dart';
import 'package:family_app/screen/family_list/member_list/member_list_state.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/line_widget.dart';
import 'package:family_app/widget/member_info_row_widget.dart';
import 'package:family_app/widget/popup/popup.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../utils/assets/shadow_util.dart';
import '../../../widget/appbar_custom.dart';
import '../../../widget/image_asset_custom.dart';

@RoutePage()
class MemberListPage extends BaseBlocProvider<MemberListState, MemberListCubit> {
  const MemberListPage({required this.parameter, super.key, this.family});

  final FamilyProfile? family;
  final MemberListParameter parameter;

  @override
  Widget buildPage() => MemberListView(family: family, parameter: parameter);

  @override
  MemberListCubit createCubit() => MemberListCubit(
    accountService: locator.get(),
    familyRepository: locator.get(),
    parameter: parameter,
    family: family,
  );
}

class MemberListView extends StatefulWidget {
  const MemberListView({ super.key, this.family, required this.parameter });

  final FamilyProfile? family;
  final MemberListParameter parameter;

  @override
  State<MemberListView> createState() => _MemberListViewState();
}

class _MemberListViewState extends BaseBlocPageState<MemberListView, MemberListState, MemberListCubit> {

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  Color? get backgroundAppBarColor => appTheme.background;

  @override
  void onTapScreen(BuildContext context) {
    super.onTapScreen(context);
  }

  Widget buildAppBar(BuildContext context, MemberListCubit cubit, MemberListState state) {
    final memberCount = cubit.family?.members ?? state.members.length;

    return CustomAppBar2(
      titleView: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            LocaleKeys.family_name_with_suffix.tr(namedArgs: {
              'name': cubit.family?.familyName ?? '',
            }),
            style: AppStyle.medium16(color: appTheme.blackText),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            LocaleKeys.members_text.tr(namedArgs: {'field': memberCount.toString()}),
            style: AppStyle.regular12(color: appTheme.blackTextV2),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      showBack: true,
      actions: [
        if (_isFamilyOwner(cubit))
          GestureDetector(
            onTap: () {
              context.pushRoute(FamilyCreateRoute(
                familyId: widget.parameter.familyId,
              ));
            },
            behavior: HitTestBehavior.opaque,
            child: CircleItem(
              backgroundColor: appTheme.backgroundV2,
              padding: padding(all: 7),
              child: SvgPicture.asset(Assets.icons.icEdit.path),
            ),
          )
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, MemberListCubit cubit, MemberListState state) {
    return Padding(
      padding: padding(top: 0),
      child: SingleChildScrollView(
        padding: EdgeInsets.only(bottom: context.bottom),
        child: Column(
          children: [
            if (_isFamilyOwner(cubit))
              Padding(
                padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
                child: _buildInviteRow(context, cubit),
              ),
            Padding(
              padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
              child: _boxBuilder(
                state.members.asMap().entries.map((entry) {
                  final index = entry.key;
                  final member = entry.value;

                  return InkWell(
                    onTap: () {
                      context.pushRoute(MemberDetailRoute(
                        parameter: MemberDetailParameter(
                          isFamilyOwner: _isFamilyOwner(cubit),
                          member: member,
                        )
                      ));
                    },
                    child: Column(
                      children: [
                        _buildMemberCard(cubit, state, member),
                        if (index < state.members.length - 1) _buildDivider(),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _isFamilyOwner(MemberListCubit cubit) {
    // Because only the owner can invite themselves
    return cubit.family?.userUuid == cubit.accountService.account?.uuid;
  }

  Widget _buildDivider() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Divider(
        height: 1,
        thickness: 1,
        color: appTheme.borderColor,
      ),
    );
  }

  Widget _buildMemberCard(MemberListCubit cubit, MemberListState state, Account member) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: UserProfileRow(
        name: member.fullName ?? '',
        text: member.familyMemberUuid == cubit.accountService.account?.uuid
            ? '${member.relationship ?? '-'} (${LocaleKeys.me.tr()})'
            : member.relationship ?? '-',
        imageUrl: member.photoUrl ?? '',
        widget: _isFamilyOwner(cubit)
            ? member.familyMemberUuid != cubit.accountService.account?.uuid
                ? _buildDeleteMember(cubit, state, member)
                : Text(member.role.toString(),
                    style: AppStyle.bold12V2(color: appTheme.primaryColorV2))
            : null,
      ),
    );
  }

  _buildDeleteMember(MemberListCubit cubit, MemberListState state, Account member) {
    return InkWell(
      onTap: () {
        cubit.onDeleteMember(context, member);
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 12, bottom: 12),
        child: Container(
          padding: padding(horizontal: 8, vertical: 4),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            border: Border.all(width: 1.w, color: appTheme.typeRedColor),
            color: appTheme.whiteText,
          ),
          child: Row(
            children: [
              Assets.icons.iconDelete.svg(width: 12.w, height: 12.w),
              SizedBox(width: 4.w),
              Text(
                LocaleKeys.remove.tr(),
                style: AppStyle.bold12V2(color: appTheme.typeRedColor),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRolePopup(MemberListCubit cubit, MemberListState state, Account member, String role) {
    return CustomPopup(
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: Role.roles
            .asMap()
            .entries
            .map((e) => _buildRoleView(
                  cubit: cubit,
                  member: member,
                  role: e.value,
                  isFinal: e.key == Role.roles.length - 1,
                ))
            .toList(),
      ),
      child: _buildUpdateRoleView(role.tr()),
    );
  }

  Widget _buildRoleText(String role) {
    return Text(
      role.tr(),
      style: AppStyle.regular14(color: appTheme.typeRedColor),
    );
  }

  Widget _buildRoleView({
    required MemberListCubit cubit,
    required Account member,
    required String role,
    required bool isFinal,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildRoleOption(cubit: cubit, member: member, role: role),
        if (!isFinal) _buildRoleDivider(),
      ],
    );
  }

  Widget _buildRoleDivider() {
    return Padding(
      padding: padding(vertical: 8),
      child: LineWidget(
        widget: 68.w,
        height: 1.h,
        color: appTheme.borderColor,
      ),
    );
  }

  Widget _buildRoleOption({
    required MemberListCubit cubit,
    required Account member,
    required String role,
  }) {
    return GestureDetector(
      onTap: () {
        context.maybePop();
        cubit.onUpdateRoleOfMember(
          cubit.state.members.indexOf(member),
          member,
          role,
        );
      },
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: padding(left: 4, top: 6),
        child: Text(
          role.tr(),
          style: AppStyle.regular14(
            color: role == member.role ? appTheme.typeRedColor : null,
          ),
        ),
      ),
    );
  }

  Widget _buildUpdateRoleView(String role) {
    return Container(
      padding: padding(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(1000),
        border: Border.all(width: 1.w, color: appTheme.hintColor),
        color: appTheme.whiteText,
      ),
      child: Row(
        children: [
          Text(role, style: AppStyle.medium12(color: appTheme.typeRedColor)),
          SizedBox(width: 4.w),
          Image.asset(Assets.images.downArrow.path, width: 8.w),
        ],
      ),
    );
  }

  Widget _boxBuilder(List<Widget> children) {
    return Container(
      padding: padding(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.w2),
        boxShadow: ShadowUtil.backgroundShadow,
      ),
      child: Column(children: children),
    );
  }

  Widget _buildInviteRow(BuildContext context, MemberListCubit cubit) {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: cubit.getUrlInvite,
            child: Container(
              padding: padding(all: 8.w2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(999),
                color: appTheme.whiteText,
                boxShadow: ShadowUtil.backgroundShadow,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ImageAssetCustom(
                    imagePath: Assets.icons.iconInvite.path,
                    width: 40,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    LocaleKeys.add_new_member.tr(),
                    style: AppStyle.regular14(color: appTheme.lightBotColor),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
