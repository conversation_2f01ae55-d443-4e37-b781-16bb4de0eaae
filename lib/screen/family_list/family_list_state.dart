import 'package:family_app/base/widget/cubit/base_state.dart';

class FamilyListState extends BaseState {
  final bool shouldForceCreateFamily;

  FamilyListState({
    this.shouldForceCreateFamily = false,
  });

  FamilyListState copyWith({
    bool? shouldForceCreateFamily,
  }) {
    return FamilyListState(
      shouldForceCreateFamily: shouldForceCreateFamily ?? this.shouldForceCreateFamily,
    );
  }

  @override
  List<Object?> get props => [shouldForceCreateFamily];
}
