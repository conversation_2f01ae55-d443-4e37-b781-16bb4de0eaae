import 'package:family_app/data/model/account.dart';

class MemberDetailParameter {
  final bool isFamilyOwner;
  final Account member;

  MemberDetailParameter({
    required this.isFamilyOwner,
    required this.member,
  });

  MemberDetailParameter copyWith({
    bool? isOwner,
    Account? member,
  }) {
    return MemberDetailParameter(
      isFamilyOwner: isOwner ?? this.isFamilyOwner,
      member: member ?? this.member,
    );
  }

  @override
  String toString() {
    return 'MemberDetailParameter{isFamilyOwner: $isFamilyOwner, memberId: ${member.uuid}}';
  }
}
