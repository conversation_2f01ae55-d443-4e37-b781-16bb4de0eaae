import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/screen/family_list/member_list/member_list_cubit.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';

import 'member_detail_parameter.dart';
import 'member_detail_state.dart';

class MemberDetailCubit extends BaseCubit<MemberDetailState> {
  final tag = "MemberDetailCubit";
  final AccountService accountService;
  final IFamilyRepository familyRepository;
  final MemberListCubit memberListCubit;
  final MemberDetailParameter parameter;

  MemberDetailCubit({
    required this.accountService,
    required this.familyRepository,
    required this.memberListCubit,
    required this.parameter,
  }) : super(MemberDetailState(member: parameter.member));

  late TextFieldHandler relationship;
  late FormTextFieldHandler form;

  @override
  Future<void> close() {
    locator.unregister<MemberDetailCubit>();
    return super.close();
  }

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();
    logd("$tag - onInit familyUuid  ${parameter.member.familyMemberUuid} == ${accountService.account?.familyUuid}");
    emit(state.copyWith(
      isFamilyOwner: parameter.isFamilyOwner,
      isEditor: parameter.member.role == Role.editor,
    ));
    relationship = TextFieldHandler(
      field: 'relationship',
      hintText: LocaleKeys.relationship.tr(),
      initializeText: parameter.member.relationship ?? '',
      errorText: (value) => LocaleKeys.relationship_invalid_text.tr(),
      isFieldValid: (value) => value.isNotEmpty,
      isRequired: true,
      onListenText: () => onChangedRelationship(relationship.text),
    );
    form = FormTextFieldHandler(handlers: [relationship], validateForm: onValidateForm);
    // showLoading();
  }

  void onChangedRelationship(String text) {
    emit(state.copyWith(isChanging: true));
  }

  Future<void> onValidateForm(Map<String, dynamic> map) async {
    emit(state.copyWith(status: MemberDetailStatus.loading));
    try {
      final currentRole = parameter.member.role;
      final newRole = state.isEditor ? Role.editor : Role.viewer;
      // Omit role if current role is owner
      final shouldUpdateRole = currentRole != Role.owner;
      final updatedMember = await familyRepository.updateMember(
        parameter.member.uuid ?? '',
        relationship: map['relationship'],
        role: shouldUpdateRole ? newRole : null,
      );
      emit(state.copyWith(status: MemberDetailStatus.success, member: updatedMember));
      memberListCubit.refresh();
    } catch (e) {
      logd("Error updating member relationship: $e");
      emit(state.copyWith(status: MemberDetailStatus.error));
    }
  }

  void handelChangeEditor(bool value) {
    emit(state.copyWith(isEditor: value, isChanging: true));
  }
}
