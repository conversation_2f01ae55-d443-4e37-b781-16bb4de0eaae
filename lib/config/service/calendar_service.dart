import 'dart:developer';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:device_calendar/device_calendar.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/device_event.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'app_service.dart';

@singleton
class CalendarService {
  final DeviceCalendarPlugin _deviceCalendarPlugin = DeviceCalendarPlugin();


  static CalendarService instance = locator<CalendarService>();

  bool permissionGranted = false;
  Calendar? defaultCalendar;
  List<Calendar> calendars = [];


  CalendarService() {
    _logd("CalendarService initialized");
    _init();

  }

  _init() async {
    var permissionsGranted = await _deviceCalendarPlugin.hasPermissions();
    _logd("Permissions granted: ${permissionsGranted.isSuccess} ${permissionsGranted.data}");
    if (permissionsGranted.isSuccess && permissionsGranted.data != null && permissionsGranted.data == true) {
      permissionGranted = true;
    }
  }

  Calendar? getCalendar(String id){
    for(var c in calendars){
      if(c.id == id) return c;
    }
    return null;
  }

  retrieveCalendars() async {
    _logd("Retrieving calendars...");
    try {
      var permissionsGranted = await _deviceCalendarPlugin.hasPermissions();
      if (permissionsGranted.isSuccess && (permissionsGranted.data == null || permissionsGranted.data == false)) {
        permissionsGranted = await _deviceCalendarPlugin.requestPermissions();
        if (!permissionsGranted.isSuccess || permissionsGranted.data == null || permissionsGranted.data == false) {
          return;
        }
      }
      permissionGranted = true;
      final calendarsResult = await _deviceCalendarPlugin.retrieveCalendars();
      calendars = calendarsResult.data as List<Calendar>;
      _logd("Calendars retrieved: ${calendars.length}");
      for (var calendar in calendars) {
        _logd("Calendar: ${calendar.toJson()}");
        if(Platform.isAndroid){
          if(calendar.accountType == "com.google" && calendar.isReadOnly == false){
            _logd("Google calendar: ${calendar.toJson()}");
            defaultCalendar = calendar;
            break;
          }
        }else{
          if (calendar.isDefault == true && calendar.isReadOnly == false) {
            _logd("Default calendar: ${calendar.toJson()}");
            defaultCalendar = calendar;
            break;
          }
        }

      }
      if (defaultCalendar == null) {
        _logd("Default calendar is null");
        defaultCalendar = calendars.firstWhereOrNull((e) => e.isReadOnly == false);
      }
      // if (defaultCalendar == null) {
      //   _logd("Creating calendar: $calendarName");
      //   var newCale = await _deviceCalendarPlugin.createCalendar(calendarName, calendarColor: appTheme.primaryColorV2);
      //
      //   defaultCalendar = Calendar(
      //     id: newCale.data ?? "",
      //     name: calendarName,
      //   );
      //
      //   _logd("Creating calendar newCale: ${newCale.data}");
      //   if (newCale.isSuccess && newCale.data != null) {
      //     _logd("Default calendar created: ${newCale.data}");
      //   } else {
      //     _logd("Error creating calendar: ${newCale.errors}");
      //   }
      // }
    } on PlatformException catch (e) {
      _logd("Error retrieving calendars: $e");
    }
  }

  bool isEnableCalendarSync() {
    _logd("Enabling calendar sync");
    LocalStorage localStorage = locator.get();
    var enableCalendarSync = localStorage.isSyncWithDeviceCalendar;
    _logd("Enable calendar sync: $enableCalendarSync");
    return enableCalendarSync;
  }

  Future<void> setEnableCalendarSync(bool enable) async {
    _logd("Setting calendar sync to $enable");
    LocalStorage localStorage = locator.get();
    await localStorage.cacheSyncWithDeviceCalendar(enable);
  }


  bool isStartOfWeekMonday() {
    final startOfWeekMonday = locator.get<LocalStorage>().isStartOfWeekMonday;
    return startOfWeekMonday;
  }

  Future<void> setStartOfWeekMonday(bool enable) async {
    _logd("Setting start of week Monday to $enable");
    final localStorage = locator.get<LocalStorage>();
    await localStorage.cacheStartOfWeekMonday(enable);
  }





  Future<List<DeviceEventModel>> retrieveEvents(DateTime from, DateTime to) async {
    if (!permissionGranted || defaultCalendar == null) {
      _logd("retrieveEvents Permission not granted");
      return [];
    }

    bool enableSync = isEnableCalendarSync();
    if(!enableSync) {
      _logd("retrieveEvents Calendar sync is disabled");
      return [];
    }


    try {
      var calendarId = defaultCalendar?.id ?? "";
      _logd("retrieveEvents: Calendar ID: $calendarId Name: ${defaultCalendar?.name} From: $from To: $to");
      var currentEvents =
          await _deviceCalendarPlugin.retrieveEvents(calendarId, RetrieveEventsParams(startDate: from, endDate: to));
      _logd("Current events: ${currentEvents.data?.length}");
      return currentEvents.data?.map((e) {
            String? notificationTime;
            if (e.reminders != null && e.reminders!.isNotEmpty) {
              notificationTime = e.start?.subtract(Duration(minutes: e.reminders?.first.minutes ?? 0))
                  .toIso8601String();
            }

            return DeviceEventModel(
              deviceEventId: e.eventId ?? "",
              event: e,
              calendarId: calendarId,
              name: e.title,
              description: e.description,
              fromDate: e.start?.toIso8601String(),
              toDate: e.end?.toIso8601String(),
              notificationTime: notificationTime,
            );
          }).toList() ??
          [];
    } catch (e) {
      _logd("Error retrieving events: $e");
    }
    return [];
  }

  // syncEvent(DateTime start, DateTime end, List<EventModels> events) async {
  //   try {
  //     if (!permissionGranted) {
  //       _logd("Permission not granted");
  //       return;
  //     }
  //     if (defaultCalendar == null) {
  //       _logd("Default calendar is null");
  //       return;
  //     }
  //
  //     // String calendarId = calendars.first.id ?? "";
  //     String calendarId = defaultCalendar?.id ?? "";
  //
  //     var currentEvents =
  //         await _deviceCalendarPlugin.retrieveEvents(calendarId, RetrieveEventsParams(startDate: start, endDate: end));
  //
  //     for (var model in events) {
  //       _logd("syncEvent start: ${model.toJson()}");
  //       final startDate = model.fromDate?.toLocalDT;
  //       final endDate = model.toDate?.toLocalDT;
  //       if (startDate == null || endDate == null || model.name == null || model.uuid == null) {
  //         _logd("Start date or end date is null");
  //         continue;
  //       }
  //
  //       if (startDate.millisecondsSinceEpoch == endDate.millisecondsSinceEpoch) {
  //         _logd("Start date and end date are the same");
  //         continue;
  //       }
  //
  //       //find this event in currentEvents
  //       //lin: [${model.uuid}] - ${model.description}
  //       final deviceCalendar = currentEvents.data?.firstWhereOrNull((e) {
  //         if (e.description != null && e.description!.contains("[${model.uuid}]")) {
  //           return true;
  //         }
  //         return false;
  //       });
  //
  //       final event = Event(
  //         calendarId,
  //         eventId: deviceCalendar?.eventId,
  //         title: model.name,
  //         description: "[${model.uuid}]\n${model.description}",
  //         start: TZDateTime.local(startDate.year, startDate.month, startDate.day, startDate.hour, startDate.minute),
  //         end: TZDateTime.local(endDate.year, endDate.month, endDate.day, endDate.hour, endDate.minute),
  //       );
  //
  //       if (model.notificationTime != null && model.notificationTime!.isNotEmpty) {
  //         final dateTime = model.notificationTime!.toLocalDT;
  //         final fromDate = startDate;
  //         final duration = fromDate.difference(dateTime);
  //         if (duration.inMinutes > 0) {
  //           event.reminders = [
  //             Reminder(
  //               minutes: duration.inMinutes,
  //             ),
  //           ];
  //         }
  //       }
  //       _logd(
  //           "syncEvent: ${defaultCalendar?.id} => ${model.uuid} # ${model.name} # ${model.fromDate} # ${model.toDate}");
  //       var result = await _deviceCalendarPlugin.createOrUpdateEvent(event);
  //       if (result?.isSuccess == true) {
  //         _logd("syncEvent successfully: ${result?.data}");
  //       } else {
  //         for (var error in result?.errors ?? []) {
  //           _logd("syncEvent Error syncing event: ${error.errorCode} ${error.errorMessage}");
  //         }
  //       }
  //     }
  //   } catch (e) {
  //     _logd("Error syncing event: $e");
  //   }
  // }

  _logd(String message) {
    log("CalendarService: $message");
  }
}
