//handle account data in here

import 'package:dartx/dartx.dart';
import 'package:family_app/base/stream/base_list_stream_controller.dart';
import 'package:family_app/base/stream/base_stream_controller.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/family.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/widgets.dart' show ValueNotifier;
import 'package:injectable/injectable.dart';

@singleton
class AccountService {
  final IFamilyRepository familyRepository;
  final LocalStorage localStorage;

  AccountService({required this.familyRepository, required this.localStorage});

  late final _myAccount = BaseStreamController<Family?>(null)..stream.listen(_listener);
  final myFamilyBelong = BaseListStreamController<Family>([]);
  final myActiveFamily = BaseStreamController<Family?>(null);
  final memberInFamily = BaseListStreamController<Account>([]);
  final _isLogged = ValueNotifier(false);

  Family? get account => _myAccount.value;
  bool get isLogged => _isLogged.value;

  Future<void> initMyProfile() async {
    final result = await familyRepository.getMyProfile();

    // logd("myProfile: $result");

    setAccount(result);
    await getMyFamilyBelong();
  }

  void setAccount(Family? account) {
    _myAccount.value = account;
    myActiveFamily.value = account;
  }

  @disposeMethod
  void dispose() {
    myFamilyBelong.dispose();
    _myAccount.dispose();
    myActiveFamily.dispose();
    memberInFamily.dispose();
    _isLogged.dispose();
  }

  Future<void> getMyFamilyBelong() async {
    final result = await familyRepository.getFamilyBelongTo();

    logd("member of the following families: \n$result");

    final hasAccountFamily = account != null && (account?.familyUuid ?? '').isNotEmpty;

    logd("account active family: ${account?.activeFamily}");
    myFamilyBelong.value = result; //[...result, if (hasAccountFamily) account!];
    final activeFamilyUuid = account?.activeFamily ?? '';
    if (activeFamilyUuid.isEmpty && hasAccountFamily) {
      myActiveFamily.value = account;
    } else {
      final activeFamily = myFamilyBelong.value.firstOrNullWhere((element) => element.familyUuid == activeFamilyUuid);

      logd("found active family info : $activeFamily");

      if (activeFamily != null) {
        activeFamily.activeFamilyName = activeFamily.familyName;

        myActiveFamily.value = activeFamily;
        account?.role = activeFamily.role;
      } else {
        //set active family to default family
        myActiveFamily.value = account;
      }
    }
    memberInFamily.value = await familyRepository.getUserInFamily(familyId);

    logd("Member in family is: ${memberInFamily.value}");
  }

  Future<void> onChangeActiveFamily(String newFamilyUuid) async {
    setAccount(account?.copyWith(activeFamily: newFamilyUuid));
    myActiveFamily.value = myFamilyBelong.value.firstOrNullWhere((element) => element.uuid == newFamilyUuid) ?? account;
    memberInFamily.value = await familyRepository.getUserInFamily(familyId);
  }

  String get familyId {
    if (account?.activeFamily != null && account?.activeFamily?.isNotEmpty == true) {
      return account!.activeFamily.toString();
    } else {
      return account?.uuid ?? '';
    }
  }

  bool isFamilyOwner(String familyId) {
    return account?.uuid == familyId;
  }

  bool isActiveFamily(String familyId) {
    return account?.activeFamily == familyId;
  }

  String get userRole {
    if (isFamilyOwner(familyId)) {
      return Role.owner;
    } else {
      return account?.role ?? Role.viewer;
    }
  }

  void logout() {
    setAccount(null);
  }

  void _listener(Family? v) {
    _isLogged.value = v != null;
  }

  void addListenerLogged(void Function(bool) callback) {
    _isLogged.addListener(() {
      callback(_isLogged.value);
    });
  }
}
