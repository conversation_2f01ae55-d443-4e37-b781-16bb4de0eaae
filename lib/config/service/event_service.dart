import 'package:family_app/base/stream/base_stream_controller.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

@injectable
class EventService {
  final IEventRepository eventRepository;

  EventService({required this.eventRepository});

  final events = BaseStreamController<List<EventModels>>(<EventModels>[]);
}
