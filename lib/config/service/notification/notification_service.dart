import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:auto_route/auto_route.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/notification/background_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_list/member_list/member_list_parameter.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_parameter.dart';
import 'package:family_app/screen/main/event/detail_event/detail_event_parameter.dart';
import 'package:family_app/screen/main/home/<USER>/detail_activity_parameter.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:injectable/injectable.dart';

@singleton
class NotificationService {
  final LocalStorage localStorage;
  final AccountService accountService;

  NotificationService({
    required this.localStorage,
    required this.accountService,
  });

  late final StreamSubscription<RemoteMessage> _onMessageSub;
  late final StreamSubscription<RemoteMessage> _onMessageOpenAppSub;
  late final StreamSubscription<String> _onTokenRefresh;

  final _messaging = FirebaseMessaging.instance;
  final _plugin = FlutterLocalNotificationsPlugin();

  @disposeMethod
  void onClose() {
    _onTokenRefresh.cancel();
    _onMessageSub.cancel();
    _onMessageOpenAppSub.cancel();
  }

  Future<bool?> onRequestPermission() async {
    try {
      if (Platform.isAndroid) {
        return _plugin
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()!
            .requestNotificationsPermission();
      } else {
        return _plugin
            .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
            ?.requestPermissions(alert: true, badge: true, sound: true);
      }
    } catch (e) {
      return false;
    }
  }

  @PostConstruct(preResolve: true)
  Future<void> onInit() async {
    _plugin.initialize(
      const InitializationSettings(
          android: AndroidInitializationSettings('@drawable/ic_notification'),
          iOS: DarwinInitializationSettings(
            requestAlertPermission: true,
            requestSoundPermission: true,
            requestBadgePermission: true,
            // onDidReceiveLocalNotification: (id, title, body, payload) {
            //   _plugin.cancel(id);
            //   onHandleNotification(jsonDecode(payload ?? '{}'));
            // }
          )),
      onDidReceiveBackgroundNotificationResponse: onNotificationTapBackground,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
    );

    _onMessageOpenAppSub = FirebaseMessaging.onMessageOpenedApp.listen((message) {
      onHandleNotification(message.data);
    });

    _onMessageSub = FirebaseMessaging.onMessage.listen((message) {
      if (message.notification != null) {
        showNotification(message);
        IsolateNameServer.lookupPortByName(AppConfig.notificationRoleReceivePort)?.send(message.toMap());
      }
    });

    _onTokenRefresh = _messaging.onTokenRefresh.listen((token) async {
      final accessToken = await localStorage.accessToken();
      if (accessToken != null && accountService.account != null) {
        // final newAcc = accountService.account!.copyWith(lastToken: token);
        // await updateProfileUsecase.call(newAcc.toJson());
      }
    });
  }

  Future<void> onHandleInitialMessage() async {
    final lastMessage = await _messaging.getInitialMessage();
    if (lastMessage != null) {
      onHandleNotification(lastMessage.data);
      return;
    }

    final lastNotification = await _plugin.getNotificationAppLaunchDetails();
    if (lastNotification != null &&
        lastNotification.didNotificationLaunchApp &&
        lastNotification.notificationResponse != null) {
      onDidReceiveNotificationResponse(lastNotification.notificationResponse!);
    }
  }

  Future<String?> getFcmToken() async {
    // if (Platform.isIOS) {
    //   return _messaging.getAPNSToken();
    // }
    try {
      return _messaging.getToken();
    } catch (e) {
      return null;
    }
  }

  void onDidReceiveNotificationResponse(NotificationResponse notificationResponse) {
    onHandleNotification((jsonDecode(notificationResponse.payload ?? '{}') as Map<String, dynamic>));
  }

  void showNotification(RemoteMessage message) {
    _plugin.show(
      0,
      message.notification?.title ?? '',
      message.notification?.body ?? '',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'channel id',
          'channel name',
          color: Color(0xFF434336),
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(presentSound: true),
      ),
      payload: jsonEncode(message.data),
    );
  }

  void onHandleNotification(Map<String, dynamic> payload) {
    print('onHandleNotification $payload');
    final type = payload['type'] as String? ?? '';
    final uuid = payload['uuid'] as String? ?? '';
    final familyId = payload['family_id'] as String? ?? '';
    print('payload $payload');
    if (type.isEmpty && uuid.isEmpty) return;
    switch (type) {
      case SocketEvent.LIST:
        navigatorKey.currentContext!.pushRoute(
          ListDetailRoute(parameter: ListDetailParameter(uuid: uuid)),
        );
        break;
      case SocketEvent.EVENT:
        navigatorKey.currentContext!.pushRoute(
          DetailEventRoute(parameter: DetailEventParameter(uuid: uuid)),
        );
        break;
      case SocketEvent.TRIP:
        navigatorKey.currentContext!.pushRoute(
          DetailActivityRoute(parameter: DetailActivityParameter(activityId: uuid)),
        );
      case SocketEvent.MEMBER:
        navigatorKey.currentContext!.pushRoute(
          MemberListRoute(
            parameter: MemberListParameter(
              familyId: familyId,
            ),
          ),
        );
        break;
    }
  }
}
