import 'dart:io' show Platform;

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';

const DAY_FORMAT = 'dd/MM/yyyy';
const DAY_MONTH_FORMAT = 'dd/MM';
const HOUR_FORMAT = 'HH:mm';
const FULL_DAY_FORMAT = 'EEEE, MMM d';

const LIMIT = 20;

RegExp spaceRegExp = RegExp(' +');

const REMIND_TIME = 15;

enum ListType { Shopping, Todo, Trip, Other }

extension ListTypeExtension on ListType {
  String typeStr() {
    switch (this) {
      case ListType.Shopping:
        return 'shopping';
      case ListType.Todo:
        return 'todo';
      case ListType.Trip:
        return 'trip';
      case ListType.Other:
        return 'other';
      default:
        return '';
    }
  }
}

enum StatusMember { pending, accepted, rejected }

abstract class Role {
  static const String editor = 'editor';
  static const String viewer = 'viewer';
  static const String owner = 'owner';

  static const List<String> roles = [editor, viewer];
}


abstract class TimeType {
  static const String am = 'AM';
  static const String pm = 'PM';
}

abstract class FamilyMemberRelationship {
  static var dad = LocaleKeys.dad.tr();
  static var mother = LocaleKeys.mom.tr();
  static var son = LocaleKeys.son.tr();
  static var daughter = LocaleKeys.daughter.tr();

  static var relationship = [dad, mother, son, daughter];
}

abstract class SocketEvent {
  static String MESSAGE = 'ai_chat_repond';
  static String SEND_MESSAGE = 'ai_chat';
  static String MESSAGE_DELETED = '';

  static const String TRIP = 'activity';
  static const String EVENT = 'event';
  static const String LIST = 'list';
  static const String MEMBER = 'member';

  static bool isAiMess(String command) {
    if (command == MESSAGE) {
      return true;
    }

    return false;
  }
}

abstract class MessageType {
  static const String TRIP = 'trip';
  static const String EVENT = 'event';
  static const String LIST = 'list';
  static const String MEMBER = 'member';
}
final premiums = Platform.isIOS
    ? {'com.gencare.family.premium.1month', 'com.gencare.family.premium.1year'}
    : Platform.isAndroid
        ? {'com.gencare.family.premium'}
        : <String>{};

const premiumBasePlans = {'com-gencare-family-premium-1month': 'month', 'com-gencare-family-premium-1year': 'year'};
abstract class ThreadMessageType {
  static const String TEXT = 'text';
  static const String POLL = 'poll';
}
