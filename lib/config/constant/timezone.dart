import 'package:family_app/data/model/timezone.dart';

const listTimezone = [
  Timezone('Greenwich Mean Time', 'GMT', 'UTC+0'),
  Timezone('Coordinated Universal Time', 'UTC', 'UTC+0'),
  Timezone('Western European Time', 'WET', 'UTC+0'),
  Timezone('Central European Time', 'CET', 'UTC+1'),
  Timezone('Eastern European Time', 'EET', 'UTC+2'),
  Timezone('Moscow Standard Time', 'MSK', 'UTC+3'),
  Timezone('Gulf Standard Time', 'GST', 'UTC+4'),
  Timezone('Pakistan Standard Time', 'PKT', 'UTC+5'),
  Timezone('India Standard Time', 'IST', 'UTC+5:30'),
  Timezone('Nepal Time', 'NPT', 'UTC+5:45'),
  Timezone('Bangladesh Standard Time', 'BST', 'UTC+6'),
  Timezone('Myanmar Time', 'MMT', 'UTC+6:30'),
  Timezone('Indochina Time', 'ICT', 'UTC+7'),
  Timezone('China Standard Time', 'CST', 'UTC+8'),
  Timezone('Japan Standard Time', 'JST', 'UTC+9'),
  Timezone('Australian Eastern Time', 'AET', 'UTC+10'),
  Timezone('New Zealand Standard Time', 'NZST', 'UTC+12'),
  Timezone('Atlantic Standard Time', 'AST', 'UTC-4'),
  Timezone('Eastern Standard Time', 'EST', 'UTC-5'),
  Timezone('Central Standard Time', 'CST', 'UTC-6'),
  Timezone('Mountain Standard Time', 'MST', 'UTC-7'),
  Timezone('Pacific Standard Time', 'PST', 'UTC-8'),
  Timezone('Alaska Standard Time', 'AKST', 'UTC-9'),
  Timezone('Hawaii-Aleutian Time', 'HAST', 'UTC-10'),
  Timezone('South Africa Standard Time', 'SAST', 'UTC+2'),
  Timezone('Iran Standard Time', 'IRST', 'UTC+3:30'),
  Timezone('Afghanistan Time', 'AFT', 'UTC+4:30'),
];

const List<int> setupReminderMinutes = [
  0,
  5,
  10,
  15,
  30,
  60,
  120,
];


