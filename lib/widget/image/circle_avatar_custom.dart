import 'dart:io';

import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/image/cache_image.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';

class CircleAvatarCustom extends StatelessWidget {
  const CircleAvatarCustom({
    super.key,
    this.imageUrl = '',
    this.size = 24,
    this.imageFile,
    this.borderWidth = 0,
    this.borderColor,
    this.defaultWidget,
    this.color,
    this.accountName,
  });

  final String imageUrl;
  final double size;
  final File? imageFile;
  final Color? borderColor;
  final double borderWidth;
  final Widget? defaultWidget;
  final Color? color;
  final String? accountName;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      // padding: padding(all: borderWidth),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: borderWidth > 0 ? Border.all(color: borderColor ?? appTheme.whiteText, width: borderWidth) : null,
        color: color,
      ),
      child: Padding(
        padding: padding(all: borderWidth),
        child: ClipOval(
          child: imageFile != null
              ? Image.file(
                  imageFile!,
                  width: size,
                  height: size,
                  fit: BoxFit.cover,
                  errorBuilder: (context, url, error) => _errorWidget(),
                )
              : imageUrl.isNotEmpty ? CacheImage(
                  imageUrl: imageUrl,
                  width: size,
                  height: size,
                  boxFit: BoxFit.cover,
                  defaultImage: _errorWidget(),
                ) : _errorWidget(),
        ),
      ),
    );
  }

  Widget _errorWidget() {
    if ((accountName ?? '').isNotEmpty) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: appTheme.primaryColor,
        ),
        child: Center(
          child: Text(
            (accountName?[0] ?? '').toUpperCase(),
            style: AppStyle.medium14(color: appTheme.whiteText),
          ),
        ),
      );
    }
    return defaultWidget ??
        (size > 40
            ? Container(
                padding: padding(all: 20),
                color: appTheme.grayE7Color,
                child: ImageAssetCustom(
                  imagePath: Assets.images.avatarLogo.path,
                  size: 20,
                  sizeBaseOnWidth: true,
                  boxFit: BoxFit.fill,
                ))
            : ImageAssetCustom(
                imagePath: Assets.images.avatarLogo.path,
                size: size,
                sizeBaseOnWidth: true,
                boxFit: BoxFit.fill,
              ));
  }
}
