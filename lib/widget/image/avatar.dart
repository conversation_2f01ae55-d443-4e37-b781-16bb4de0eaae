import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/widget/image/cache_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class Avatar extends StatelessWidget {
  const Avatar(this.url, {super.key, required this.size, this.name, this.isFamily = false, this.radius});

  final String? url, name;
  final double size;
  final double? radius;
  final bool isFamily;

  @override
  Widget build(BuildContext context) {
    if (url?.isEmpty ?? true) return _errorWidget();

    return _buildWrap(CacheImage(
      imageUrl: url ?? '',
      width: size,
      height: size,
      boxFit: BoxFit.cover,
      defaultImage: _errorWidget(),
    ));
  }

  Widget _buildWrap(Widget child) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(radius ?? size),
      child: ColoredBox(color: const Color(0xffFFE9EC), child: child),
    );
  }

  Widget _errorWidget() {
    if (isFamily)
      return _buildWrap(Image.asset(Assets.images.avatarFamily.path, width: size, height: size, fit: BoxFit.cover));

    return _buildWrap(SizedBox(
      width: size,
      height: size,
      child: Center(
        child: _getAvatarName().isNotEmpty
            ? Text(
                _getAvatarName(),
                style: TextStyle(
                  fontFamily: 'DM Sans',
                  fontSize: 0.333333333 * size,
                  height: 1.5,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )
            : SvgPicture.asset(Assets.images.avatarDefault.path, width: size * 0.75),
      ),
    ));
  }

  _getAvatarName(){
    if (name == null) {
      return '';
    }
    var names = name!.split(' ');
    names.removeWhere((element) => element.isEmpty);
    if(names.length >= 2) {
      return '${names[0][0].toUpperCase()}${names[1][0].toUpperCase()}';
    } else {
      return name![0].toUpperCase();
    }

  }
}
