import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';

class AppBarCustom extends StatefulWidget {
  final Function? onBack;
  final String? title;
  final List<Widget>? actions;
  final bool? showBack;
  final bool? centerTitle;
  final double? height;
  final bool? hasDivider;
  final TextStyle? titleStyle;
  final Color? backgroundColor;
  final Color? backColor;
  final EdgeInsets? padding;
  final bool isBackIcon;
  final Widget? backIcon;
  final Widget? titleView;
  final EdgeInsets? viewPadding;

  const AppBarCustom({
    Key? key,
    this.onBack,
    this.title,
    this.padding,
    this.actions,
    this.viewPadding,
    this.showBack = true,
    this.centerTitle = true,
    this.height,
    this.hasDivider = false,
    this.backgroundColor,
    this.titleStyle,
    this.backColor,
    this.isBackIcon = true,
    this.backIcon,
    this.titleView,
  }) : super(key: key);

  @override
  _AppBarCustomState createState() => _AppBarCustomState();
}

class _AppBarCustomState extends State<AppBarCustom> {
  back() {
    if (widget.onBack != null) {
      widget.onBack!();
    } else {
      context.maybePop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: widget.backgroundColor,
      width: double.infinity,
      padding: widget.viewPadding ?? padding(horizontal: 16, vertical: 8),
      child: Stack(alignment: Alignment.centerLeft, children: [
        if (widget.showBack ?? true) ...[
          if (widget.backIcon != null)
            widget.backIcon ?? const SizedBox()
          else if (widget.isBackIcon)
            GestureDetector(
              onTap: back,
              child: ButtonIcon(Assets.icons.icArrowLeft.path, context.maybePop,
                  bg: appTheme.backgroundV2),
            )
          else
            CircleItem(
                onTap: back,
                padding: padding(all: 6),
                backgroundColor: appTheme.blackColor.withOpacity(.16),
                child: Icon(Icons.close, color: appTheme.whiteText, size: 20)),
        ],
        Padding(
          padding: (widget.showBack ?? true) ? widget.padding ?? padding(horizontal: 24) : padding(),
          child: Center(
            child: widget.titleView ??
                Text(
                  widget.title ?? '',
                  style: widget.titleStyle ?? AppStyle.medium16(),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  textAlign: (widget.centerTitle ?? false) ? TextAlign.center : TextAlign.left,
                ),
          ),
        ),
        if ((widget.actions ?? []).isNotEmpty) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: widget.actions ?? [],
          )
        ]
      ]),
    );
  }
}

class CustomAppBar2 extends AppBarCustom implements PreferredSizeWidget {
  const CustomAppBar2({
    Key? key,
    Function? onBack,
    String? title,
    EdgeInsets? padding,
    List<Widget>? actions,
    EdgeInsets? viewPadding,
    bool showBack = true,
    bool centerTitle = true,
    double? height,
    bool hasDivider = false,
    Color backgroundColor = Colors.white,
    TextStyle? titleStyle,
    Color? backColor,
    bool isBackIcon = true,
    Widget? backIcon,
    Widget? titleView,
  }) : super(
          key: key,
          onBack: onBack,
          title: title,
          padding: padding,
          actions: actions,
          viewPadding: viewPadding,
          showBack: showBack,
          centerTitle: centerTitle,
          height: height,
          hasDivider: hasDivider,
          backgroundColor: backgroundColor,
          titleStyle: titleStyle,
          backColor: backColor,
          isBackIcon: isBackIcon,
          backIcon: backIcon,
          titleView: titleView,
        );

  @override
  Size get preferredSize => Size.fromHeight(height ?? 48.w2);

  @override
  _CustomAppBar2State createState() => _CustomAppBar2State();
}

class _CustomAppBar2State extends _AppBarCustomState {
  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 5.0), // Add horizontal padding here
        child: Container(
          height: widget.height ?? 48.w2,
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(48.w2),
          ),
          width: double.infinity,
          padding: widget.viewPadding ?? padding(horizontal: 4),
          child: Stack(alignment: Alignment.centerLeft, children: [
            if (widget.showBack ?? true) ...[
              if (widget.backIcon != null)
                widget.backIcon ?? const SizedBox()
              else if (widget.isBackIcon)
                CircleItem(
                  onTap: back,
                  backgroundColor: appTheme.backgroundV2,
                  child: ButtonIcon(
                      Assets.icons.icArrowLeft.path, back,
                      bg: appTheme.backgroundV2)
                )
              else
                CircleItem(
                    onTap: back,
                    padding: padding(all: 6),
                    backgroundColor: appTheme.blackColor.withOpacity(.16),
                    child: Icon(Icons.close, color: appTheme.whiteText, size: 20)),
            ],
            Padding(
              padding: (widget.showBack ?? true) ? widget.padding ?? padding(horizontal: 24) : padding(),
              child: Center(
                child: widget.titleView ??
                    Text(
                      widget.title ?? '',
                      style: widget.titleStyle ?? AppStyle.medium16(),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      textAlign: (widget.centerTitle ?? false) ? TextAlign.center : TextAlign.left,
                    ),
              ),
            ),
            if ((widget.actions ?? []).isNotEmpty) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: widget.actions ?? [],
              )
            ]
          ]),
        ));
  }
}
