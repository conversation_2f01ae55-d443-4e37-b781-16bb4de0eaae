import 'package:auto_route/auto_route.dart';
import 'package:dartx/dartx.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/extension/int_ext.dart';
import 'package:family_app/widget/custom_page_view.dart';
import 'package:flutter/material.dart';

class DatePickerBottomSheet extends StatefulWidget {
  const DatePickerBottomSheet({
    super.key,
    this.initialDate,
    this.title,
    this.onSelected,
  });

  final String? title;
  final DateTime? initialDate;
  final Function(DateTime)? onSelected;

  static show(
    BuildContext context, {
    DateTime? initialDate,
    String? title,
    Function(DateTime)? onSelected,
  }) {
    return BottomSheetUtils.showHeight(
      context,
      child: DatePickerBottomSheet(
        initialDate: initialDate,
        title: title,
        onSelected: onSelected,
      ),
    );
  }

  @override
  State<DatePickerBottomSheet> createState() => _DatePickerBottomSheetState();
}

class _DatePickerBottomSheetState extends State<DatePickerBottomSheet> {
  late final currentDate = ValueNotifier<DateTime>(widget.initialDate ?? DateTime.now());
  late final currentMonth = ValueNotifier<DateTime>(widget.initialDate ?? DateTime.now());
  final currentPage = ValueNotifier(0);
  late final timeType = ValueNotifier<String>(TimeType.am);
  final currentTimeHour = ValueNotifier<TimeOfDay>(TimeOfDay.now());

  final startDate = DateTime.now();

  @override
  void dispose() {
    currentDate.dispose();
    currentPage.dispose();
    currentMonth.dispose();
    timeType.dispose();
    currentTimeHour.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    if (widget.initialDate != null) {
      currentTimeHour.value = TimeOfDay.fromDateTime(widget.initialDate!);
      if (widget.initialDate!.hour > 12) {
        currentTimeHour.value = currentTimeHour.value.replacing(hour: currentTimeHour.value.hour - 12);
        timeType.value = TimeType.pm;
      } else {
        timeType.value = TimeType.am;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: padding(horizontal: 13, top: 17, bottom: 25),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(onTap: context.maybePop, child: const Icon(Icons.close, size: 22)),
              Text(widget.title ?? 'title', style: AppStyle.medium17()),
              GestureDetector(
                onTap: () {
                  final hour =
                      timeType.value == TimeType.pm ? currentTimeHour.value.hour + 12 : currentTimeHour.value.hour;
                  widget.onSelected
                      ?.call(currentDate.value.copyWith(hour: hour, minute: currentTimeHour.value.minute, second: 0));
                  context.maybePop();
                },
                child: Text(LocaleKeys.save.tr(), style: AppStyle.medium14(color: appTheme.primaryColor)),
              ),
            ],
          ),
        ),
        ValueListenableBuilder(
          valueListenable: currentMonth,
          builder: (_, date, __) {
            return Padding(
              padding: padding(left: 18, bottom: 14),
              child: Text(date.MMMM_YYYY, style: AppStyle.medium20()),
            );
          },
        ),
        _buildWeekDayHeader(),
        _buildCalendar(),
        Divider(color: appTheme.borderColor),
        Padding(
          padding: padding(vertical: 9, left: 18, right: 16),
          child: Row(
            children: [
              Text(LocaleKeys.time.tr(), style: AppStyle.regular17()),
              const Spacer(),
              ValueListenableBuilder(
                valueListenable: currentTimeHour,
                builder: (context, time, child) => GestureDetector(
                  onTap: () async {
                    final currentTime = timeType.value == TimeType.pm
                        ? time.replacing(hour: time.hour + 12)
                        : time.replacing(hour: time.hour);
                    final result = await showTimePicker(
                      context: context,
                      initialTime: currentTime,
                      initialEntryMode: TimePickerEntryMode.dialOnly,
                      builder: (context, child) {
                        return MediaQuery(
                          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
                          child: child!,
                        );
                      },
                    );
                    if (result != null) {
                      currentTimeHour.value = result;
                      if (result.hour > 12) {
                        timeType.value = TimeType.pm;
                        currentTimeHour.value = currentTimeHour.value.replacing(hour: currentTimeHour.value.hour - 12);
                      } else {
                        timeType.value = TimeType.am;
                      }
                    }
                  },
                  child: _buildBackground(
                    viewPadding: padding(vertical: 4, horizontal: 8),
                    child: Text('${time.hour.toTimeFormat}:${time.minute.toTimeFormat}', style: AppStyle.regular22()),
                  ),
                ),
              ),
              const SizedBox(width: 6),
              _buildBackground(
                viewPadding: padding(all: 2),
                child: ValueListenableBuilder(
                  valueListenable: timeType,
                  builder: (context, type, _) => Row(
                    children: [
                      _buildAMandPMView(TimeType.am, type),
                      _buildAMandPMView(TimeType.pm, type),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildWeekDayHeader() {
    const weekdays = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];
    return Container(
      padding: padding(vertical: 7),
      decoration: BoxDecoration(color: appTheme.whiteText, boxShadow: [
        BoxShadow(
          color: const Color(0xFF7D7E80).withOpacity(.16),
          blurRadius: 10,
          offset: const Offset(0, 2),
        )
      ]),
      child: Row(
        children: List.generate(
          weekdays.length,
          (index) => SizedBox(
            width: MediaQuery.of(context).size.width / 7,
            child: Center(
              child: Text(weekdays[index], style: AppStyle.regular12()),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCalendar() {
    return SizedBox(
      width: double.infinity,
      child: ValueListenableBuilder(
        valueListenable: currentMonth,
        builder: (context, month, child) => ValueListenableBuilder(
          valueListenable: currentDate,
          builder: (_, date, __) {
            return CustomPageView(
              onPageChanged: (index) {
                currentMonth.value = startDate.nextMonth(index);
              },
              viewBuilder: (index) => Stack(
                alignment: Alignment.center,
                children: [
                  Text(month.month.toString(), style: AppStyle.normal160(color: appTheme.monthColor)),
                  _buildCalendarByMonth(month, date)
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCalendarByMonth(DateTime month, DateTime current) {
    final start = month.firstDayOfMonth;
    final end = month.lastDayOfMonth;
    final rows = <Widget>[];
    var days = <Widget>[];
    for (var i = 1; i <= end.day; i++) {
      final day = start.add(Duration(days: i - 1));
      final dayView = _buildDayView(dateTime: day, current: current);
      final weekDay = day.weekday;
      if (weekDay == 7) {
        final remainLength = 6 - days.length;
        if (remainLength > 0) {
          days.insertAll(0, List.generate(remainLength, (i) => _buildDayView()));
        }
        // days.insert(0, dayView);
        days.add(dayView);
        rows.add(Row(children: days));
        days = [];
      } else {
        // if (days.length < weekDay - 1) {
        //   days.insertAll(0, List.generate(weekDay - 1, (i) => _buildDayView()));
        //   days.add(dayView);
        // } else {
        days.add(dayView);
        // }
      }
    }
    if (days.isNotEmpty) {
      // final remainLength = 6 - days.length;
      // if (remainLength > 0) {
      //   days.insertAll(0, List.generate(remainLength - 1, (i) => _buildDayView()));
      // }
      // if (days.length == 7) {
      //   final last = days.removeLast();
      //   days.insert(0, last);
      // }
      rows.add(Row(children: days));
    }
    return Column(children: rows);
  }

  Widget _buildDayView({DateTime? dateTime, DateTime? current}) {
    final widthView = MediaQuery.of(context).size.width / 7;
    final isSame = dateTime != null && current != null && dateTime.isSameDay(current);
    return GestureDetector(
      onTap: dateTime != null ? () => currentDate.value = dateTime : null,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: widthView,
        height: widthView,
        decoration: isSame ? BoxDecoration(color: appTheme.primaryColor, borderRadius: BorderRadius.circular(4)) : null,
        child: Center(
          child: Text(
            dateTime == null ? '' : dateTime.day.toString(),
            style: isSame ? AppStyle.regular16(color: appTheme.whiteText) : AppStyle.normal14(),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildBackground({EdgeInsets? viewPadding, required Widget child}) {
    return Container(
      padding: viewPadding ?? padding(all: 2),
      decoration: BoxDecoration(
        color: appTheme.gray767680.withOpacity(.16),
        borderRadius: BorderRadius.circular(6),
      ),
      child: child,
    );
  }

  Widget _buildAMandPMView(String time, String currentType) {
    final isCurrentType = time == currentType;
    return GestureDetector(
      onTap: () {
        timeType.value = time;
        // if (time == TimeType.am) {
        //   currentTimeHour.value = currentTimeHour.value.replacing(hour: currentTimeHour.value.hour - 12);
        // } else {
        //   currentTimeHour.value = currentTimeHour.value.replacing(hour: currentTimeHour.value.hour + 12);
        // }
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: padding(vertical: 7, horizontal: 14.89),
        decoration: isCurrentType
            ? BoxDecoration(
                color: appTheme.whiteText,
                borderRadius: BorderRadius.circular(6.93),
                boxShadow: [
                  BoxShadow(color: Colors.black.withOpacity(.12), offset: const Offset(0, 3), blurRadius: 8),
                  BoxShadow(color: Colors.black.withOpacity(.04), offset: const Offset(0, 3), blurRadius: 1),
                ],
              )
            : const BoxDecoration(),
        child: Text(time, style: isCurrentType ? AppStyle.medium13(height: 1.8) : AppStyle.normal13()),
      ),
    );
  }
}
