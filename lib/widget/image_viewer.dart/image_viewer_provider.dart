import 'package:family_app/widget/custom_easy_image_viewer/easy_image_provider.dart';
import 'package:flutter/material.dart';

class ImageViewerProvider extends EasyImageProvider {
  final List<String> imageUrls;
  @override
  final int initialIndex;

  ImageViewerProvider({required this.imageUrls, this.initialIndex = 0}) : super();

  @override
  ImageProvider<Object> imageBuilder(BuildContext context, int index) {
    return NetworkImage(imageUrls[index]);
  }

  @override
  int get imageCount => imageUrls.length;
}
