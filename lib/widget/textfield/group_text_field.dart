import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/widget/textfield/animation_error.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:family_app/widget/textfield/title_text_field_v2.dart';
import 'package:flutter/material.dart';

class GroupTextField extends StatefulWidget {
  final TextFieldHandler fieldNodeFirst, fieldNodeSecond;
  final bool isValidate, obscureTextFirst, obscureTextSecond;
  final Widget? suffixIconFirst, suffixIconSecond;

  const GroupTextField({
    required this.fieldNodeFirst,
    required this.fieldNodeSecond,
    required this.isValidate,
    this.obscureTextFirst = false,
    this.obscureTextSecond = false,
    this.suffixIconFirst,
    this.suffixIconSecond,
    super.key,
  });

  @override
  State<GroupTextField> createState() => _GroupTextFieldState();
}

class _GroupTextFieldState extends State<GroupTextField> {
  final _focus = ValueNotifier(0);
  final _key = GlobalKey();
  final _keyFirst = GlobalKey();
  final _keySecond = GlobalKey();
  double? _size;

  @override
  void initState() {
    widget.fieldNodeFirst.node.addListener(_listener);
    widget.fieldNodeSecond.node.addListener(_listener);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final renderBox = _key.currentContext?.findRenderObject() as RenderBox;

      setState(() => _size = renderBox.size.height);
    });

    super.initState();
  }

  @override
  void dispose() {
    widget.fieldNodeFirst.node.removeListener(_listener);
    widget.fieldNodeSecond.node.removeListener(_listener);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final radiusTop = BorderRadius.vertical(top: Radius.circular(8.w2));
    final radiusBottom = BorderRadius.vertical(bottom: Radius.circular(8.w2));

    if (_size == null) return Column(children: [TitleTextFieldV2(key: _key), const TitleTextFieldV2()]);

    final first = Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: TitleTextFieldV2(
        key: _keyFirst,
        fieldNode: widget.fieldNodeFirst,
        validatedForm: widget.isValidate,
        radius: radiusTop,
        suffixIcon: widget.suffixIconFirst,
        obscureText: widget.obscureTextFirst,
        showError: false,
        // showBorder: false,
      ),
    );
    final second = Positioned(
      top: _size! - 1.w2,
      left: 0,
      right: 0,
      child: TitleTextFieldV2(
        key: _keySecond,
        fieldNode: widget.fieldNodeSecond,
        validatedForm: widget.isValidate,
        radius: radiusBottom,
        suffixIcon: widget.suffixIconSecond,
        obscureText: widget.obscureTextSecond,
        showError: false,
        // showBorder: false,
      ),
    );

    return Stack(clipBehavior: Clip.none, children: [
      Column(children: [
        SizedBox(height: (_size! * 2) - 1.w2, width: double.infinity),
        _buildValueListenerView(
          widget.fieldNodeFirst,
          (errorFirst) => _buildValueListenerView(widget.fieldNodeSecond, (errorSecond) {
            var error = '';

            if (errorFirst != null) error += errorFirst;

            if (errorSecond != null) error += '${error.isEmpty ? '' : '\n'}$errorSecond';

            return AnimationError(error);
          }),
        ),
      ]),
      ValueListenableBuilder(
        valueListenable: _focus,
        child: second,
        builder: (context, focus, child) => focus == 1 ? child! : const SizedBox.shrink(),
      ),
      ValueListenableBuilder(
        valueListenable: _focus,
        child: first,
        builder: (context, focus, child) => focus == 1 || focus == 0 ? child! : const SizedBox.shrink(),
      ),
      ValueListenableBuilder(
        valueListenable: _focus,
        child: first,
        builder: (context, focus, child) => focus == 2 ? child! : const SizedBox.shrink(),
      ),
      ValueListenableBuilder(
        valueListenable: _focus,
        child: second,
        builder: (context, focus, child) => focus == 2 || focus == 0 ? child! : const SizedBox.shrink(),
      ),
    ]);
  }

  Widget _buildValueListenerView(TextFieldHandler fieldNode, Widget Function(String? error) viewBuilder) {
    return ValueListenableBuilder<bool>(
      valueListenable: fieldNode.emptyError,
      builder: (context, isEmpty, child) => ValueListenableBuilder<bool>(
        valueListenable: fieldNode.isValidNotifier,
        builder: (context, isValid, child) => ValueListenableBuilder(
            valueListenable: fieldNode.customErrorNotifier,
            builder: (context, customError, child) {
              final hasError = ((!isValid || isEmpty) && widget.isValidate) || customError.isNotEmpty;
              final errorText = customError.isNotEmpty
                  ? customError
                  : isEmpty
                      ? LocaleKeys.empty_error.tr(args: [fieldNode.title])
                      : fieldNode.errorText?.call(fieldNode.textCtrl.text);

              return viewBuilder((errorText?.isNotEmpty ?? false) && hasError ? errorText : null);
            }),
      ),
    );
  }

  void _listener() {
    setState(() => _focus.value = widget.fieldNodeSecond.node.hasFocus
        ? 2
        : widget.fieldNodeFirst.node.hasFocus
            ? 1
            : 0);
  }
}
