import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class ApiManager {
  static const String baseUrl = 'https://vrelay-vn1.5gencare.com';

  Future<Map<String, dynamic>> login(String email, String password) async {
    final response = await http.post(
      Uri.parse('$baseUrl/v2/login'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email, 'password': password}),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to login');
    }
  }

  /* 
  API: GET {{BASE_URL}}v1/activity/all?ordering=desc&order_by=created_at
  HEADER: Authentication Bearer Token @token
  
  Response: 
{
    "code": 200,
    "data": [
        {
            "uuid": "d8dc0fed-355d-4640-a550-9d151411e194",
            "status": 0,
            "created_at": "2024-12-02 15:04:45",
            "updated_at": "2024-12-02 15:04:45",
            "created_by": "f610c8f2-49f2-42d6-9680-98164042bb92",
            "family_id": "f610c8f2-49f2-42d6-9680-98164042bb92",
            "activity_type": "5c4884e8-a8fa-4018-a787-83962e8893f0",
            "name": "title",
            "description": "des",
            "from_date": "2024-12-02T15:04:00.770170Z",
            "to_date": "2024-12-02T15:04:00.770170Z",
            "color": "#ED57E4",
            "caption": "",
            "included_members": [],
            "included_events": null,
            "included_lists": null,
            "type": {
                "uuid": "5c4884e8-a8fa-4018-a787-83962e8893f0",
                "status": 1,
                "name": "Trip",
                "image": "",
                "color": "#dddd"
            },
            "lat": "",
            "lon": "",
            "notification_status": "",
            "notification_time": ""
        },
        {
            "uuid": "2c01c112-c0ec-4a01-8e67-58926aa7f534",
            "status": 0,
            "created_at": "2024-12-02 15:31:50",
            "updated_at": "2024-12-02 15:31:50",
            "created_by": "f610c8f2-49f2-42d6-9680-98164042bb92",
            "family_id": "f610c8f2-49f2-42d6-9680-98164042bb92",
            "activity_type": "5c4884e8-a8fa-4018-a787-83962e8893f0",
            "name": "activity title",
            "description": "activity des",
            "from_date": "2024-12-02T15:31:00.600592Z",
            "to_date": "2024-12-02T15:31:00.600592Z",
            "color": "#FF6458",
            "caption": "",
            "included_members": [],
            "included_events": null,
            "included_lists": null,
            "type": {
                "uuid": "5c4884e8-a8fa-4018-a787-83962e8893f0",
                "status": 1,
                "name": "Trip",
                "image": "",
                "color": "#dddd"
            },
            "lat": "",
            "lon": "",
            "notification_status": "",
            "notification_time": ""
        },
        {
            "uuid": "ca7b6e26-a055-4c4f-af61-daea549b7068",
            "status": 0,
            "created_at": "2024-12-03 16:55:42",
            "updated_at": "2024-12-03 16:55:42",
            "created_by": "f610c8f2-49f2-42d6-9680-98164042bb92",
            "family_id": "f610c8f2-49f2-42d6-9680-98164042bb92",
            "activity_type": "5c4884e8-a8fa-4018-a787-83962e8893f0",
            "name": "sự kiện activity",
            "description": "chỉ tiết activity",
            "from_date": "2024-12-03T16:55:00.335520Z",
            "to_date": "2024-12-14T16:55:00.000Z",
            "color": "#5BA0FA",
            "caption": "",
            "included_members": [],
            "included_events": [
                {
                    "uuid": "4e882911-2be4-4ee6-a9b3-6615d43da3b7",
                    "status": 0,
                    "created_by": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "activity_id": "d38be1d3-ec24-42d9-9fa3-b1a4eb338156",
                    "list_id": "",
                    "family_id": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "name": "title",
                    "description": "des",
                    "from_date_utc": "2024-12-07T15:20:00.000Z",
                    "to_date_utc": "2024-12-02T15:20:00.021566Z",
                    "color": "#5BA0FA",
                    "notification_status": "",
                    "notification_time_utc": "",
                    "role": "",
                    "time_zone": ""
                },
                {
                    "uuid": "3c40531b-36c8-45b0-8374-ee76c8c834e1",
                    "status": 0,
                    "created_by": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "activity_id": "",
                    "list_id": "",
                    "family_id": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "name": "title",
                    "description": "des",
                    "from_date_utc": "2024-12-02T15:03:00.789715Z",
                    "to_date_utc": "2024-12-06T15:03:00.000Z",
                    "color": "#5BA0FA",
                    "notification_status": "",
                    "notification_time_utc": "",
                    "role": "",
                    "time_zone": ""
                },
                {
                    "uuid": "2819ae38-82bd-4c91-8257-935aa9dacd96",
                    "status": 0,
                    "created_by": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "activity_id": "",
                    "list_id": "",
                    "family_id": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "name": "tutle",
                    "description": "des",
                    "from_date_utc": "2024-12-06T15:05:00.000Z",
                    "to_date_utc": "2024-12-02T15:05:00.004842Z",
                    "color": "#5BA0FA",
                    "notification_status": "",
                    "notification_time_utc": "",
                    "role": "",
                    "time_zone": ""
                },
                {
                    "uuid": "abbabc8e-8708-4366-b215-3a015aa6d1cd",
                    "status": 0,
                    "created_by": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "activity_id": "",
                    "list_id": "",
                    "family_id": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "name": "title",
                    "description": "des",
                    "from_date_utc": "2024-12-06T15:03:00.000Z",
                    "to_date_utc": "2024-12-02T15:03:00.789715Z",
                    "color": "#5BA0FA",
                    "notification_status": "",
                    "notification_time_utc": "",
                    "role": "",
                    "time_zone": ""
                },
                {
                    "uuid": "68863c6a-63ad-4baf-9235-fdb40b9be6ae",
                    "status": 0,
                    "created_by": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "activity_id": "",
                    "list_id": "",
                    "family_id": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "name": "titoe",
                    "description": "des",
                    "from_date_utc": "2024-12-02T15:02:00.814939Z",
                    "to_date_utc": "2024-12-02T15:02:00.263502Z",
                    "color": "#FD814E",
                    "notification_status": "",
                    "notification_time_utc": "",
                    "role": "",
                    "time_zone": ""
                },
                {
                    "uuid": "306d70df-41b2-4374-abca-52bb8a206b4d",
                    "status": 0,
                    "created_by": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "activity_id": "",
                    "list_id": "",
                    "family_id": "f610c8f2-49f2-42d6-9680-98164042bb92",
                    "name": "event",
                    "description": "chỉ tiết event",
                    "from_date_utc": "",
                    "to_date_utc": "",
                    "color": "#FF6458",
                    "notification_status": "",
                    "notification_time_utc": "",
                    "role": "",
                    "time_zone": ""
                }
            ],
            "included_lists": null,
            "type": {
                "uuid": "5c4884e8-a8fa-4018-a787-83962e8893f0",
                "status": 1,
                "name": "Trip",
                "image": "",
                "color": "#dddd"
            },
            "lat": "",
            "lon": "",
            "notification_status": "",
            "notification_time": ""
        },
        ....
    ],
    "message": "success",
    "request_id": "b315c642-f6ac-4434-9a9a-0e4329c1beb1"
}
  
  */
  Future<Map<String, dynamic>> getUserActivity(String token) async {
    final response = await http.get(
      Uri.parse(
          '$baseUrl/v1/activity/all?ordering=desc&order_by=created_at&limit=10'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    print("Response: ${response.body}");

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to fetch user activities');
    }
  }

  /*
  API: POST {{BASE_URL}}v1/activity
  HEADER: Authentication Bearer Token @token
  {
    "name": "trip to Paris",
    "from_date": "2024-12-30",
    "to_date": "2024-12-30",
    "color": "#133483",
    "caption": "",
    "description": "test create plan",
    "included_members": [],
    "included_events": [],
    "included_lists": [],
    "activity_type": "5c4884e8-a8fa-4018-a787-83962e8893f0"
    "itinerary" : [ 
    {
 

    ]
}
  */

  Future<Map<String, dynamic>> createActivity(
      String token, Map<String, dynamic> activity) async {
    debugPrint("query: $baseUrl/v1/activity");
    debugPrint('createActivity token: $token, data: $activity');
    final response = await http.post(
      Uri.parse('$baseUrl/v1/activity'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(activity),
    );

    debugPrint("status code: ${response.statusCode}");
    fullyLog("Create activity: ${jsonEncode(activity)}");
    debugPrint("response: ${response.body}");

    if (response.statusCode == 200) {
      print('output: ${jsonDecode(response.body)}');
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to create activity');
    }
  }

  /* Update Activity 
  API: PUT {{BASE_URL}}v1/activity/{uuid}
  HEADER: Authentication Bearer Token @token
  {
    "name": "trip to Paris",
    "from_date": "2024-12-30",
    "to_date": "2024-12-30",
    "color": "#133483",
    "caption": "",
    "description": "test create plan",
    "included_members": [],
    "included_events": [],
    "included_lists": [],
    "activity_type": "5c4884e8-a8fa-4018-a787-83962e8893f0"
    "itinerary" : [ 
    {
 

    }

  */

  Future<Map<String, dynamic>> updateActivity(
      String token, String uuid, Map<String, dynamic> activity) async {
    final response = await http.put(
      Uri.parse('$baseUrl/v1/activity/$uuid'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(activity),
    );
    print("Update query: $baseUrl/v1/activity/$uuid");
    fullyLog("Update body: ${jsonEncode(activity)}");

    print("response: ${response.body}");

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to update activity');
    }
  }

  /*
    API to get presign url for uploading image
    GET {{BASE_URL}}v1/family/storage/presign/$uuid
    HEADER: Authentication Bearer Token @token

    Response:
    {
    "code": 200,
    "data": {
        "url": "https://vrelay-vn1.5gencare.com/v1/family/storage/presign/5c4884e8-a8fa-4018-a787-83962e8893f0",
        "uuid": "5c4884e8-a8fa-4018-a787-83962e8893f0"
        }
    "message": "success",
    "request_id": "b315c642-f6ac-4434-9a9a-0e4329c1beb1"
    }

  */
  Future<Map<String, dynamic>> getPresignUrl(
      String token, String family_uuid) async {
    final response = await http.get(
      Uri.parse('$baseUrl/v1/family/storage/presign/$family_uuid'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get presign url');
    }
  }

  void fullyLog(String message) {
    // Log the message to the console fully
    const int chunkSize = 800;
    for (int i = 0; i < message.length; i += chunkSize) {
      debugPrint(message.substring(
          i, i + chunkSize > message.length ? message.length : i + chunkSize));
    }
  }
}
