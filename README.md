# family_app

# Config source

Run the following command in your terminal of this source
dart run easy_localization:generate -O lib/config/lang -f keys -o locale_keys.g.dart --source-dir ./assets/lang
dart run build_runner build


# iOS build

need to run "pod update" once

# Get an env file that store API Keys and other project level secret from the admin

Copy it to the project flutter root folder and name it ".env", like below
[familylink-app]# ls -la
drwxr-xr-x@ 21 <USER> <GROUP> 672 Dec 17 11:32 .
drwxr-xr-x 23 <USER> <GROUP> 736 Dec 12 10:07 ..
-rw-r--r--@ 1 <USER> <GROUP> 165 Dec 17 11:15 .env <<<<<
rwxr-xr-x@ 15 macmini staff 480 Dec 16 10:42 .git
-rw-r--r--@ 1 <USER> <GROUP> 1048 Dec 5 11:56 .gitignore
....

# Free Syncfusion® Community license key
Ngo9BigBOggjHTQxAR8/V1NNaF5cXmBCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWXpfeXZXRWZcWEV1XUNWYUA=


# Config ENV for AMADEUS
add below key to your .env file
```agsl
AMADEUS_API_KEY=********************************
AMADEUS_API_SECRET=lWOkwU3GAZVNDQ86
AMADEUS_API_URL=https://test.api.amadeus.com
```
